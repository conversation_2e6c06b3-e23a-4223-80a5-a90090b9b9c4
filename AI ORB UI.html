<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>AI Voice Assistant Interface</title>
  <style>
    body {
      margin: 0;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      background: ;
      flex-direction: column;
    }
    .orb {
      position: relative;
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background: radial-gradient(circle at 30% 30%, #ffffff, #4a90e2);
      box-shadow: 
        inset -10px -10px 20px rgba(0,0,0,0.2),
        inset 10px 10px 20px rgba(255,255,255,0.5),
        0 0 20px 5px #4a90e2;
      animation: pulsate 3s infinite;
      cursor: pointer;
      transition: all 0.5s ease-in-out;
    }
    .orb::before {
      content: "";
      position: absolute;
      top: 20%;
      left: 25%;
      width: 30%;
      height: 30%;
      background: rgba(255, 255, 255, 0.7);
      border-radius: 50%;
      filter: blur(4px);
    }
    .orb:hover {
      transform: scale(1.5);
      box-shadow: 
        inset -10px -10px 20px rgba(0,0,0,0.2),
        inset 10px 10px 20px rgba(255,255,255,0.5),
        0 0 40px 10px #4a90e2;
    }
    .orb.recording {
      background: radial-gradient(circle at 30% 30%, #ffffff, #ff4444);
      box-shadow: 
        inset -10px -10px 20px rgba(0,0,0,0.2),
        inset 10px 10px 20px rgba(255,255,255,0.5),
        0 0 40px 10px #ff4444;
      animation: record-pulse 1s infinite;
    }
    .orb.responding {
      background: radial-gradient(circle at 30% 30%, #ffffff, #44ff44);
      box-shadow: 
        inset -10px -10px 20px rgba(0,0,0,0.2),
        inset 10px 10px 20px rgba(255,255,255,0.5),
        0 0 40px 10px #44ff44;
      animation: respond-pulse 2s infinite;
    }
    .status-text {
      color: #fff;
      font-family: Arial, sans-serif;
      margin-top: 20px;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    .orb:hover + .status-text,
    .recording + .status-text,
    .responding + .status-text {
      opacity: 1;
    }
    @keyframes pulsate {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }
    @keyframes record-pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
    }
    @keyframes respond-pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.08); }
      100% { transform: scale(1); }
    }
  </style>
</head>
<body>
  <div class="orb"></div>
  <div class="status-text">Click to speak</div>
  
  <script>
    let mediaRecorder;
    let audioChunks = [];
    const orb = document.querySelector('.orb');
    const statusText = document.querySelector('.status-text');
    let isRecording = false;
    let audioContext;
    let analyser;
    let microphone;

    // Initialize text-to-speech
    const synth = window.speechSynthesis;

    // API endpoint (replace with your actual API endpoint)
    const API_ENDPOINT = 'https://your-ai-service-endpoint.com/process-audio';

    // Request microphone access when page loads
    window.addEventListener('load', async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            setupAudioAnalyser(stream);
            setupMediaRecorder(stream);
            statusText.textContent = 'Click to speak';
        } catch (err) {
            console.error('Error accessing microphone:', err);
            statusText.textContent = 'Microphone access denied';
            orb.style.opacity = '0.5';
            orb.style.cursor = 'not-allowed';
        }
    });

    function setupAudioAnalyser(stream) {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        analyser = audioContext.createAnalyser();
        microphone = audioContext.createMediaStreamSource(stream);
        
        analyser.fftSize = 256;
        analyser.smoothingTimeConstant = 0.8;
        microphone.connect(analyser);
        
        const dataArray = new Uint8Array(analyser.frequencyBinCount);
        
        function updateOrb() {
            if (isRecording) {
                analyser.getByteFrequencyData(dataArray);
                
                // Calculate the average frequency intensity
                const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
                
                // Scale the orb based on the audio intensity
                const scale = 1 + (average / 512); // Adjust divisor to control sensitivity
                orb.style.transform = `scale(${scale})`;
                
                // Adjust the glow intensity
                const glowIntensity = Math.min(20 + (average / 8), 40);
                orb.style.boxShadow = `
                    inset -10px -10px 20px rgba(0,0,0,0.2),
                    inset 10px 10px 20px rgba(255,255,255,0.5),
                    0 0 ${glowIntensity}px ${glowIntensity/4}px #ff4444
                `;
            }
            requestAnimationFrame(updateOrb);
        }
        
        updateOrb();
    }

    function setupMediaRecorder(stream) {
        mediaRecorder = new MediaRecorder(stream);

        mediaRecorder.ondataavailable = (event) => {
            audioChunks.push(event.data);
        };

        mediaRecorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
            await processAudioAndGetResponse(audioBlob);
            audioChunks = [];
        };
    }

    async function processAudioAndGetResponse(audioBlob) {
        statusText.textContent = 'Processing...';
        orb.classList.add('responding');
        
        // Reset orb styles
        orb.style.transform = '';
        orb.style.boxShadow = '';

        // Test response without API call
        setTimeout(() => {
            const testResponse = "I received your audio input. This is a test response to confirm the recording is working.";
            speakResponse(testResponse);
        }, 1000);
    }

    function speakResponse(text) {
        statusText.textContent = 'AI is speaking...';
        
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.onend = () => {
            orb.classList.remove('responding');
            statusText.textContent = 'Click to speak';
        };
        
        synth.speak(utterance);
    }

    orb.addEventListener('click', () => {
        if (!mediaRecorder) {
            statusText.textContent = 'Microphone access required';
            return;
        }

        if (orb.classList.contains('responding')) {
            return;
        }

        if (!isRecording) {
            // Resume audio context if it was suspended
            if (audioContext.state === 'suspended') {
                audioContext.resume();
            }
            
            mediaRecorder.start();
            isRecording = true;
            orb.classList.add('recording');
            statusText.textContent = 'Listening...';
            
            setTimeout(() => {
                if (isRecording) {
                    mediaRecorder.stop();
                    isRecording = false;
                    orb.classList.remove('recording');
                    // Reset orb styles
                    orb.style.transform = '';
                    orb.style.boxShadow = '';
                }
            }, 5000);
        } else {
            mediaRecorder.stop();
            isRecording = false;
            orb.classList.remove('recording');
            // Reset orb styles
            orb.style.transform = '';
            orb.style.boxShadow = '';
        }
    });

    document.addEventListener('keydown', (e) => {
        if (e.code === 'Space') {
            e.preventDefault();
            orb.click();
        }
    });
</script>
</body>
</html>