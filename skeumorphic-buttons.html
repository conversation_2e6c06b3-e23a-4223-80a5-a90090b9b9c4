<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skeumorphic Buttons</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
        }

        .button-container {
            display: flex;
            gap: 20px;
            flex-direction: column;
        }

        .skeu-button {
            position: relative;
            padding: 15px 30px;
            font-size: 18px;
            border: 1px solid #ccc;
            border-radius: 8px;
            background: linear-gradient(to bottom, #ffffff 0%, #f3f3f3 100%);
            box-shadow: 
                0 1px 2px rgba(0,0,0,0.2),
                inset 0 1px 0 rgba(255,255,255,0.9);
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #333;
        }

        .skeu-button:hover {
            background: linear-gradient(to bottom, #ff5555 0%, #ff3333 100%);
            color: white;
            box-shadow: 
                0 2px 4px rgba(0,0,0,0.3),
                inset 0 1px 0 rgba(255,255,255,0.4);
        }

        .skeu-button:active {
            transform: translateY(1px);
            box-shadow: 
                0 1px 2px rgba(0,0,0,0.2),
                inset 0 1px 3px rgba(0,0,0,0.2);
        }

        .name-banner {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 30px;
            border-radius: 5px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        #rico-banner { display: none; }
        #chike-banner { display: none; }
        #ryan-banner { display: none; }

        #button1:hover ~ #rico-banner { 
            display: block;
            opacity: 1;
        }
        #button2:hover ~ #chike-banner {
            display: block;
            opacity: 1;
        }
        #button3:hover ~ #ryan-banner {
            display: block;
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="button-container">
        <a href="#" class="skeu-button" id="button1">Button 1</a>
        <a href="#" class="skeu-button" id="button2">Button 2</a>
        <a href="#" class="skeu-button" id="button3">Button 3</a>

        <div class="name-banner" id="rico-banner">Rico</div>
        <div class="name-banner" id="chike-banner">Chike</div>
        <div class="name-banner" id="ryan-banner">Ryan</div>
    </div>
</body>
</html>