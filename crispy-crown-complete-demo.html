<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H & Chicken - Complete POS System Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fef7ed',
                            100: '#fdedd3',
                            200: '#fbd6a5',
                            300: '#f8b86d',
                            400: '#f59332',
                            500: '#f37316',
                            600: '#e4570b',
                            700: '#bd420b',
                            800: '#973510',
                            900: '#7c2d12',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .neu-card {
            background: #e0e5ec;
            box-shadow: 2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff;
            transition: all 0.2s ease;
        }
        .neu-button {
            background: #e0e5ec;
            box-shadow: 2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .neu-button:active {
            box-shadow: inset 2px 2px 5px #a3b1c6, inset -2px -2px 5px #ffffff;
        }
        .hidden { display: none; }
        .tab-active {
            background: linear-gradient(135deg, #f37316, #e4570b);
            color: white;
            box-shadow: inset 1px 1px 2px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Login Screen -->
    <div id="loginScreen" class="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center p-4">
        <div class="neu-card p-8 rounded-xl max-w-md w-full">
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span class="text-white font-bold text-2xl">🍗</span>
                </div>
                <h1 class="text-2xl font-bold text-gray-900">Welcome to H & Chicken</h1>
                <p class="text-gray-600 mt-2">Authentic Fried Chicken & More</p>
            </div>

            <form onsubmit="login(event)" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input type="email" value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input type="password" value="demo123" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500" readonly>
                </div>
                <button type="submit" class="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors">
                    Sign In
                </button>
            </form>

            <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                <p class="text-sm text-blue-700">
                    <strong>Demo Account:</strong> Full access to all features including Dashboard, POS, and Product Management.
                </p>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="hidden">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">🍗</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">H & Chicken</h1>
                        <p class="text-xs text-gray-500">Authentic Fried Chicken & More</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>Shift Active</span>
                    </div>
                    <div class="relative">
                        <button class="neu-button px-4 py-2 rounded-lg text-gray-700 font-medium" onclick="showTab('pos')">
                            🛒 Cart (<span id="cartCount">0</span>)
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <div class="flex">
            <!-- Sidebar -->
            <aside class="w-64 bg-white shadow-sm h-screen">
                <nav class="p-4 space-y-2">
                    <button onclick="showTab('dashboard')" id="tab-dashboard" class="w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100 tab-active">
                        📊 Dashboard
                    </button>
                    <button onclick="showTab('pos')" id="tab-pos" class="w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        🛒 Order Entry
                    </button>
                    <button onclick="showTab('products')" id="tab-products" class="w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        📦 Menu Items
                    </button>
                    <button onclick="showTab('customers')" id="tab-customers" class="w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        👥 Customers
                    </button>
                    <button onclick="showTab('reports')" id="tab-reports" class="w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        📈 Sales Reports
                    </button>
                    <button onclick="showTab('settings')" id="tab-settings" class="w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        ⚙️ Settings
                    </button>
                </nav>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 p-6 overflow-y-auto">
                <!-- Dashboard Tab -->
                <div id="content-dashboard" class="tab-content">
                    <!-- Welcome Header -->
                    <div class="bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg p-6 text-white mb-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-bold mb-2">Welcome to H & Chicken! 🍗</h1>
                                <p class="text-primary-100">Authentic Fried Chicken & More - Serving quality since today</p>
                            </div>
                            <div class="text-right">
                                <p class="text-primary-200 text-sm">Today's Date</p>
                                <p class="text-xl font-semibold" id="currentDate"></p>
                            </div>
                        </div>
                    </div>

                    <!-- Stats Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-green-100 mr-4">
                                    <span class="text-green-600 text-xl">💰</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Today's Sales</p>
                                    <p class="text-2xl font-bold text-gray-900" id="todaySales">$0.00</p>
                                    <p class="text-xs text-green-600">↗️ +15.3% from yesterday</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-blue-100 mr-4">
                                    <span class="text-blue-600 text-xl">🛒</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Orders</p>
                                    <p class="text-2xl font-bold text-gray-900" id="todayOrders">0</p>
                                    <p class="text-xs text-gray-500">Avg. $<span id="avgOrder">0.00</span> per order</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-purple-100 mr-4">
                                    <span class="text-purple-600 text-xl">👥</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Customers</p>
                                    <p class="text-2xl font-bold text-gray-900" id="todayCustomers">0</p>
                                    <p class="text-xs text-gray-500"><span id="newCustomers">0</span> new today</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-orange-100 mr-4">
                                    <span class="text-orange-600 text-xl">🔥</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Hot Items</p>
                                    <p class="text-2xl font-bold text-gray-900">🍗</p>
                                    <p class="text-xs text-gray-500" id="hotItem">Signature Sandwich leading</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <div class="neu-card rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <button onclick="showTab('pos')" class="bg-primary-600 text-white h-20 rounded-lg flex flex-col items-center justify-center space-y-2 hover:bg-primary-700">
                                    <span class="text-xl">🛒</span>
                                    <span class="text-sm font-medium">New Order</span>
                                </button>
                                <button onclick="showTab('products')" class="neu-button h-20 rounded-lg flex flex-col items-center justify-center space-y-2">
                                    <span class="text-xl">🍗</span>
                                    <span class="text-sm font-medium">Menu Items</span>
                                </button>
                                <button onclick="showTab('customers')" class="neu-button h-20 rounded-lg flex flex-col items-center justify-center space-y-2">
                                    <span class="text-xl">👥</span>
                                    <span class="text-sm font-medium">Customers</span>
                                </button>
                                <button onclick="showTab('reports')" class="neu-button h-20 rounded-lg flex flex-col items-center justify-center space-y-2">
                                    <span class="text-xl">📈</span>
                                    <span class="text-sm font-medium">Reports</span>
                                </button>
                            </div>
                        </div>

                        <!-- Recent Orders -->
                        <div class="neu-card rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-4">Recent Orders</h3>
                            <div id="recentOrdersList" class="space-y-3">
                                <div class="text-center text-gray-500 py-8">
                                    <span class="text-4xl">🍗</span>
                                    <p class="mt-2">No orders yet today</p>
                                    <p class="text-sm">Start taking orders to see them here!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- POS Tab -->
                <div id="content-pos" class="tab-content hidden">
                    <div class="flex flex-col lg:flex-row gap-6">
                        <!-- Left Side - Menu Items -->
                        <div class="lg:w-2/3">
                            <!-- Categories -->
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold mb-4">Menu Categories</h3>
                                <div class="flex flex-wrap gap-2">
                                    <button onclick="filterProducts('all')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter active">
                                        All Items
                                    </button>
                                    <button onclick="filterProducts('signature-sandwiches')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        🍗 Signature Sandwiches
                                    </button>
                                    <button onclick="filterProducts('tender-combos')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        🍗 Tender Combos
                                    </button>
                                    <button onclick="filterProducts('fried-chicken')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        🍗 Fried Chicken
                                    </button>
                                    <button onclick="filterProducts('waffle-chicken')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        🧇 Waffle & Chicken
                                    </button>
                                    <button onclick="filterProducts('sides')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        🍟 Sides
                                    </button>
                                    <button onclick="filterProducts('beverages')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        🥤 Beverages
                                    </button>
                                    <button onclick="filterProducts('family-deals')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        👨‍👩‍👧‍👦 Family Deals
                                    </button>
                                </div>
                            </div>

                            <!-- Products Grid -->
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-4" id="productsGrid">
                                <!-- Products will be dynamically added here -->
                            </div>
                        </div>

                        <!-- Right Side - Cart -->
                        <div class="lg:w-1/3">
                            <div class="neu-card rounded-lg p-6 sticky top-6">
                                <h3 class="text-lg font-semibold mb-4">Current Order</h3>

                                <!-- Cart Items -->
                                <div id="cartItems" class="space-y-3 mb-6 max-h-96 overflow-y-auto">
                                    <div class="text-center text-gray-500 py-8">
                                        <span class="text-4xl">🛒</span>
                                        <p class="mt-2">Your cart is empty</p>
                                        <p class="text-sm">Add items from the menu</p>
                                    </div>
                                </div>

                                <!-- Cart Summary -->
                                <div class="border-t border-gray-200 pt-4 space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Subtotal</span>
                                        <span class="font-medium" id="cartSubtotal">$0.00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Tax (8%)</span>
                                        <span class="font-medium" id="cartTax">$0.00</span>
                                    </div>
                                    <div class="flex justify-between text-lg font-bold">
                                        <span>Total</span>
                                        <span id="cartTotal">$0.00</span>
                                    </div>
                                </div>

                                <!-- Checkout Button -->
                                <button id="checkoutBtn" onclick="showCheckout()" class="w-full bg-primary-600 text-white py-3 px-4 rounded-lg mt-6 font-medium hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                    Checkout
                                </button>

                                <!-- Clear Cart Button -->
                                <button onclick="clearCart()" class="w-full neu-button py-2 px-4 rounded-lg mt-3 text-sm font-medium">
                                    Clear Order
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Checkout Modal -->
                    <div id="checkoutModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
                        <div class="bg-white rounded-lg max-w-md w-full">
                            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-900">Complete Order</h2>
                                <button onclick="hideCheckout()" class="text-gray-400 hover:text-gray-600">
                                    ✕
                                </button>
                            </div>

                            <div class="p-6 space-y-6">
                                <div class="space-y-2">
                                    <p class="font-medium">Order Summary</p>
                                    <div id="checkoutItems" class="space-y-2 max-h-40 overflow-y-auto"></div>

                                    <div class="border-t border-gray-200 pt-4 space-y-2 mt-4">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Subtotal</span>
                                            <span class="font-medium" id="checkoutSubtotal">$0.00</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Tax (8%)</span>
                                            <span class="font-medium" id="checkoutTax">$0.00</span>
                                        </div>
                                        <div class="flex justify-between text-lg font-bold">
                                            <span>Total</span>
                                            <span id="checkoutTotal">$0.00</span>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <p class="font-medium mb-3">Payment Method</p>
                                    <div class="grid grid-cols-2 gap-3">
                                        <button onclick="processPayment('cash')" class="neu-button p-3 rounded-lg text-center">
                                            💵 Cash
                                        </button>
                                        <button onclick="processPayment('cashapp')" class="neu-button p-3 rounded-lg text-center">
                                            💚 CashApp
                                        </button>
                                        <button onclick="processPayment('zelle')" class="neu-button p-3 rounded-lg text-center">
                                            🏦 Zelle
                                        </button>
                                        <button onclick="processPayment('card')" class="neu-button p-3 rounded-lg text-center">
                                            💳 Card
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Success Modal -->
                    <div id="paymentSuccessModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
                        <div class="bg-white rounded-lg max-w-md w-full p-6 text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-green-600 text-2xl">✓</span>
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 mb-2">Payment Successful!</h2>
                            <p class="text-gray-600 mb-6">Order #<span id="successOrderNumber">1001</span> has been completed.</p>
                            <button onclick="closePaymentSuccess()" class="bg-primary-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors">
                                New Order
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Products Tab -->
                <div id="content-products" class="tab-content hidden">
                    <!-- Product Management Header -->
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Menu Items & Inventory</h1>
                            <p class="text-gray-600">Manage your chicken restaurant menu and track inventory</p>
                        </div>
                        <div class="flex space-x-3">
                            <button onclick="showCategoryManager()" class="neu-button px-4 py-2 rounded-lg text-gray-700 font-medium">
                                📋 Categories
                            </button>
                            <button onclick="showAddProductModal()" class="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700">
                                + Add Menu Item
                            </button>
                        </div>
                    </div>

                    <!-- Inventory Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-blue-100 mr-4">
                                    <span class="text-blue-600 text-xl">📦</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Items</p>
                                    <p class="text-2xl font-bold text-gray-900" id="totalItems">12</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-yellow-100 mr-4">
                                    <span class="text-yellow-600 text-xl">⚠️</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Low Stock</p>
                                    <p class="text-2xl font-bold text-gray-900" id="lowStockItems">3</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-red-100 mr-4">
                                    <span class="text-red-600 text-xl">📉</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Out of Stock</p>
                                    <p class="text-2xl font-bold text-gray-900" id="outOfStockItems">1</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-green-100 mr-4">
                                    <span class="text-green-600 text-xl">📈</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Inventory Value</p>
                                    <p class="text-2xl font-bold text-gray-900" id="inventoryValue">$1,247.50</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Low Stock Alert -->
                    <div id="lowStockAlert" class="neu-card rounded-lg mb-6 border-l-4 border-l-yellow-500">
                        <div class="p-6">
                            <h3 class="flex items-center text-lg font-semibold text-yellow-700 mb-4">
                                <span class="mr-2">⚠️</span>
                                Low Stock Alert (<span id="lowStockCount">3</span> items)
                            </h3>
                            <div id="lowStockItems" class="space-y-3">
                                <!-- Low stock items will be added here -->
                            </div>
                        </div>
                    </div>

                    <!-- Filters and Search -->
                    <div class="neu-card rounded-lg mb-6">
                        <div class="p-6">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                                <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
                                    <!-- Search -->
                                    <div class="relative">
                                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</span>
                                        <input
                                            type="text"
                                            id="productSearch"
                                            placeholder="Search menu items..."
                                            class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 w-64"
                                        />
                                    </div>

                                    <!-- Category Filter -->
                                    <select
                                        id="categoryFilter"
                                        class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                    >
                                        <option value="all">All Categories</option>
                                        <option value="signature-sandwiches">🍗 Signature Sandwiches</option>
                                        <option value="tender-combos">🍗 Tender Combos</option>
                                        <option value="fried-chicken">🍗 Fried Chicken</option>
                                        <option value="waffle-chicken">🧇 Waffle & Chicken</option>
                                        <option value="sides">🍟 Sides</option>
                                        <option value="beverages">🥤 Beverages</option>
                                        <option value="family-deals">👨‍👩‍👧‍👦 Family Deals</option>
                                    </select>

                                    <!-- Sort -->
                                    <select
                                        id="productSort"
                                        class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                    >
                                        <option value="name-asc">Name A-Z</option>
                                        <option value="name-desc">Name Z-A</option>
                                        <option value="price-asc">Price Low-High</option>
                                        <option value="price-desc">Price High-Low</option>
                                        <option value="stock-asc">Stock Low-High</option>
                                        <option value="stock-desc">Stock High-Low</option>
                                    </select>
                                </div>

                                <!-- View Mode Toggle -->
                                <div class="flex items-center space-x-2">
                                    <button id="gridViewBtn" class="bg-primary-600 text-white p-2 rounded-lg">
                                        <span class="text-sm">Grid</span>
                                    </button>
                                    <button id="listViewBtn" class="neu-button p-2 rounded-lg text-gray-700">
                                        <span class="text-sm">List</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <div id="inventoryProductsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        <!-- Product cards will be added here -->
                    </div>
                </div>

                <!-- Placeholder for other tabs -->
                <div id="content-customers" class="tab-content hidden">
                    <div class="text-center py-12">
                        <div class="text-6xl mb-4">👥</div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Customer Management</h2>
                        <p class="text-gray-600 max-w-md mx-auto">This section would contain customer management features including loyalty programs, customer history, and contact information.</p>
                    </div>
                </div>

                <div id="content-reports" class="tab-content hidden">
                    <div class="text-center py-12">
                        <div class="text-6xl mb-4">📈</div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Sales Reports</h2>
                        <p class="text-gray-600 max-w-md mx-auto">This section would contain detailed sales reports, analytics, and business insights.</p>
                    </div>
                </div>

                <div id="content-settings" class="tab-content hidden">
                    <div class="text-center py-12">
                        <div class="text-6xl mb-4">⚙️</div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">System Settings</h2>
                        <p class="text-gray-600 max-w-md mx-auto">This section would contain system settings, user management, and configuration options.</p>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Global state
        let currentTab = 'dashboard';
        let cart = [];
        let totalSales = 0;
        let totalOrders = 0;
        let orderNumber = 1001;
        let currentFilter = 'all';

        // H & Chicken Product data
        const products = [
            // Signature Sandwiches
            {
                id: 'signature-chicken-sandwich',
                name: 'H & Chicken Signature Sandwich',
                price: 9.99,
                cost: 4.20,
                emoji: '🍗',
                category: 'signature-sandwiches',
                sku: 'HCS001',
                stock: 35,
                lowStockAlert: 8,
                unit: 'pieces',
                description: 'Our signature fried chicken breast with special sauce, lettuce, and pickles on a brioche bun'
            },
            {
                id: 'spicy-deluxe-sandwich',
                name: 'Spicy Deluxe Sandwich',
                price: 10.99,
                cost: 4.50,
                emoji: '🌶️',
                category: 'signature-sandwiches',
                sku: 'SDS001',
                stock: 28,
                lowStockAlert: 8,
                unit: 'pieces',
                description: 'Spicy fried chicken breast with pepper jack cheese, spicy mayo, and jalapeños'
            },
            {
                id: 'bbq-bacon-sandwich',
                name: 'BBQ Bacon Chicken Sandwich',
                price: 11.99,
                cost: 5.00,
                emoji: '🥓',
                category: 'signature-sandwiches',
                sku: 'BBS001',
                stock: 22,
                lowStockAlert: 6,
                unit: 'pieces',
                description: 'Fried chicken breast with crispy bacon, BBQ sauce, and onion rings'
            },
            // Tender Combos
            {
                id: 'tender-combo-3pc',
                name: '3-Piece Tender Combo',
                price: 8.99,
                cost: 3.20,
                emoji: '🍗',
                category: 'tender-combos',
                sku: 'TC3001',
                stock: 40,
                lowStockAlert: 10,
                unit: 'combos',
                description: 'Three hand-breaded chicken tenders with fries and drink'
            },
            {
                id: 'tender-combo-5pc',
                name: '5-Piece Tender Combo',
                price: 12.99,
                cost: 4.80,
                emoji: '🍗',
                category: 'tender-combos',
                sku: 'TC5001',
                stock: 25,
                lowStockAlert: 8,
                unit: 'combos',
                description: 'Five hand-breaded chicken tenders with fries and drink'
            },
            {
                id: 'buffalo-tender-combo',
                name: 'Buffalo Tender Combo',
                price: 10.99,
                cost: 4.00,
                emoji: '🔥',
                category: 'tender-combos',
                sku: 'BTC001',
                stock: 18,
                lowStockAlert: 6,
                unit: 'combos',
                description: 'Buffalo-style chicken tenders with celery, ranch, fries and drink'
            },
            // Fried Chicken
            {
                id: 'fried-chicken-2pc',
                name: '2-Piece Fried Chicken',
                price: 6.99,
                cost: 2.50,
                emoji: '🍗',
                category: 'fried-chicken',
                sku: 'FC2001',
                stock: 35,
                lowStockAlert: 8,
                unit: 'pieces',
                description: 'Two pieces of our signature fried chicken'
            },
            {
                id: 'fried-chicken-4pc',
                name: '4-Piece Fried Chicken',
                price: 12.99,
                cost: 4.80,
                emoji: '🍗',
                category: 'fried-chicken',
                sku: 'FC4001',
                stock: 22,
                lowStockAlert: 6,
                unit: 'pieces',
                description: 'Four pieces of our signature fried chicken'
            },
            {
                id: 'fried-chicken-8pc',
                name: '8-Piece Fried Chicken',
                price: 24.99,
                cost: 9.50,
                emoji: '🍗',
                category: 'fried-chicken',
                sku: 'FC8001',
                stock: 12,
                lowStockAlert: 4,
                unit: 'pieces',
                description: 'Eight pieces of our signature fried chicken'
            },
            // Waffle & Chicken
            {
                id: 'chicken-waffle-classic',
                name: 'Classic Chicken & Waffle',
                price: 13.99,
                cost: 5.20,
                emoji: '🧇',
                category: 'waffle-chicken',
                sku: 'CW001',
                stock: 20,
                lowStockAlert: 5,
                unit: 'plates',
                description: 'Crispy fried chicken breast served on a golden Belgian waffle with syrup'
            },
            {
                id: 'chicken-waffle-deluxe',
                name: 'Deluxe Chicken & Waffle',
                price: 16.99,
                cost: 6.50,
                emoji: '🧇',
                category: 'waffle-chicken',
                sku: 'CWD001',
                stock: 15,
                lowStockAlert: 4,
                unit: 'plates',
                description: 'Two pieces of fried chicken on Belgian waffle with bacon and syrup'
            },
            // Sides
            {
                id: 'seasoned-fries',
                name: 'Seasoned Fries',
                price: 3.99,
                cost: 0.90,
                emoji: '🍟',
                category: 'sides',
                sku: 'SF001',
                stock: 60,
                lowStockAlert: 15,
                unit: 'servings',
                description: 'Crispy fries seasoned with our signature blend'
            },
            {
                id: 'mac-cheese',
                name: 'Mac & Cheese',
                price: 4.99,
                cost: 1.20,
                emoji: '🧀',
                category: 'sides',
                sku: 'MC001',
                stock: 2,
                lowStockAlert: 8,
                unit: 'servings',
                description: 'Creamy three-cheese macaroni and cheese'
            },
            {
                id: 'coleslaw',
                name: 'Coleslaw',
                price: 2.99,
                cost: 0.60,
                emoji: '🥗',
                category: 'sides',
                sku: 'CS001',
                stock: 35,
                lowStockAlert: 10,
                unit: 'servings',
                description: 'Fresh cabbage slaw with creamy dressing'
            },
            {
                id: 'mashed-potatoes',
                name: 'Mashed Potatoes & Gravy',
                price: 3.99,
                cost: 0.80,
                emoji: '🥔',
                category: 'sides',
                sku: 'MP001',
                stock: 25,
                lowStockAlert: 8,
                unit: 'servings',
                description: 'Creamy mashed potatoes with rich brown gravy'
            },
            {
                id: 'corn-on-cob',
                name: 'Corn on the Cob',
                price: 2.99,
                cost: 0.70,
                emoji: '🌽',
                category: 'sides',
                sku: 'COC001',
                stock: 30,
                lowStockAlert: 10,
                unit: 'pieces',
                description: 'Fresh corn on the cob with butter'
            },
            {
                id: 'biscuit',
                name: 'Fresh Biscuit',
                price: 1.99,
                cost: 0.40,
                emoji: '🥖',
                category: 'sides',
                sku: 'FB001',
                stock: 5,
                lowStockAlert: 12,
                unit: 'pieces',
                description: 'Warm, flaky biscuit with honey butter'
            },
            // Beverages
            {
                id: 'sweet-tea',
                name: 'Sweet Tea',
                price: 2.49,
                cost: 0.30,
                emoji: '🧊',
                category: 'beverages',
                sku: 'ST001',
                stock: 100,
                lowStockAlert: 20,
                unit: 'cups',
                description: 'Southern-style sweet iced tea'
            },
            {
                id: 'fresh-lemonade',
                name: 'Fresh Lemonade',
                price: 2.99,
                cost: 0.50,
                emoji: '🍋',
                category: 'beverages',
                sku: 'FL001',
                stock: 85,
                lowStockAlert: 15,
                unit: 'cups',
                description: 'Freshly squeezed lemonade'
            },
            {
                id: 'soft-drink',
                name: 'Soft Drink',
                price: 2.29,
                cost: 0.25,
                emoji: '🥤',
                category: 'beverages',
                sku: 'SD001',
                stock: 120,
                lowStockAlert: 25,
                unit: 'cups',
                description: 'Choice of Coke, Sprite, or Orange'
            },
            // Family Deals
            {
                id: 'family-feast-12pc',
                name: 'Family Feast (12pc)',
                price: 29.99,
                cost: 14.50,
                emoji: '👨‍👩‍👧‍👦',
                category: 'family-deals',
                sku: 'FF12001',
                stock: 8,
                lowStockAlert: 3,
                unit: 'meals',
                description: '12 pieces of mixed fried chicken with 3 large sides and 6 biscuits'
            },
            {
                id: 'family-combo-20pc',
                name: 'Family Combo (20pc)',
                price: 44.99,
                cost: 22.00,
                emoji: '👨‍👩‍👧‍👦',
                category: 'family-deals',
                sku: 'FC20001',
                stock: 5,
                lowStockAlert: 2,
                unit: 'meals',
                description: '20 pieces of mixed fried chicken with 4 large sides and 8 biscuits'
            },
            {
                id: 'tender-family-pack',
                name: 'Tender Family Pack',
                price: 26.99,
                cost: 12.80,
                emoji: '👨‍👩‍👧‍👦',
                category: 'family-deals',
                sku: 'TFP001',
                stock: 10,
                lowStockAlert: 3,
                unit: 'meals',
                description: '15 chicken tenders with 3 large sides and 6 biscuits'
            }
        ];

        // Initialize the app
        function init() {
            document.getElementById('currentDate').textContent = new Date().toLocaleDateString();
            updateDashboardStats();
            renderProducts();
            renderInventoryProducts();
            renderLowStockAlert();
            updateCartDisplay();
        }

        // Login function
        function login(event) {
            event.preventDefault();
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');
            init();
        }

        // Tab management
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Remove active class from all tabs
            document.querySelectorAll('[id^="tab-"]').forEach(tab => {
                tab.classList.remove('tab-active');
                tab.classList.add('text-gray-700', 'hover:bg-gray-100');
            });

            // Show selected tab content
            document.getElementById(`content-${tabName}`).classList.remove('hidden');

            // Add active class to selected tab
            const activeTab = document.getElementById(`tab-${tabName}`);
            activeTab.classList.add('tab-active');
            activeTab.classList.remove('text-gray-700', 'hover:bg-gray-100');

            currentTab = tabName;

            // Refresh data when switching tabs
            if (tabName === 'dashboard') {
                updateDashboardStats();
            } else if (tabName === 'pos') {
                renderProducts();
            } else if (tabName === 'products') {
                renderInventoryProducts();
                renderLowStockAlert();
            }
        }

        // Dashboard functions
        function updateDashboardStats() {
            document.getElementById('todaySales').textContent = `$${totalSales.toFixed(2)}`;
            document.getElementById('todayOrders').textContent = totalOrders;
            document.getElementById('todayCustomers').textContent = totalOrders; // Assuming 1 customer per order
            document.getElementById('newCustomers').textContent = Math.floor(totalOrders * 0.3);
            document.getElementById('avgOrder').textContent = totalOrders > 0 ? (totalSales / totalOrders).toFixed(2) : '0.00';
        }

        // POS functions
        function renderProducts() {
            const grid = document.getElementById('productsGrid');
            const filteredProducts = products.filter(product =>
                currentFilter === 'all' || product.category === currentFilter
            );

            grid.innerHTML = filteredProducts.map(product => `
                <div class="neu-card rounded-lg p-4 cursor-pointer hover:shadow-lg transition-shadow" onclick="addToCart('${product.id}')">
                    <div class="text-center">
                        <div class="text-4xl mb-2">${product.emoji}</div>
                        <h3 class="font-semibold text-gray-900 mb-1">${product.name}</h3>
                        <p class="text-sm text-gray-600 mb-3 line-clamp-2">${product.description}</p>
                        <p class="text-lg font-bold text-primary-600">$${product.price.toFixed(2)}</p>
                        ${product.stock <= product.lowStockAlert ?
                            `<p class="text-xs text-red-600 mt-1">Low Stock: ${product.stock}</p>` :
                            `<p class="text-xs text-green-600 mt-1">In Stock: ${product.stock}</p>`
                        }
                    </div>
                </div>
            `).join('');
        }

        function filterProducts(category) {
            currentFilter = category;

            // Update active filter button
            document.querySelectorAll('.category-filter').forEach(btn => {
                btn.classList.remove('active', 'bg-primary-600', 'text-white');
                btn.classList.add('neu-button');
            });

            event.target.classList.add('active', 'bg-primary-600', 'text-white');
            event.target.classList.remove('neu-button');

            renderProducts();
        }

        function addToCart(productId) {
            const product = products.find(p => p.id === productId);
            if (!product || product.stock <= 0) {
                alert('This item is out of stock!');
                return;
            }

            const existingItem = cart.find(item => item.id === productId);
            if (existingItem) {
                if (existingItem.quantity < product.stock) {
                    existingItem.quantity++;
                } else {
                    alert('Not enough stock available!');
                    return;
                }
            } else {
                cart.push({
                    id: productId,
                    name: product.name,
                    price: product.price,
                    emoji: product.emoji,
                    quantity: 1
                });
            }

            updateCartDisplay();
        }

        function removeFromCart(productId) {
            const itemIndex = cart.findIndex(item => item.id === productId);
            if (itemIndex > -1) {
                if (cart[itemIndex].quantity > 1) {
                    cart[itemIndex].quantity--;
                } else {
                    cart.splice(itemIndex, 1);
                }
            }
            updateCartDisplay();
        }

        function updateCartDisplay() {
            const cartItems = document.getElementById('cartItems');
            const cartCount = document.getElementById('cartCount');
            const cartSubtotal = document.getElementById('cartSubtotal');
            const cartTax = document.getElementById('cartTax');
            const cartTotal = document.getElementById('cartTotal');
            const checkoutBtn = document.getElementById('checkoutBtn');

            if (cart.length === 0) {
                cartItems.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <span class="text-4xl">🛒</span>
                        <p class="mt-2">Your cart is empty</p>
                        <p class="text-sm">Add items from the menu</p>
                    </div>
                `;
                checkoutBtn.disabled = true;
            } else {
                cartItems.innerHTML = cart.map(item => `
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <span class="text-xl">${item.emoji}</span>
                            <div>
                                <p class="font-medium">${item.name}</p>
                                <p class="text-sm text-gray-600">$${item.price.toFixed(2)} each</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="removeFromCart('${item.id}')" class="w-6 h-6 rounded-full bg-red-100 text-red-600 text-sm">-</button>
                            <span class="font-medium">${item.quantity}</span>
                            <button onclick="addToCart('${item.id}')" class="w-6 h-6 rounded-full bg-green-100 text-green-600 text-sm">+</button>
                        </div>
                    </div>
                `).join('');
                checkoutBtn.disabled = false;
            }

            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const tax = subtotal * 0.08;
            const total = subtotal + tax;
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);

            cartCount.textContent = totalItems;
            cartSubtotal.textContent = `$${subtotal.toFixed(2)}`;
            cartTax.textContent = `$${tax.toFixed(2)}`;
            cartTotal.textContent = `$${total.toFixed(2)}`;
        }

        function clearCart() {
            cart = [];
            updateCartDisplay();
        }

        function showCheckout() {
            if (cart.length === 0) return;

            const modal = document.getElementById('checkoutModal');
            const checkoutItems = document.getElementById('checkoutItems');
            const checkoutSubtotal = document.getElementById('checkoutSubtotal');
            const checkoutTax = document.getElementById('checkoutTax');
            const checkoutTotal = document.getElementById('checkoutTotal');

            checkoutItems.innerHTML = cart.map(item => `
                <div class="flex justify-between text-sm">
                    <span>${item.emoji} ${item.name} x${item.quantity}</span>
                    <span>$${(item.price * item.quantity).toFixed(2)}</span>
                </div>
            `).join('');

            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const tax = subtotal * 0.08;
            const total = subtotal + tax;

            checkoutSubtotal.textContent = `$${subtotal.toFixed(2)}`;
            checkoutTax.textContent = `$${tax.toFixed(2)}`;
            checkoutTotal.textContent = `$${total.toFixed(2)}`;

            modal.classList.remove('hidden');
        }

        function hideCheckout() {
            document.getElementById('checkoutModal').classList.add('hidden');
        }

        function processPayment(method) {
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) * 1.08;

            // Update inventory
            cart.forEach(item => {
                const product = products.find(p => p.id === item.id);
                if (product) {
                    product.stock -= item.quantity;
                }
            });

            // Update stats
            totalSales += total;
            totalOrders++;

            // Add to recent orders
            addRecentOrder(cart, total);

            // Clear cart
            cart = [];

            // Show success modal
            document.getElementById('checkoutModal').classList.add('hidden');
            document.getElementById('successOrderNumber').textContent = orderNumber++;
            document.getElementById('paymentSuccessModal').classList.remove('hidden');

            // Update displays
            updateCartDisplay();
            updateDashboardStats();
            renderProducts();
            renderInventoryProducts();
            renderLowStockAlert();
        }

        function closePaymentSuccess() {
            document.getElementById('paymentSuccessModal').classList.add('hidden');
        }

        function addRecentOrder(orderItems, total) {
            const recentOrdersList = document.getElementById('recentOrdersList');
            const orderDiv = document.createElement('div');
            orderDiv.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';
            orderDiv.innerHTML = `
                <div>
                    <p class="font-medium">Order #${orderNumber}</p>
                    <p class="text-sm text-gray-600">${orderItems.length} items • Just now</p>
                </div>
                <div class="text-right">
                    <p class="font-bold text-green-600">$${total.toFixed(2)}</p>
                    <p class="text-xs text-gray-500">Completed</p>
                </div>
            `;

            // Remove empty state if it exists
            const emptyState = recentOrdersList.querySelector('.text-center');
            if (emptyState) {
                emptyState.remove();
            }

            recentOrdersList.insertBefore(orderDiv, recentOrdersList.firstChild);

            // Keep only last 5 orders
            while (recentOrdersList.children.length > 5) {
                recentOrdersList.removeChild(recentOrdersList.lastChild);
            }
        }

        // Product Management functions
        function renderInventoryProducts() {
            const grid = document.getElementById('inventoryProductsGrid');

            grid.innerHTML = products.map(product => {
                const isLowStock = product.stock <= product.lowStockAlert;
                const isOutOfStock = product.stock === 0;

                return `
                    <div class="neu-card rounded-lg overflow-hidden">
                        <div class="h-48 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center relative">
                            <div class="text-6xl">${product.emoji}</div>
                            <div class="absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium ${
                                isOutOfStock ? 'bg-red-100 text-red-800' :
                                isLowStock ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                            }">
                                ${isOutOfStock ? 'Out of Stock' : isLowStock ? 'Low Stock' : 'In Stock'}
                            </div>
                            <div class="absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-700">
                                ${getCategoryIcon(product.category)} ${getCategoryName(product.category)}
                            </div>
                        </div>

                        <div class="p-4">
                            <div class="mb-3">
                                <h3 class="font-semibold text-gray-900 mb-1">${product.name}</h3>
                                <p class="text-sm text-gray-600">${product.description}</p>
                            </div>

                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <p class="text-lg font-bold text-primary-600">$${product.price.toFixed(2)}</p>
                                    <p class="text-xs text-gray-500">Cost: $${product.cost.toFixed(2)}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-xs text-gray-500">SKU</p>
                                    <p class="text-sm font-mono">${product.sku}</p>
                                </div>
                            </div>

                            <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700">Inventory</span>
                                    <button onclick="adjustInventory('${product.id}')" class="text-xs text-primary-600 hover:text-primary-700">
                                        Adjust
                                    </button>
                                </div>

                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">Current Stock:</span>
                                    <span class="font-medium ${isLowStock ? 'text-red-600' : 'text-gray-900'}">
                                        ${product.stock} ${product.unit}
                                    </span>
                                </div>

                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">Alert Level:</span>
                                    <span class="text-gray-900">${product.lowStockAlert}</span>
                                </div>

                                <div class="mt-2">
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="h-2 rounded-full transition-all ${
                                            isOutOfStock ? 'bg-red-500' :
                                            isLowStock ? 'bg-yellow-500' : 'bg-green-500'
                                        }" style="width: ${Math.min(100, (product.stock / (product.lowStockAlert * 2)) * 100)}%"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="flex space-x-2">
                                <button onclick="editProduct('${product.id}')" class="neu-button flex-1 px-3 py-2 rounded-lg text-sm">
                                    ✏️ Edit
                                </button>
                                <button onclick="adjustInventory('${product.id}')" class="neu-button px-3 py-2 rounded-lg text-sm">
                                    📦 Stock
                                </button>
                                <button onclick="deleteProduct('${product.id}')" class="text-red-600 hover:text-red-700 px-2 py-2 rounded-lg text-sm">
                                    🗑️
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function renderLowStockAlert() {
            const lowStockProducts = products.filter(p => p.stock <= p.lowStockAlert);
            const lowStockItems = document.getElementById('lowStockItems');
            const lowStockCount = document.getElementById('lowStockCount');
            const lowStockAlert = document.getElementById('lowStockAlert');

            if (lowStockProducts.length === 0) {
                lowStockAlert.classList.add('hidden');
                return;
            }

            lowStockAlert.classList.remove('hidden');
            lowStockCount.textContent = lowStockProducts.length;

            lowStockItems.innerHTML = lowStockProducts.map(product => {
                const isOutOfStock = product.stock === 0;

                return `
                    <div class="flex items-center justify-between p-4 rounded-lg border-2 ${
                        isOutOfStock ? 'bg-red-50 border-red-200' : 'bg-yellow-50 border-yellow-200'
                    }">
                        <div class="flex items-center space-x-4">
                            <div class="h-12 w-12 bg-white rounded-lg flex items-center justify-center text-xl shadow-sm">
                                ${product.emoji}
                            </div>
                            <div>
                                <h4 class="font-medium ${isOutOfStock ? 'text-red-900' : 'text-yellow-900'}">${product.name}</h4>
                                <div class="flex items-center space-x-4 text-sm">
                                    <span class="font-medium ${isOutOfStock ? 'text-red-700' : 'text-yellow-700'}">
                                        Current: ${product.stock} ${product.unit}
                                    </span>
                                    <span class="text-gray-600">
                                        Alert at: ${product.lowStockAlert} ${product.unit}
                                    </span>
                                    <span class="text-gray-500 font-mono">
                                        SKU: ${product.sku}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center space-x-2">
                            <div class="px-3 py-1 rounded-full text-xs font-medium ${
                                isOutOfStock ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                            }">
                                ${isOutOfStock ? 'Out of Stock' : 'Low Stock'}
                            </div>
                            <button onclick="restockProduct('${product.id}')" class="bg-primary-600 text-white px-3 py-1 rounded-lg text-sm font-medium hover:bg-primary-700">
                                Restock
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            // Update inventory stats
            document.getElementById('lowStockItems').textContent = lowStockProducts.filter(p => p.stock > 0 && p.stock <= p.lowStockAlert).length;
            document.getElementById('outOfStockItems').textContent = lowStockProducts.filter(p => p.stock === 0).length;

            const totalValue = products.reduce((sum, p) => sum + (p.stock * p.cost), 0);
            document.getElementById('inventoryValue').textContent = `$${totalValue.toFixed(2)}`;
        }

        function getCategoryIcon(category) {
            const icons = {
                'signature-sandwiches': '🍗',
                'tender-combos': '🍗',
                'fried-chicken': '🍗',
                'waffle-chicken': '🧇',
                'sides': '🍟',
                'beverages': '🥤',
                'family-deals': '👨‍👩‍👧‍👦'
            };
            return icons[category] || '📦';
        }

        function getCategoryName(category) {
            const names = {
                'signature-sandwiches': 'Signature Sandwiches',
                'tender-combos': 'Tender Combos',
                'fried-chicken': 'Fried Chicken',
                'waffle-chicken': 'Waffle & Chicken',
                'sides': 'Sides',
                'beverages': 'Beverages',
                'family-deals': 'Family Deals'
            };
            return names[category] || 'Other';
        }

        function adjustInventory(productId) {
            const product = products.find(p => p.id === productId);
            const newStock = prompt(`Current stock: ${product.stock} ${product.unit}\nEnter new stock level:`, product.stock);

            if (newStock !== null && !isNaN(newStock) && newStock >= 0) {
                product.stock = parseInt(newStock);
                renderInventoryProducts();
                renderLowStockAlert();
                renderProducts(); // Update POS view too
                alert(`Stock updated for ${product.name}`);
            }
        }

        function restockProduct(productId) {
            const product = products.find(p => p.id === productId);
            const restockAmount = prompt(`Restock ${product.name}\nCurrent: ${product.stock} ${product.unit}\nAdd quantity:`, product.lowStockAlert * 2);

            if (restockAmount !== null && !isNaN(restockAmount) && restockAmount > 0) {
                product.stock += parseInt(restockAmount);
                renderInventoryProducts();
                renderLowStockAlert();
                renderProducts(); // Update POS view too
                alert(`Added ${restockAmount} ${product.unit} to ${product.name}`);
            }
        }

        function editProduct(productId) {
            const product = products.find(p => p.id === productId);
            alert(`Edit Product: ${product.name}\n\nThis would open a detailed edit modal in the full application.`);
        }

        function deleteProduct(productId) {
            const product = products.find(p => p.id === productId);
            if (confirm(`Are you sure you want to delete ${product.name}?`)) {
                const index = products.findIndex(p => p.id === productId);
                products.splice(index, 1);
                renderInventoryProducts();
                renderLowStockAlert();
                renderProducts(); // Update POS view too
                alert(`${product.name} has been deleted`);
            }
        }

        function showAddProductModal() {
            alert('Add Product Modal\n\nThis would open a detailed form to add new menu items in the full application.');
        }

        function showCategoryManager() {
            alert('Category Manager\n\nThis would open the category management interface in the full application.');
        }

        // Add button press effects
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.neu-button').forEach(button => {
                button.addEventListener('mousedown', () => {
                    button.style.boxShadow = 'inset 2px 2px 5px #a3b1c6, inset -2px -2px 5px #ffffff';
                });
                button.addEventListener('mouseup', () => {
                    button.style.boxShadow = '2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff';
                });
                button.addEventListener('mouseleave', () => {
                    button.style.boxShadow = '2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff';
                });
            });
        });
    </script>
</body>
</html>