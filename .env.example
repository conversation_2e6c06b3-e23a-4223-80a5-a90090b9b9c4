# Supabase Configuration
REACT_APP_SUPABASE_URL=your_supabase_project_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key

# Payment Integration (Optional)
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
REACT_APP_CASHAPP_HANDLE=$YourCashAppHandle
REACT_APP_ZELLE_EMAIL=<EMAIL>
REACT_APP_VENMO_HANDLE=@YourVenmoHandle

# App Configuration
REACT_APP_APP_NAME=POSess
REACT_APP_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development
