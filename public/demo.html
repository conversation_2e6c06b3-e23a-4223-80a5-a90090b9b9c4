<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><PERSON> Bartender Assistant Demo</title>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #2a2a72;
      --secondary-color: #4f9cff;
      --accent-color: #ff6b6b;
      --dark-bg: #121212;
      --light-text: #f5f5f5;
    }
    
    body {
      margin: 0;
      font-family: 'Poppins', sans-serif;
      background: linear-gradient(135deg, var(--dark-bg), #1a1a2e);
      color: var(--light-text);
      line-height: 1.6;
    }
    
    .demo-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
    }
    
    header {
      text-align: center;
      margin-bottom: 60px;
    }
    
    h1 {
      font-size: 3rem;
      margin-bottom: 10px;
      background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .tagline {
      font-size: 1.2rem;
      opacity: 0.8;
    }
    
    .demo-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 40px;
    }
    
    .demo-video {
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
    
    .demo-video img {
      width: 100%;
      height: auto;
      display: block;
    }
    
    .features-list {
      padding: 0;
    }
    
    .feature-item {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 20px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    }
    
    .feature-item h3 {
      color: var(--secondary-color);
      margin-top: 0;
    }
    
    .cta-section {
      text-align: center;
      margin-top: 60px;
    }
    
    .cta-button {
      display: inline-block;
      padding: 15px 30px;
      background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
      color: white;
      text-decoration: none;
      border-radius: 30px;
      font-weight: 600;
      font-size: 1.1rem;
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .cta-button:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
    }
    
    footer {
      text-align: center;
      margin-top: 80px;
      opacity: 0.7;
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <header>
      <h1>AI Bartender Assistant</h1>
      <p class="tagline">Your interactive 3D assistant for mobile bartending services</p>
    </header>
    
    <div class="demo-content">
      <div class="demo-video">
        <img src="https://via.placeholder.com/600x400/1a1a2e/4f9cff?text=AI+Bartender+Demo" alt="AI Bartender Demo">
      </div>
      
      <div class="features-list">
        <div class="feature-item">
          <h3>3D Interactive Orb</h3>
          <p>A beautiful, pulsating orb that responds to voice interactions, creating an engaging and futuristic experience for your clients.</p>
        </div>
        
        <div class="feature-item">
          <h3>Voice-Powered Ordering</h3>
          <p>Natural conversation with AI to help clients select cocktail packages, customize orders, and schedule events.</p>
        </div>
        
        <div class="feature-item">
          <h3>Client Memory</h3>
          <p>The assistant remembers client preferences, past orders, and special requests for a personalized experience.</p>
        </div>
        
        <div class="feature-item">
          <h3>Seamless Payments</h3>
          <p>Guides clients through CashApp and Zelle payment processes with clear instructions and secure transactions.</p>
        </div>
      </div>
    </div>
    
    <div class="cta-section">
      <a href="/" class="cta-button">Launch Demo App</a>
    </div>
    
    <footer>
      <p>© 2023 Mobile Bartending Services. All rights reserved.</p>
    </footer>
  </div>
</body>
</html>