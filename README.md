# POSess - Modern Cloud POS Platform

POSess is a modern cloud-based Point of Sale platform for small business owners. It's a powerful alternative to traditional POS systems like Clover and Lightspeed, with no mandatory transaction fees.

## 🚀 Features

- **Modern UI/UX Design** - Neumorphic design with dark/light mode
- **Product & Inventory Management** - Full catalog system with categories and stock tracking
- **POS Core Functionality** - Touch-friendly sales interface with cart management
- **Alternative Payment Integration** - CashApp, Zelle, Venmo, QR payments (no mandatory fees!)
- **Order Management System** - Multi-channel order tracking and processing
- **Customer Management & Loyalty** - Customer database with points system
- **Analytics Dashboard** - Real-time business insights and reporting
- **Admin Controls & User Management** - Role-based access control

## 🛠️ Tech Stack

- **Frontend**: React, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Realtime)
- **Payment Processing**: CashApp, Zelle, Venmo, ACH (to avoid traditional payment fees)

## 📋 Prerequisites

- Node.js 16+ and npm
- Supabase account (free tier available)

## 🔧 Setup Instructions

### 1. Clone the repository

```bash
git clone https://github.com/yourusername/posess.git
cd posess
```

### 2. Install dependencies

```bash
npm install
```

### 3. Set up Supabase

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Go to Project Settings > API to get your project URL and anon key
3. Create a `.env` file in the project root (use `.env.example` as a template)
4. Add your Supabase credentials:

```
REACT_APP_SUPABASE_URL=your_supabase_project_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Set up the database schema

1. Go to the SQL Editor in your Supabase dashboard
2. Copy the contents of `supabase/schema.sql` from this repository
3. Paste and run the SQL in the Supabase SQL Editor

### 5. Start the development server

```bash
npm start
```

The app will be available at [http://localhost:3000](http://localhost:3000)

## 📱 Using POSess

### First-time setup

1. Sign up with your email and password
2. Create your business profile
3. Add products and categories
4. Configure payment methods
5. Start selling!

### Key workflows

- **Selling Products**: Navigate to the POS screen, add items to cart, and checkout
- **Managing Inventory**: Add/edit products, track stock levels, receive inventory
- **Customer Management**: Create customer profiles, track purchase history, manage loyalty points
- **Analytics**: View sales reports, track performance, export data

## 🔒 Security Features

POSess uses Supabase's security features:

- **Row Level Security (RLS)**: Data is protected at the database level
- **JWT Authentication**: Secure token-based authentication
- **Role-Based Access Control**: Different permission levels for owners, managers, and cashiers

## 🌐 Deployment

To deploy POSess to production:

1. Build the production version:

```bash
npm run build
```

2. Deploy the `build` folder to your preferred hosting service:
   - Vercel
   - Netlify
   - Firebase Hosting
   - AWS Amplify
   - GitHub Pages

## 📊 Database Schema

POSess uses the following main tables in Supabase:

- **profiles**: User accounts and permissions
- **businesses**: Business information and settings
- **products**: Product catalog and inventory
- **categories**: Product categories
- **customers**: Customer database and loyalty
- **orders**: Sales transactions
- **payments**: Payment records
- **shifts**: Cash drawer management

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📧 Contact

For support or inquiries, <NAME_EMAIL>
