<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>H & Chicken - Complete POS System Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fef7ed',
                            100: '#fdedd3',
                            200: '#fbd6a5',
                            300: '#f8b86d',
                            400: '#f59332',
                            500: '#f37316',
                            600: '#e4570b',
                            700: '#bd420b',
                            800: '#973510',
                            900: '#7c2d12',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .neu-card {
            background: #e0e5ec;
            box-shadow: 2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff;
            transition: all 0.2s ease;
        }
        .neu-button {
            background: #e0e5ec;
            box-shadow: 2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .neu-button:active {
            box-shadow: inset 2px 2px 5px #a3b1c6, inset -2px -2px 5px #ffffff;
        }
        .hidden { display: none; }
        .tab-active {
            background: linear-gradient(135deg, #f37316, #e4570b);
            color: white;
            box-shadow: inset 1px 1px 2px rgba(0,0,0,0.1);
        }
        .nfc-container {
            position: relative;
        }
        .nfc-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.3) 100%);
            border-radius: 0.5rem;
            pointer-events: none;
        }
        .nfc-gif {
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
        }
        .brand-logo {
            transition: all 0.3s ease;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
            max-width: 100%;
            max-height: 100%;
        }
        .brand-logo:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.15));
        }
        @keyframes logoGlow {
            0%, 100% { filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1)); }
            50% { filter: drop-shadow(0 2px 8px rgba(243, 115, 22, 0.3)); }
        }
        .brand-logo.animate-glow {
            animation: logoGlow 2s ease-in-out infinite;
        }
        .login-logo-container {
            width: 100px;
            height: 100px;
        }
        .header-logo-container {
            width: 64px;
            height: 64px;
        }

        /* Dashboard platter image styling */
        .platter-image-container {
            transition: all 0.3s ease;
        }
        .platter-image-container:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .platter-image-container img {
            transition: all 0.3s ease;
        }
        .platter-image-container:hover img {
            filter: brightness(1.1) contrast(1.05);
        }

        /* Navigation tab hover animations */
        nav button {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        nav button:hover {
            transform: translateX(8px);
            background: linear-gradient(135deg, #f37316, #e4570b);
            color: white;
            box-shadow: 0 4px 12px rgba(243, 115, 22, 0.3);
        }
        nav button:hover::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 0.6s ease-in-out;
        }
        nav button.tab-active:hover {
            transform: translateX(4px);
            box-shadow: 0 6px 16px rgba(243, 115, 22, 0.4);
        }
        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Enhanced hover effects for cards and buttons */
        .neu-card {
            transition: all 0.3s ease;
        }
        .neu-card:hover {
            transform: translateY(-2px);
            box-shadow: 4px 4px 12px #a3b1c6, -4px -4px 12px #ffffff;
        }

        /* Product card hover effects */
        .product-card {
            transition: all 0.3s ease;
        }
        .product-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        /* Button hover enhancements */
        .neu-button:hover {
            transform: translateY(-1px);
            box-shadow: 3px 3px 8px #a3b1c6, -3px -3px 8px #ffffff;
        }

        /* Quick action buttons */
        .quick-action-btn {
            transition: all 0.3s ease;
        }
        .quick-action-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 6px 20px rgba(243, 115, 22, 0.3);
        }

        /* Add image button styling */
        .add-image-btn {
            transition: all 0.2s ease;
            border: 2px solid rgba(255,255,255,0.2);
        }
        .add-image-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            border-color: rgba(255,255,255,0.4);
        }
        .add-image-btn:active {
            transform: scale(0.95);
        }

        /* Mobile responsive adjustments */
        @media (max-width: 640px) {
            .login-logo-container {
                width: 80px;
                height: 80px;
            }
            .header-logo-container {
                width: 48px;
                height: 48px;
            }
            .header-logo-container + div h1 {
                font-size: 1.25rem;
            }
            .header-logo-container + div p {
                font-size: 0.75rem;
            }
        }
        .logo-fallback {
            background: linear-gradient(135deg, #f37316, #e4570b);
            color: white;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Login Screen -->
    <div id="loginScreen" class="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center p-4">
        <div class="neu-card p-8 rounded-xl max-w-md w-full">
            <div class="text-center mb-6">
                <div class="login-logo-container bg-white rounded-2xl flex items-center justify-center mx-auto mb-4 p-3 shadow-lg">
                    <img src="H_Chicken.png" alt="H & Chicken Logo" class="w-full h-full object-contain brand-logo"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                    <div class="w-full h-full rounded-xl logo-fallback text-3xl font-bold" style="display: none;">
                        H&C
                    </div>
                </div>
                <h1 class="text-2xl font-bold text-gray-900">Welcome to H & Chicken</h1>
                <p class="text-gray-600 mt-2">Authentic Fried Chicken & More</p>
            </div>

            <form onsubmit="login(event)" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input type="email" value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500" readonly>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input type="password" value="demo123" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500" readonly>
                </div>
                <button type="submit" class="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors">
                    Sign In
                </button>
            </form>

            <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                <p class="text-sm text-blue-700">
                    <strong>Demo Account:</strong> Full access to all features including Dashboard, POS, and Product Management.
                </p>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="hidden">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center space-x-4">
                    <div class="header-logo-container bg-white rounded-lg flex items-center justify-center p-2 shadow-sm">
                        <img src="H_Chicken.png" alt="H & Chicken Logo" class="w-full h-full object-contain brand-logo"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                        <div class="w-full h-full rounded logo-fallback text-lg font-bold" style="display: none;">
                            H&C
                        </div>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">H & Chicken</h1>
                        <p class="text-sm text-gray-500">Authentic Fried Chicken & More</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>Shift Active</span>
                    </div>
                    <div id="paymentProcessingIndicator" class="hidden flex items-center space-x-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                        <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        <span id="paymentProcessingText">Processing Payment...</span>
                    </div>
                    <div class="relative">
                        <button class="neu-button px-4 py-2 rounded-lg text-gray-700 font-medium" onclick="showTab('pos')">
                            🛒 Cart (<span id="cartCount">0</span>)
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <div class="flex">
            <!-- Sidebar -->
            <aside class="w-64 bg-white shadow-sm h-screen">
                <nav class="p-4 space-y-2">
                    <button onclick="showTab('dashboard')" id="tab-dashboard" class="w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100 tab-active">
                        📊 Dashboard
                    </button>
                    <button onclick="showTab('pos')" id="tab-pos" class="w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        🛒 Order Entry
                    </button>
                    <button onclick="showTab('products')" id="tab-products" class="w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        📦 Menu Items
                    </button>
                    <button onclick="showTab('customers')" id="tab-customers" class="w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        👥 Customers
                    </button>
                    <button onclick="showTab('reports')" id="tab-reports" class="w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        📈 Sales Reports
                    </button>
                    <button onclick="showTab('settings')" id="tab-settings" class="w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        ⚙️ Settings
                    </button>
                </nav>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 p-6 overflow-y-auto">
                <!-- Dashboard Tab -->
                <div id="content-dashboard" class="tab-content">
                    <!-- Welcome Header and Platter Image Row -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                        <!-- Welcome Section -->
                        <div class="lg:col-span-2">
                            <div class="bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg p-6 text-white h-full">
                                <div class="flex items-center justify-between h-full">
                                    <div>
                                        <h1 class="text-2xl font-bold mb-2">Welcome to H & Chicken! 🍗</h1>
                                        <p class="text-primary-100 mb-4">Authentic Fried Chicken & More - Serving quality since today</p>
                                        <div class="flex items-center space-x-4 text-primary-200 text-sm">
                                            <div>
                                                <span class="block">Today's Date</span>
                                                <span class="text-xl font-semibold text-white" id="currentDate"></span>
                                            </div>
                                            <div class="hidden md:block">
                                                <span class="block">Restaurant Status</span>
                                                <span class="text-xl font-semibold text-white">Open & Serving</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- H & Chicken Platter Image -->
                        <div class="lg:col-span-1">
                            <div class="neu-card rounded-lg overflow-hidden h-full platter-image-container">
                                <div class="relative h-full min-h-[200px] bg-gradient-to-br from-primary-50 to-primary-100">
                                    <img
                                        src="H_Chicken_Platter.png"
                                        alt="H & Chicken Signature Platter"
                                        class="w-full h-full object-cover"
                                        onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                                    />
                                    <!-- Fallback content if image doesn't load -->
                                    <div class="absolute inset-0 flex flex-col items-center justify-center text-center p-4" style="display: none;">
                                        <div class="text-6xl mb-3">🍗</div>
                                        <h3 class="text-lg font-bold text-primary-700 mb-2">H & Chicken</h3>
                                        <p class="text-sm text-primary-600">Signature Platter</p>
                                        <p class="text-xs text-gray-500 mt-2">Delicious fried chicken & sides</p>
                                    </div>


                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Stats Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-green-100 mr-4">
                                    <span class="text-green-600 text-xl">💰</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Today's Sales</p>
                                    <p class="text-2xl font-bold text-gray-900" id="todaySales">$0.00</p>
                                    <p class="text-xs text-green-600">↗️ +15.3% from yesterday</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-blue-100 mr-4">
                                    <span class="text-blue-600 text-xl">🛒</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Orders</p>
                                    <p class="text-2xl font-bold text-gray-900" id="todayOrders">0</p>
                                    <p class="text-xs text-gray-500">Avg. $<span id="avgOrder">0.00</span> per order</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-purple-100 mr-4">
                                    <span class="text-purple-600 text-xl">👥</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Customers</p>
                                    <p class="text-2xl font-bold text-gray-900" id="todayCustomers">0</p>
                                    <p class="text-xs text-gray-500"><span id="newCustomers">0</span> new today</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-orange-100 mr-4">
                                    <span class="text-orange-600 text-xl">🔥</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Hot Items</p>
                                    <p class="text-2xl font-bold text-gray-900">🍗</p>
                                    <p class="text-xs text-gray-500" id="hotItem">Signature Sandwich leading</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <div class="neu-card rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <button onclick="showTab('pos')" class="bg-primary-600 text-white h-20 rounded-lg flex flex-col items-center justify-center space-y-2 hover:bg-primary-700 quick-action-btn">
                                    <span class="text-xl">🛒</span>
                                    <span class="text-sm font-medium">New Order</span>
                                </button>
                                <button onclick="showTab('products')" class="neu-button h-20 rounded-lg flex flex-col items-center justify-center space-y-2 quick-action-btn">
                                    <span class="text-xl">🍗</span>
                                    <span class="text-sm font-medium">Menu Items</span>
                                </button>
                                <button onclick="showTab('customers')" class="neu-button h-20 rounded-lg flex flex-col items-center justify-center space-y-2 quick-action-btn">
                                    <span class="text-xl">👥</span>
                                    <span class="text-sm font-medium">Customers</span>
                                </button>
                                <button onclick="showTab('reports')" class="neu-button h-20 rounded-lg flex flex-col items-center justify-center space-y-2 quick-action-btn">
                                    <span class="text-xl">📈</span>
                                    <span class="text-sm font-medium">Reports</span>
                                </button>
                            </div>
                        </div>

                        <!-- Recent Orders -->
                        <div class="neu-card rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-4">Recent Orders</h3>
                            <div id="recentOrdersList" class="space-y-3">
                                <div class="text-center text-gray-500 py-8">
                                    <span class="text-4xl">🍗</span>
                                    <p class="mt-2">No orders yet today</p>
                                    <p class="text-sm">Start taking orders to see them here!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- POS Tab -->
                <div id="content-pos" class="tab-content hidden">
                    <div class="flex flex-col lg:flex-row gap-6">
                        <!-- Left Side - Menu Items -->
                        <div class="lg:w-2/3">
                            <!-- Categories -->
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold mb-4">Menu Categories</h3>
                                <div class="flex flex-wrap gap-2">
                                    <button onclick="filterProducts('all')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter active">
                                        All Items
                                    </button>
                                    <button onclick="filterProducts('signature-sandwiches')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        🍗 Signature Sandwiches
                                    </button>
                                    <button onclick="filterProducts('tender-combos')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        🍗 Tender Combos
                                    </button>
                                    <button onclick="filterProducts('fried-chicken')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        🍗 Fried Chicken
                                    </button>
                                    <button onclick="filterProducts('waffle-chicken')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        🧇 Waffle & Chicken
                                    </button>
                                    <button onclick="filterProducts('sides')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        🍟 Sides
                                    </button>
                                    <button onclick="filterProducts('beverages')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        🥤 Beverages
                                    </button>
                                    <button onclick="filterProducts('family-deals')" class="neu-button px-4 py-2 rounded-lg text-sm font-medium category-filter">
                                        👨‍👩‍👧‍👦 Family Deals
                                    </button>
                                </div>
                            </div>

                            <!-- Products Grid -->
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-4" id="productsGrid">
                                <!-- Products will be dynamically added here -->
                            </div>
                        </div>

                        <!-- Right Side - Cart -->
                        <div class="lg:w-1/3">
                            <div class="neu-card rounded-lg p-6 sticky top-6">
                                <h3 class="text-lg font-semibold mb-4">Current Order</h3>

                                <!-- Cart Items -->
                                <div id="cartItems" class="space-y-3 mb-6 max-h-96 overflow-y-auto">
                                    <div class="text-center text-gray-500 py-8">
                                        <span class="text-4xl">🛒</span>
                                        <p class="mt-2">Your cart is empty</p>
                                        <p class="text-sm">Add items from the menu</p>
                                    </div>
                                </div>

                                <!-- Cart Summary -->
                                <div class="border-t border-gray-200 pt-4 space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Subtotal</span>
                                        <span class="font-medium" id="cartSubtotal">$0.00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Tax (8%)</span>
                                        <span class="font-medium" id="cartTax">$0.00</span>
                                    </div>
                                    <div class="flex justify-between text-lg font-bold">
                                        <span>Total</span>
                                        <span id="cartTotal">$0.00</span>
                                    </div>
                                </div>

                                <!-- Checkout Button -->
                                <button id="checkoutBtn" onclick="showCheckout()" class="w-full bg-primary-600 text-white py-3 px-4 rounded-lg mt-6 font-medium hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                    Checkout
                                </button>

                                <!-- Clear Cart Button -->
                                <button onclick="clearCart()" class="w-full neu-button py-2 px-4 rounded-lg mt-3 text-sm font-medium">
                                    Clear Order
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Checkout Modal -->
                    <div id="checkoutModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
                        <div class="bg-white rounded-lg max-w-md w-full">
                            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-900">Complete Order</h2>
                                <button onclick="hideCheckout()" class="text-gray-400 hover:text-gray-600">
                                    ✕
                                </button>
                            </div>

                            <div class="p-6 space-y-6">
                                <div class="space-y-2">
                                    <p class="font-medium">Order Summary</p>
                                    <div id="checkoutItems" class="space-y-2 max-h-40 overflow-y-auto"></div>

                                    <div class="border-t border-gray-200 pt-4 space-y-2 mt-4">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Subtotal</span>
                                            <span class="font-medium" id="checkoutSubtotal">$0.00</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">Tax (8%)</span>
                                            <span class="font-medium" id="checkoutTax">$0.00</span>
                                        </div>
                                        <div class="flex justify-between text-lg font-bold">
                                            <span>Total</span>
                                            <span id="checkoutTotal">$0.00</span>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <p class="font-medium mb-3">Payment Method</p>
                                    <div class="grid grid-cols-2 gap-3">
                                        <button onclick="processPayment('cash')" class="neu-button p-3 rounded-lg text-center hover:bg-green-50">
                                            💵 Cash
                                        </button>
                                        <button onclick="showCashAppPayment()" class="neu-button p-3 rounded-lg text-center hover:bg-green-50">
                                            💚 CashApp
                                        </button>
                                        <button onclick="showZellePayment()" class="neu-button p-3 rounded-lg text-center hover:bg-blue-50">
                                            🏦 Zelle
                                        </button>
                                        <button onclick="processPayment('card')" class="neu-button p-3 rounded-lg text-center hover:bg-gray-50">
                                            💳 Card
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CashApp Payment Modal -->
                    <div id="cashAppModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
                        <div class="bg-white rounded-lg max-w-md w-full">
                            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                        <span class="text-white font-bold text-sm">$</span>
                                    </div>
                                    <h2 class="text-xl font-semibold text-gray-900">CashApp Payment</h2>
                                </div>
                                <button onclick="hideCashAppPayment()" class="text-gray-400 hover:text-gray-600">
                                    ✕
                                </button>
                            </div>

                            <div class="p-6 space-y-6">
                                <!-- Payment Method Tabs -->
                                <div class="flex bg-gray-100 rounded-lg p-1">
                                    <button onclick="switchCashAppMethod('nfc')" id="cashAppNFCTab" class="flex-1 py-2 px-4 rounded-md text-sm font-medium bg-green-500 text-white transition-colors">
                                        📡 NFC Tap
                                    </button>
                                    <button onclick="switchCashAppMethod('qr')" id="cashAppQRTab" class="flex-1 py-2 px-4 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors">
                                        📱 QR Code
                                    </button>
                                </div>

                                <!-- NFC Payment Section -->
                                <div id="cashAppNFCSection" class="text-center">
                                    <div class="w-32 h-32 bg-gradient-to-br from-green-100 to-green-200 rounded-lg mx-auto mb-4 flex items-center justify-center relative overflow-hidden nfc-container">
                                        <div id="nfcAnimation" class="absolute inset-0 flex items-center justify-center">
                                            <img src="NFC.gif" alt="NFC Animation" class="w-24 h-24 object-contain nfc-gif" />
                                        </div>
                                    </div>
                                    <p class="text-lg font-semibold text-gray-900 mb-2">Tap Phone to Pay</p>
                                    <p class="text-sm text-gray-600 mb-2">Hold your phone near the NFC reader</p>
                                    <p class="text-2xl font-bold text-gray-900 mt-2">$<span id="cashAppAmountNFC">0.00</span></p>

                                    <div class="mt-4 p-3 bg-green-50 rounded-lg">
                                        <p class="text-sm text-green-700">
                                            <strong>NFC Status:</strong> <span id="nfcStatus">Ready for tap</span>
                                        </p>
                                    </div>
                                </div>

                                <!-- QR Code Section (Hidden by default) -->
                                <div id="cashAppQRSection" class="text-center hidden">
                                    <div class="w-32 h-32 bg-gray-100 rounded-lg mx-auto mb-4 flex items-center justify-center">
                                        <div id="cashAppQR" class="text-6xl">📱</div>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">Scan QR code with CashApp or send to:</p>
                                    <p class="font-mono text-lg font-bold text-green-600">$HChickenPOS</p>
                                    <p class="text-2xl font-bold text-gray-900 mt-2">$<span id="cashAppAmount">0.00</span></p>
                                </div>

                                <div id="cashAppInstructions">
                                    <!-- NFC Instructions -->
                                    <div id="nfcInstructions" class="bg-green-50 p-4 rounded-lg">
                                        <h4 class="font-medium text-green-900 mb-2">NFC Payment Instructions:</h4>
                                        <ol class="text-sm text-green-700 space-y-1">
                                            <li>1. Open CashApp on your phone</li>
                                            <li>2. Enable NFC in your phone settings</li>
                                            <li>3. Hold phone near the NFC reader above</li>
                                            <li>4. Confirm payment when prompted</li>
                                        </ol>
                                    </div>

                                    <!-- QR Instructions (Hidden by default) -->
                                    <div id="qrInstructions" class="bg-green-50 p-4 rounded-lg hidden">
                                        <h4 class="font-medium text-green-900 mb-2">QR Payment Instructions:</h4>
                                        <ol class="text-sm text-green-700 space-y-1">
                                            <li>1. Open CashApp on your phone</li>
                                            <li>2. Scan the QR code or search $HChickenPOS</li>
                                            <li>3. Send the exact amount shown above</li>
                                            <li>4. Include order number in the note</li>
                                        </ol>
                                    </div>
                                </div>

                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <span class="text-sm font-medium">Payment Status:</span>
                                        <span id="cashAppStatus" class="text-sm text-yellow-600">⏳ Waiting for payment...</span>
                                    </div>

                                    <div class="flex space-x-3">
                                        <button onclick="simulateCashAppNFC()" id="simulateCashAppBtn" class="flex-1 bg-green-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-600 transition-colors">
                                            📡 Simulate NFC Tap
                                        </button>
                                        <button onclick="hideCashAppPayment()" class="flex-1 neu-button py-2 px-4 rounded-lg font-medium">
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Zelle Payment Modal -->
                    <div id="zelleModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
                        <div class="bg-white rounded-lg max-w-md w-full">
                            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                                        <span class="text-white font-bold text-sm">Z</span>
                                    </div>
                                    <h2 class="text-xl font-semibold text-gray-900">Zelle Payment</h2>
                                </div>
                                <button onclick="hideZellePayment()" class="text-gray-400 hover:text-gray-600">
                                    ✕
                                </button>
                            </div>

                            <div class="p-6 space-y-6">
                                <!-- Payment Method Tabs -->
                                <div class="flex bg-gray-100 rounded-lg p-1">
                                    <button onclick="switchZelleMethod('nfc')" id="zelleNFCTab" class="flex-1 py-2 px-4 rounded-md text-sm font-medium bg-blue-600 text-white transition-colors">
                                        📡 NFC Tap
                                    </button>
                                    <button onclick="switchZelleMethod('email')" id="zelleEmailTab" class="flex-1 py-2 px-4 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors">
                                        📧 Email Transfer
                                    </button>
                                </div>

                                <!-- NFC Payment Section -->
                                <div id="zelleNFCSection" class="text-center">
                                    <div class="w-32 h-32 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg mx-auto mb-4 flex items-center justify-center relative overflow-hidden nfc-container">
                                        <div id="zelleNfcAnimation" class="absolute inset-0 flex items-center justify-center">
                                            <img src="NFC.gif" alt="NFC Animation" class="w-24 h-24 object-contain nfc-gif" />
                                        </div>
                                    </div>
                                    <p class="text-lg font-semibold text-gray-900 mb-2">Tap Phone to Pay</p>
                                    <p class="text-sm text-gray-600 mb-2">Hold your phone near the NFC reader</p>
                                    <p class="text-2xl font-bold text-gray-900 mt-2">$<span id="zelleAmountNFC">0.00</span></p>

                                    <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                                        <p class="text-sm text-blue-700">
                                            <strong>NFC Status:</strong> <span id="zelleNfcStatus">Ready for tap</span>
                                        </p>
                                    </div>
                                </div>

                                <!-- Email Transfer Section (Hidden by default) -->
                                <div id="zelleEmailSection" class="text-center hidden">
                                    <div class="w-32 h-32 bg-blue-50 rounded-lg mx-auto mb-4 flex items-center justify-center">
                                        <div class="text-6xl">🏦</div>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">Send Zelle payment to:</p>
                                    <p class="font-mono text-lg font-bold text-blue-600"><EMAIL></p>
                                    <p class="text-2xl font-bold text-gray-900 mt-2">$<span id="zelleAmount">0.00</span></p>
                                </div>

                                <div id="zelleInstructions">
                                    <!-- NFC Instructions -->
                                    <div id="zelleNfcInstructions" class="bg-blue-50 p-4 rounded-lg">
                                        <h4 class="font-medium text-blue-900 mb-2">NFC Payment Instructions:</h4>
                                        <ol class="text-sm text-blue-700 space-y-1">
                                            <li>1. Open your banking app with Zelle</li>
                                            <li>2. Enable NFC in your phone settings</li>
                                            <li>3. Hold phone near the NFC reader above</li>
                                            <li>4. Confirm Zelle payment when prompted</li>
                                        </ol>
                                    </div>

                                    <!-- Email Instructions (Hidden by default) -->
                                    <div id="zelleEmailInstructions" class="bg-blue-50 p-4 rounded-lg hidden">
                                        <h4 class="font-medium text-blue-900 mb-2">Email Transfer Instructions:</h4>
                                        <ol class="text-sm text-blue-700 space-y-1">
                                            <li>1. Open your banking app</li>
                                            <li>2. Select "Send Money with Zelle"</li>
                                            <li>3. Enter: <EMAIL></li>
                                            <li>4. Send the exact amount shown above</li>
                                            <li>5. Include order number in memo</li>
                                        </ol>
                                    </div>
                                </div>

                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <span class="text-sm font-medium">Payment Status:</span>
                                        <span id="zelleStatus" class="text-sm text-yellow-600">⏳ Waiting for payment...</span>
                                    </div>

                                    <div class="bg-yellow-50 p-3 rounded-lg">
                                        <p class="text-xs text-yellow-700">
                                            <strong>Note:</strong> Zelle payments typically arrive within minutes.
                                            We'll notify you once payment is confirmed.
                                        </p>
                                    </div>

                                    <div class="flex space-x-3">
                                        <button onclick="simulateZelleNFC()" id="simulateZelleBtn" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                                            📡 Simulate NFC Tap
                                        </button>
                                        <button onclick="hideZellePayment()" class="flex-1 neu-button py-2 px-4 rounded-lg font-medium">
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Success Modal -->
                    <div id="paymentSuccessModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
                        <div class="bg-white rounded-lg max-w-md w-full p-6 text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-green-600 text-2xl">✓</span>
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 mb-2">Payment Successful!</h2>
                            <p class="text-gray-600 mb-2">Order #<span id="successOrderNumber">1001</span> has been completed.</p>
                            <p class="text-sm text-gray-500 mb-6">Payment method: <span id="successPaymentMethod">Cash</span></p>
                            <button onclick="closePaymentSuccess()" class="bg-primary-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors">
                                New Order
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Products Tab -->
                <div id="content-products" class="tab-content hidden">
                    <!-- Product Management Header -->
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Menu Items & Inventory</h1>
                            <p class="text-gray-600">Manage your chicken restaurant menu and track inventory</p>
                        </div>
                        <div class="flex space-x-3">
                            <button onclick="showCategoryManager()" class="neu-button px-4 py-2 rounded-lg text-gray-700 font-medium">
                                📋 Categories
                            </button>
                            <button onclick="showAddProductModal()" class="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700">
                                + Add Menu Item
                            </button>
                        </div>
                    </div>

                    <!-- Inventory Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-blue-100 mr-4">
                                    <span class="text-blue-600 text-xl">📦</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Items</p>
                                    <p class="text-2xl font-bold text-gray-900" id="totalItems">12</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-yellow-100 mr-4">
                                    <span class="text-yellow-600 text-xl">⚠️</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Low Stock</p>
                                    <p class="text-2xl font-bold text-gray-900" id="lowStockItems">3</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-red-100 mr-4">
                                    <span class="text-red-600 text-xl">📉</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Out of Stock</p>
                                    <p class="text-2xl font-bold text-gray-900" id="outOfStockItems">1</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-green-100 mr-4">
                                    <span class="text-green-600 text-xl">📈</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Inventory Value</p>
                                    <p class="text-2xl font-bold text-gray-900" id="inventoryValue">$1,247.50</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Low Stock Alert -->
                    <div id="lowStockAlert" class="neu-card rounded-lg mb-6 border-l-4 border-l-yellow-500">
                        <div class="p-6">
                            <h3 class="flex items-center text-lg font-semibold text-yellow-700 mb-4">
                                <span class="mr-2">⚠️</span>
                                Low Stock Alert (<span id="lowStockCount">3</span> items)
                            </h3>
                            <div id="lowStockItems" class="space-y-3">
                                <!-- Low stock items will be added here -->
                            </div>
                        </div>
                    </div>

                    <!-- Filters and Search -->
                    <div class="neu-card rounded-lg mb-6">
                        <div class="p-6">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                                <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
                                    <!-- Search -->
                                    <div class="relative">
                                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</span>
                                        <input
                                            type="text"
                                            id="productSearch"
                                            placeholder="Search menu items..."
                                            class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 w-64"
                                        />
                                    </div>

                                    <!-- Category Filter -->
                                    <select
                                        id="categoryFilter"
                                        class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                    >
                                        <option value="all">All Categories</option>
                                        <option value="signature-sandwiches">🍗 Signature Sandwiches</option>
                                        <option value="tender-combos">🍗 Tender Combos</option>
                                        <option value="fried-chicken">🍗 Fried Chicken</option>
                                        <option value="waffle-chicken">🧇 Waffle & Chicken</option>
                                        <option value="sides">🍟 Sides</option>
                                        <option value="beverages">🥤 Beverages</option>
                                        <option value="family-deals">👨‍👩‍👧‍👦 Family Deals</option>
                                    </select>

                                    <!-- Sort -->
                                    <select
                                        id="productSort"
                                        class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                    >
                                        <option value="name-asc">Name A-Z</option>
                                        <option value="name-desc">Name Z-A</option>
                                        <option value="price-asc">Price Low-High</option>
                                        <option value="price-desc">Price High-Low</option>
                                        <option value="stock-asc">Stock Low-High</option>
                                        <option value="stock-desc">Stock High-Low</option>
                                    </select>
                                </div>

                                <!-- View Mode Toggle -->
                                <div class="flex items-center space-x-2">
                                    <button id="gridViewBtn" class="bg-primary-600 text-white p-2 rounded-lg">
                                        <span class="text-sm">Grid</span>
                                    </button>
                                    <button id="listViewBtn" class="neu-button p-2 rounded-lg text-gray-700">
                                        <span class="text-sm">List</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <div id="inventoryProductsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        <!-- Product cards will be added here -->
                    </div>
                </div>

                <!-- Image Upload Modal -->
                <div id="imageUploadModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden">
                    <div class="bg-white rounded-lg max-w-md w-full">
                        <div class="flex items-center justify-between p-6 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-900">Add/Change Menu Item Image</h2>
                            <button onclick="hideImageUpload()" class="text-gray-400 hover:text-gray-600">
                                ✕
                            </button>
                        </div>

                        <div class="p-6 space-y-6">
                            <div class="text-center">
                                <div class="w-32 h-32 bg-gray-100 rounded-lg mx-auto mb-4 flex items-center justify-center overflow-hidden">
                                    <img id="currentItemImage" src="" alt="Current item" class="w-full h-full object-cover hidden" />
                                    <div id="currentItemEmoji" class="text-6xl">🍗</div>
                                </div>
                                <p class="font-medium text-gray-900" id="currentItemName">Menu Item</p>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Upload New Image</label>
                                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors">
                                        <input type="file" id="imageUploadInput" accept="image/jpeg,image/jpg,image/png" class="hidden" onchange="handleImageUpload(event)">
                                        <button onclick="document.getElementById('imageUploadInput').click()" class="text-primary-600 hover:text-primary-700">
                                            <div class="text-4xl mb-2">📷</div>
                                            <p class="text-sm font-medium">Click to upload image</p>
                                            <p class="text-xs text-gray-500 mt-1">JPEG, JPG, PNG up to 5MB</p>
                                        </button>
                                    </div>
                                </div>

                                <div class="grid grid-cols-3 gap-2">
                                    <p class="col-span-3 text-sm font-medium text-gray-700 mb-2">Or choose from gallery:</p>
                                    <button onclick="selectGalleryImage('🍗')" class="p-3 border border-gray-200 rounded-lg hover:border-primary-400 text-2xl">
                                        🍗
                                    </button>
                                    <button onclick="selectGalleryImage('🍟')" class="p-3 border border-gray-200 rounded-lg hover:border-primary-400 text-2xl">
                                        🍟
                                    </button>
                                    <button onclick="selectGalleryImage('🥤')" class="p-3 border border-gray-200 rounded-lg hover:border-primary-400 text-2xl">
                                        🥤
                                    </button>
                                    <button onclick="selectGalleryImage('🧇')" class="p-3 border border-gray-200 rounded-lg hover:border-primary-400 text-2xl">
                                        🧇
                                    </button>
                                    <button onclick="selectGalleryImage('🥗')" class="p-3 border border-gray-200 rounded-lg hover:border-primary-400 text-2xl">
                                        🥗
                                    </button>
                                    <button onclick="selectGalleryImage('🌶️')" class="p-3 border border-gray-200 rounded-lg hover:border-primary-400 text-2xl">
                                        🌶️
                                    </button>
                                </div>
                            </div>

                            <div class="flex space-x-3">
                                <button onclick="saveImageChange()" class="flex-1 bg-primary-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-primary-700 transition-colors">
                                    Save Changes
                                </button>
                                <button onclick="hideImageUpload()" class="flex-1 neu-button py-2 px-4 rounded-lg font-medium">
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Placeholder for other tabs -->
                <div id="content-customers" class="tab-content hidden">
                    <!-- Customer Management Header -->
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Customer Management</h1>
                            <p class="text-gray-600">Manage customer relationships and loyalty programs</p>
                        </div>
                        <div class="flex space-x-3">
                            <button onclick="showAddCustomerModal()" class="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700">
                                + Add Customer
                            </button>
                            <button onclick="exportCustomers()" class="neu-button px-4 py-2 rounded-lg text-gray-700 font-medium">
                                📊 Export
                            </button>
                        </div>
                    </div>

                    <!-- Customer Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-blue-100 mr-4">
                                    <span class="text-blue-600 text-xl">👥</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Customers</p>
                                    <p class="text-2xl font-bold text-gray-900" id="totalCustomers">247</p>
                                    <p class="text-xs text-green-600">↗️ +12 this month</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-green-100 mr-4">
                                    <span class="text-green-600 text-xl">⭐</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Loyalty Members</p>
                                    <p class="text-2xl font-bold text-gray-900" id="loyaltyMembers">89</p>
                                    <p class="text-xs text-gray-500">36% of customers</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-purple-100 mr-4">
                                    <span class="text-purple-600 text-xl">💰</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Avg. Order Value</p>
                                    <p class="text-2xl font-bold text-gray-900" id="avgCustomerOrder">$18.45</p>
                                    <p class="text-xs text-green-600">↗️ +$2.30 vs last month</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-orange-100 mr-4">
                                    <span class="text-orange-600 text-xl">🔄</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Repeat Rate</p>
                                    <p class="text-2xl font-bold text-gray-900" id="repeatRate">68%</p>
                                    <p class="text-xs text-green-600">↗️ +5% this month</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search and Filters -->
                    <div class="neu-card rounded-lg mb-6">
                        <div class="p-6">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                                <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
                                    <!-- Search -->
                                    <div class="relative">
                                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</span>
                                        <input
                                            type="text"
                                            id="customerSearch"
                                            placeholder="Search customers..."
                                            class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 w-64"
                                            onkeyup="filterCustomers()"
                                        />
                                    </div>

                                    <!-- Status Filter -->
                                    <select
                                        id="customerStatusFilter"
                                        class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                        onchange="filterCustomers()"
                                    >
                                        <option value="all">All Customers</option>
                                        <option value="active">Active</option>
                                        <option value="loyalty">Loyalty Members</option>
                                        <option value="inactive">Inactive</option>
                                    </select>

                                    <!-- Sort -->
                                    <select
                                        id="customerSort"
                                        class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                                        onchange="filterCustomers()"
                                    >
                                        <option value="name-asc">Name A-Z</option>
                                        <option value="name-desc">Name Z-A</option>
                                        <option value="orders-desc">Most Orders</option>
                                        <option value="spent-desc">Highest Spent</option>
                                        <option value="recent">Most Recent</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Customers Table -->
                    <div class="neu-card rounded-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Spent</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Visit</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="customersTableBody" class="bg-white divide-y divide-gray-200">
                                    <!-- Customer rows will be added here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div id="content-reports" class="tab-content hidden">
                    <!-- Sales Reports Header -->
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">Sales Reports & Analytics</h1>
                            <p class="text-gray-600">Track performance and analyze business trends</p>
                        </div>
                        <div class="flex space-x-3">
                            <select id="reportPeriod" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500" onchange="updateReportPeriod()">
                                <option value="today">Today</option>
                                <option value="week">This Week</option>
                                <option value="month" selected>This Month</option>
                                <option value="quarter">This Quarter</option>
                                <option value="year">This Year</option>
                            </select>
                            <button onclick="exportReports()" class="neu-button px-4 py-2 rounded-lg text-gray-700 font-medium">
                                📊 Export
                            </button>
                        </div>
                    </div>

                    <!-- Key Metrics -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-green-100 mr-4">
                                    <span class="text-green-600 text-xl">💰</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                                    <p class="text-2xl font-bold text-gray-900" id="totalRevenue">$12,847.50</p>
                                    <p class="text-xs text-green-600">↗️ +18.2% vs last month</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-blue-100 mr-4">
                                    <span class="text-blue-600 text-xl">🛒</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Orders</p>
                                    <p class="text-2xl font-bold text-gray-900" id="totalOrdersReport">697</p>
                                    <p class="text-xs text-green-600">↗️ +12.5% vs last month</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-purple-100 mr-4">
                                    <span class="text-purple-600 text-xl">📊</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Avg Order Value</p>
                                    <p class="text-2xl font-bold text-gray-900" id="avgOrderValue">$18.43</p>
                                    <p class="text-xs text-green-600">↗️ +5.1% vs last month</p>
                                </div>
                            </div>
                        </div>

                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-orange-100 mr-4">
                                    <span class="text-orange-600 text-xl">⏱️</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Peak Hour</p>
                                    <p class="text-2xl font-bold text-gray-900" id="peakHour">12-1 PM</p>
                                    <p class="text-xs text-gray-500">Lunch rush</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <!-- Sales Chart -->
                        <div class="neu-card rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-4">Daily Sales Trend</h3>
                            <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                                <div class="text-center">
                                    <div class="text-4xl mb-2">📈</div>
                                    <p class="text-gray-600">Sales chart visualization</p>
                                    <div class="mt-4 space-y-2">
                                        <div class="flex items-center justify-between text-sm">
                                            <span>Mon: $1,245</span>
                                            <div class="w-20 h-2 bg-primary-200 rounded-full">
                                                <div class="w-16 h-2 bg-primary-600 rounded-full"></div>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between text-sm">
                                            <span>Tue: $1,567</span>
                                            <div class="w-20 h-2 bg-primary-200 rounded-full">
                                                <div class="w-full h-2 bg-primary-600 rounded-full"></div>
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between text-sm">
                                            <span>Wed: $1,123</span>
                                            <div class="w-20 h-2 bg-primary-200 rounded-full">
                                                <div class="w-14 h-2 bg-primary-600 rounded-full"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Methods -->
                        <div class="neu-card rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-4">Payment Methods</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <span class="text-xl mr-3">💳</span>
                                        <span class="font-medium">Card</span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <div class="w-24 h-2 bg-gray-200 rounded-full">
                                            <div class="w-16 h-2 bg-blue-500 rounded-full"></div>
                                        </div>
                                        <span class="text-sm font-medium">45%</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <span class="text-xl mr-3">💚</span>
                                        <span class="font-medium">CashApp</span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <div class="w-24 h-2 bg-gray-200 rounded-full">
                                            <div class="w-12 h-2 bg-green-500 rounded-full"></div>
                                        </div>
                                        <span class="text-sm font-medium">28%</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <span class="text-xl mr-3">💵</span>
                                        <span class="font-medium">Cash</span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <div class="w-24 h-2 bg-gray-200 rounded-full">
                                            <div class="w-8 h-2 bg-gray-500 rounded-full"></div>
                                        </div>
                                        <span class="text-sm font-medium">18%</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <span class="text-xl mr-3">🏦</span>
                                        <span class="font-medium">Zelle</span>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <div class="w-24 h-2 bg-gray-200 rounded-full">
                                            <div class="w-6 h-2 bg-blue-600 rounded-full"></div>
                                        </div>
                                        <span class="text-sm font-medium">9%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Top Products and Recent Transactions -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Top Products -->
                        <div class="neu-card rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-4">Top Selling Items</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <span class="text-2xl mr-3">🍗</span>
                                        <div>
                                            <p class="font-medium">H & Chicken Signature Sandwich</p>
                                            <p class="text-sm text-gray-600">127 sold</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold text-green-600">$1,269.73</p>
                                        <p class="text-xs text-gray-500">Revenue</p>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <span class="text-2xl mr-3">🍟</span>
                                        <div>
                                            <p class="font-medium">Seasoned Fries</p>
                                            <p class="text-sm text-gray-600">98 sold</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold text-green-600">$391.02</p>
                                        <p class="text-xs text-gray-500">Revenue</p>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <span class="text-2xl mr-3">🧇</span>
                                        <div>
                                            <p class="font-medium">Classic Chicken & Waffle</p>
                                            <p class="text-sm text-gray-600">76 sold</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold text-green-600">$1,063.24</p>
                                        <p class="text-xs text-gray-500">Revenue</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Large Orders -->
                        <div class="neu-card rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-4">Recent Large Orders</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <p class="font-medium">Order #1247</p>
                                        <p class="text-sm text-gray-600">Family Combo (20pc) + sides</p>
                                        <p class="text-xs text-gray-500">2 hours ago</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold text-green-600">$44.99</p>
                                        <p class="text-xs text-gray-500">💚 CashApp</p>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <p class="font-medium">Order #1246</p>
                                        <p class="text-sm text-gray-600">Multiple sandwiches + drinks</p>
                                        <p class="text-xs text-gray-500">3 hours ago</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold text-green-600">$67.45</p>
                                        <p class="text-xs text-gray-500">💳 Card</p>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <p class="font-medium">Order #1245</p>
                                        <p class="text-sm text-gray-600">Tender Family Pack</p>
                                        <p class="text-xs text-gray-500">4 hours ago</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold text-green-600">$26.99</p>
                                        <p class="text-xs text-gray-500">🏦 Zelle</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="content-settings" class="tab-content hidden">
                    <div class="text-center py-12">
                        <div class="text-6xl mb-4">⚙️</div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">System Settings</h2>
                        <p class="text-gray-600 max-w-md mx-auto">This section would contain system settings, user management, and configuration options.</p>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Payment Notification Toast -->
    <div id="paymentNotification" class="fixed top-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-50 hidden transform transition-all duration-300">
        <div class="flex items-center space-x-3">
            <div id="notificationIcon" class="w-8 h-8 rounded-full flex items-center justify-center">
                <span id="notificationEmoji">💚</span>
            </div>
            <div>
                <p id="notificationTitle" class="font-medium text-gray-900">Payment Received</p>
                <p id="notificationMessage" class="text-sm text-gray-600">CashApp payment confirmed</p>
            </div>
        </div>
    </div>

    <script>
        // Global state
        let currentTab = 'dashboard';
        let cart = [];
        let totalSales = 0;
        let totalOrders = 0;
        let orderNumber = 1001;
        let currentFilter = 'all';

        // H & Chicken Product data
        const products = [
            // Signature Sandwiches
            {
                id: 'signature-chicken-sandwich',
                name: 'H & Chicken Signature Sandwich',
                price: 9.99,
                cost: 4.20,
                emoji: '🍗',
                image: null, // Custom image URL will be stored here
                category: 'signature-sandwiches',
                sku: 'HCS001',
                stock: 35,
                lowStockAlert: 8,
                unit: 'pieces',
                description: 'Our signature fried chicken breast with special sauce, lettuce, and pickles on a brioche bun'
            },
            {
                id: 'spicy-deluxe-sandwich',
                name: 'Spicy Deluxe Sandwich',
                price: 10.99,
                cost: 4.50,
                emoji: '🌶️',
                image: null,
                category: 'signature-sandwiches',
                sku: 'SDS001',
                stock: 28,
                lowStockAlert: 8,
                unit: 'pieces',
                description: 'Spicy fried chicken breast with pepper jack cheese, spicy mayo, and jalapeños'
            },
            {
                id: 'bbq-bacon-sandwich',
                name: 'BBQ Bacon Chicken Sandwich',
                price: 11.99,
                cost: 5.00,
                emoji: '🥓',
                image: null,
                category: 'signature-sandwiches',
                sku: 'BBS001',
                stock: 22,
                lowStockAlert: 6,
                unit: 'pieces',
                description: 'Fried chicken breast with crispy bacon, BBQ sauce, and onion rings'
            },
            // Tender Combos
            {
                id: 'tender-combo-3pc',
                name: '3-Piece Tender Combo',
                price: 8.99,
                cost: 3.20,
                emoji: '🍗',
                image: null,
                category: 'tender-combos',
                sku: 'TC3001',
                stock: 40,
                lowStockAlert: 10,
                unit: 'combos',
                description: 'Three hand-breaded chicken tenders with fries and drink'
            },
            {
                id: 'tender-combo-5pc',
                name: '5-Piece Tender Combo',
                price: 12.99,
                cost: 4.80,
                emoji: '🍗',
                category: 'tender-combos',
                sku: 'TC5001',
                stock: 25,
                lowStockAlert: 8,
                unit: 'combos',
                description: 'Five hand-breaded chicken tenders with fries and drink'
            },
            {
                id: 'buffalo-tender-combo',
                name: 'Buffalo Tender Combo',
                price: 10.99,
                cost: 4.00,
                emoji: '🔥',
                category: 'tender-combos',
                sku: 'BTC001',
                stock: 18,
                lowStockAlert: 6,
                unit: 'combos',
                description: 'Buffalo-style chicken tenders with celery, ranch, fries and drink'
            },
            // Fried Chicken
            {
                id: 'fried-chicken-2pc',
                name: '2-Piece Fried Chicken',
                price: 6.99,
                cost: 2.50,
                emoji: '🍗',
                category: 'fried-chicken',
                sku: 'FC2001',
                stock: 35,
                lowStockAlert: 8,
                unit: 'pieces',
                description: 'Two pieces of our signature fried chicken'
            },
            {
                id: 'fried-chicken-4pc',
                name: '4-Piece Fried Chicken',
                price: 12.99,
                cost: 4.80,
                emoji: '🍗',
                category: 'fried-chicken',
                sku: 'FC4001',
                stock: 22,
                lowStockAlert: 6,
                unit: 'pieces',
                description: 'Four pieces of our signature fried chicken'
            },
            {
                id: 'fried-chicken-8pc',
                name: '8-Piece Fried Chicken',
                price: 24.99,
                cost: 9.50,
                emoji: '🍗',
                category: 'fried-chicken',
                sku: 'FC8001',
                stock: 12,
                lowStockAlert: 4,
                unit: 'pieces',
                description: 'Eight pieces of our signature fried chicken'
            },
            // Waffle & Chicken
            {
                id: 'chicken-waffle-classic',
                name: 'Classic Chicken & Waffle',
                price: 13.99,
                cost: 5.20,
                emoji: '🧇',
                category: 'waffle-chicken',
                sku: 'CW001',
                stock: 20,
                lowStockAlert: 5,
                unit: 'plates',
                description: 'Crispy fried chicken breast served on a golden Belgian waffle with syrup'
            },
            {
                id: 'chicken-waffle-deluxe',
                name: 'Deluxe Chicken & Waffle',
                price: 16.99,
                cost: 6.50,
                emoji: '🧇',
                category: 'waffle-chicken',
                sku: 'CWD001',
                stock: 15,
                lowStockAlert: 4,
                unit: 'plates',
                description: 'Two pieces of fried chicken on Belgian waffle with bacon and syrup'
            },
            // Sides
            {
                id: 'seasoned-fries',
                name: 'Seasoned Fries',
                price: 3.99,
                cost: 0.90,
                emoji: '🍟',
                category: 'sides',
                sku: 'SF001',
                stock: 60,
                lowStockAlert: 15,
                unit: 'servings',
                description: 'Crispy fries seasoned with our signature blend'
            },
            {
                id: 'mac-cheese',
                name: 'Mac & Cheese',
                price: 4.99,
                cost: 1.20,
                emoji: '🧀',
                category: 'sides',
                sku: 'MC001',
                stock: 2,
                lowStockAlert: 8,
                unit: 'servings',
                description: 'Creamy three-cheese macaroni and cheese'
            },
            {
                id: 'coleslaw',
                name: 'Coleslaw',
                price: 2.99,
                cost: 0.60,
                emoji: '🥗',
                category: 'sides',
                sku: 'CS001',
                stock: 35,
                lowStockAlert: 10,
                unit: 'servings',
                description: 'Fresh cabbage slaw with creamy dressing'
            },
            {
                id: 'mashed-potatoes',
                name: 'Mashed Potatoes & Gravy',
                price: 3.99,
                cost: 0.80,
                emoji: '🥔',
                category: 'sides',
                sku: 'MP001',
                stock: 25,
                lowStockAlert: 8,
                unit: 'servings',
                description: 'Creamy mashed potatoes with rich brown gravy'
            },
            {
                id: 'corn-on-cob',
                name: 'Corn on the Cob',
                price: 2.99,
                cost: 0.70,
                emoji: '🌽',
                category: 'sides',
                sku: 'COC001',
                stock: 30,
                lowStockAlert: 10,
                unit: 'pieces',
                description: 'Fresh corn on the cob with butter'
            },
            {
                id: 'biscuit',
                name: 'Fresh Biscuit',
                price: 1.99,
                cost: 0.40,
                emoji: '🥖',
                category: 'sides',
                sku: 'FB001',
                stock: 5,
                lowStockAlert: 12,
                unit: 'pieces',
                description: 'Warm, flaky biscuit with honey butter'
            },
            // Beverages
            {
                id: 'sweet-tea',
                name: 'Sweet Tea',
                price: 2.49,
                cost: 0.30,
                emoji: '🧊',
                category: 'beverages',
                sku: 'ST001',
                stock: 100,
                lowStockAlert: 20,
                unit: 'cups',
                description: 'Southern-style sweet iced tea'
            },
            {
                id: 'fresh-lemonade',
                name: 'Fresh Lemonade',
                price: 2.99,
                cost: 0.50,
                emoji: '🍋',
                category: 'beverages',
                sku: 'FL001',
                stock: 85,
                lowStockAlert: 15,
                unit: 'cups',
                description: 'Freshly squeezed lemonade'
            },
            {
                id: 'soft-drink',
                name: 'Soft Drink',
                price: 2.29,
                cost: 0.25,
                emoji: '🥤',
                category: 'beverages',
                sku: 'SD001',
                stock: 120,
                lowStockAlert: 25,
                unit: 'cups',
                description: 'Choice of Coke, Sprite, or Orange'
            },
            // Family Deals
            {
                id: 'family-feast-12pc',
                name: 'Family Feast (12pc)',
                price: 29.99,
                cost: 14.50,
                emoji: '👨‍👩‍👧‍👦',
                category: 'family-deals',
                sku: 'FF12001',
                stock: 8,
                lowStockAlert: 3,
                unit: 'meals',
                description: '12 pieces of mixed fried chicken with 3 large sides and 6 biscuits'
            },
            {
                id: 'family-combo-20pc',
                name: 'Family Combo (20pc)',
                price: 44.99,
                cost: 22.00,
                emoji: '👨‍👩‍👧‍👦',
                category: 'family-deals',
                sku: 'FC20001',
                stock: 5,
                lowStockAlert: 2,
                unit: 'meals',
                description: '20 pieces of mixed fried chicken with 4 large sides and 8 biscuits'
            },
            {
                id: 'tender-family-pack',
                name: 'Tender Family Pack',
                price: 26.99,
                cost: 12.80,
                emoji: '👨‍👩‍👧‍👦',
                category: 'family-deals',
                sku: 'TFP001',
                stock: 10,
                lowStockAlert: 3,
                unit: 'meals',
                description: '15 chicken tenders with 3 large sides and 6 biscuits'
            }
        ];

        // Initialize the app
        function init() {
            // Add image property to all products if not present
            products.forEach(product => {
                if (!product.hasOwnProperty('image')) {
                    product.image = null;
                }
            });

            document.getElementById('currentDate').textContent = new Date().toLocaleDateString();
            updateDashboardStats();
            renderProducts();
            renderInventoryProducts();
            renderLowStockAlert();
            updateCartDisplay();
            initializeNFCAnimation();
            initializeBrandLogos();
            initializePlatterImage();
            renderCustomers();
        }

        // Login function
        function login(event) {
            event.preventDefault();
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');
            init();
        }

        // Tab management
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Remove active class from all tabs
            document.querySelectorAll('[id^="tab-"]').forEach(tab => {
                tab.classList.remove('tab-active');
                tab.classList.add('text-gray-700', 'hover:bg-gray-100');
            });

            // Show selected tab content
            document.getElementById(`content-${tabName}`).classList.remove('hidden');

            // Add active class to selected tab
            const activeTab = document.getElementById(`tab-${tabName}`);
            activeTab.classList.add('tab-active');
            activeTab.classList.remove('text-gray-700', 'hover:bg-gray-100');

            currentTab = tabName;

            // Refresh data when switching tabs
            if (tabName === 'dashboard') {
                updateDashboardStats();
            } else if (tabName === 'pos') {
                renderProducts();
            } else if (tabName === 'products') {
                renderInventoryProducts();
                renderLowStockAlert();
            } else if (tabName === 'customers') {
                renderCustomers();
            } else if (tabName === 'reports') {
                updateReportPeriod();
            }
        }

        // Dashboard functions
        function updateDashboardStats() {
            document.getElementById('todaySales').textContent = `$${totalSales.toFixed(2)}`;
            document.getElementById('todayOrders').textContent = totalOrders;
            document.getElementById('todayCustomers').textContent = totalOrders; // Assuming 1 customer per order
            document.getElementById('newCustomers').textContent = Math.floor(totalOrders * 0.3);
            document.getElementById('avgOrder').textContent = totalOrders > 0 ? (totalSales / totalOrders).toFixed(2) : '0.00';
        }

        // POS functions
        function renderProducts() {
            const grid = document.getElementById('productsGrid');
            const filteredProducts = products.filter(product =>
                currentFilter === 'all' || product.category === currentFilter
            );

            grid.innerHTML = filteredProducts.map(product => `
                <div class="neu-card product-card rounded-lg p-4 cursor-pointer relative" onclick="addToCart('${product.id}')">
                    <div class="text-center">
                        <div class="w-16 h-16 mx-auto mb-2 flex items-center justify-center relative">
                            ${product.image ?
                                `<img src="${product.image}" alt="${product.name}" class="w-full h-full object-cover rounded-lg" />` :
                                `<div class="text-4xl">${product.emoji}</div>`
                            }
                            <button onclick="event.stopPropagation(); showImageUpload('${product.id}')" class="absolute -bottom-1 -right-1 bg-primary-600 hover:bg-primary-700 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs z-10 shadow-lg add-image-btn">
                                +
                            </button>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-1">${product.name}</h3>
                        <p class="text-sm text-gray-600 mb-3 line-clamp-2">${product.description}</p>
                        <p class="text-lg font-bold text-primary-600">$${product.price.toFixed(2)}</p>
                        ${product.stock <= product.lowStockAlert ?
                            `<p class="text-xs text-red-600 mt-1">Low Stock: ${product.stock}</p>` :
                            `<p class="text-xs text-green-600 mt-1">In Stock: ${product.stock}</p>`
                        }
                    </div>
                </div>
            `).join('');
        }

        function filterProducts(category) {
            currentFilter = category;

            // Update active filter button
            document.querySelectorAll('.category-filter').forEach(btn => {
                btn.classList.remove('active', 'bg-primary-600', 'text-white');
                btn.classList.add('neu-button');
            });

            event.target.classList.add('active', 'bg-primary-600', 'text-white');
            event.target.classList.remove('neu-button');

            renderProducts();
        }

        function addToCart(productId) {
            const product = products.find(p => p.id === productId);
            if (!product || product.stock <= 0) {
                alert('This item is out of stock!');
                return;
            }

            const existingItem = cart.find(item => item.id === productId);
            if (existingItem) {
                if (existingItem.quantity < product.stock) {
                    existingItem.quantity++;
                } else {
                    alert('Not enough stock available!');
                    return;
                }
            } else {
                cart.push({
                    id: productId,
                    name: product.name,
                    price: product.price,
                    emoji: product.emoji,
                    quantity: 1
                });
            }

            updateCartDisplay();
        }

        function removeFromCart(productId) {
            const itemIndex = cart.findIndex(item => item.id === productId);
            if (itemIndex > -1) {
                if (cart[itemIndex].quantity > 1) {
                    cart[itemIndex].quantity--;
                } else {
                    cart.splice(itemIndex, 1);
                }
            }
            updateCartDisplay();
        }

        function updateCartDisplay() {
            const cartItems = document.getElementById('cartItems');
            const cartCount = document.getElementById('cartCount');
            const cartSubtotal = document.getElementById('cartSubtotal');
            const cartTax = document.getElementById('cartTax');
            const cartTotal = document.getElementById('cartTotal');
            const checkoutBtn = document.getElementById('checkoutBtn');

            if (cart.length === 0) {
                cartItems.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <span class="text-4xl">🛒</span>
                        <p class="mt-2">Your cart is empty</p>
                        <p class="text-sm">Add items from the menu</p>
                    </div>
                `;
                checkoutBtn.disabled = true;
            } else {
                cartItems.innerHTML = cart.map(item => `
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <span class="text-xl">${item.emoji}</span>
                            <div>
                                <p class="font-medium">${item.name}</p>
                                <p class="text-sm text-gray-600">$${item.price.toFixed(2)} each</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="removeFromCart('${item.id}')" class="w-6 h-6 rounded-full bg-red-100 text-red-600 text-sm">-</button>
                            <span class="font-medium">${item.quantity}</span>
                            <button onclick="addToCart('${item.id}')" class="w-6 h-6 rounded-full bg-green-100 text-green-600 text-sm">+</button>
                        </div>
                    </div>
                `).join('');
                checkoutBtn.disabled = false;
            }

            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const tax = subtotal * 0.08;
            const total = subtotal + tax;
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);

            cartCount.textContent = totalItems;
            cartSubtotal.textContent = `$${subtotal.toFixed(2)}`;
            cartTax.textContent = `$${tax.toFixed(2)}`;
            cartTotal.textContent = `$${total.toFixed(2)}`;
        }

        function clearCart() {
            cart = [];
            updateCartDisplay();
        }

        function showCheckout() {
            if (cart.length === 0) return;

            const modal = document.getElementById('checkoutModal');
            const checkoutItems = document.getElementById('checkoutItems');
            const checkoutSubtotal = document.getElementById('checkoutSubtotal');
            const checkoutTax = document.getElementById('checkoutTax');
            const checkoutTotal = document.getElementById('checkoutTotal');

            checkoutItems.innerHTML = cart.map(item => `
                <div class="flex justify-between text-sm">
                    <span>${item.emoji} ${item.name} x${item.quantity}</span>
                    <span>$${(item.price * item.quantity).toFixed(2)}</span>
                </div>
            `).join('');

            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const tax = subtotal * 0.08;
            const total = subtotal + tax;

            checkoutSubtotal.textContent = `$${subtotal.toFixed(2)}`;
            checkoutTax.textContent = `$${tax.toFixed(2)}`;
            checkoutTotal.textContent = `$${total.toFixed(2)}`;

            modal.classList.remove('hidden');
        }

        function hideCheckout() {
            document.getElementById('checkoutModal').classList.add('hidden');
        }

        function processPayment(method) {
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) * 1.08;

            // Show appropriate notification (NFC or regular)
            if (method.includes('NFC')) {
                showNFCPaymentNotification(method, total);
            } else {
                showPaymentNotification(method, total);
            }

            // Update inventory
            cart.forEach(item => {
                const product = products.find(p => p.id === item.id);
                if (product) {
                    product.stock -= item.quantity;
                }
            });

            // Update stats
            totalSales += total;
            totalOrders++;

            // Add to recent orders
            addRecentOrder(cart, total, method);

            // Clear cart
            cart = [];

            // Show success modal
            document.getElementById('checkoutModal').classList.add('hidden');
            document.getElementById('successOrderNumber').textContent = orderNumber++;
            document.getElementById('successPaymentMethod').textContent = method.charAt(0).toUpperCase() + method.slice(1);
            document.getElementById('paymentSuccessModal').classList.remove('hidden');

            // Update displays
            updateCartDisplay();
            updateDashboardStats();
            renderProducts();
            renderInventoryProducts();
            renderLowStockAlert();
        }

        function showPaymentNotification(method, amount) {
            const notification = document.getElementById('paymentNotification');
            const icon = document.getElementById('notificationIcon');
            const emoji = document.getElementById('notificationEmoji');
            const title = document.getElementById('notificationTitle');
            const message = document.getElementById('notificationMessage');

            // Set notification content based on payment method
            const notificationData = {
                'CashApp': {
                    emoji: '💚',
                    bgColor: 'bg-green-100',
                    title: 'CashApp Payment Received',
                    message: `$${amount.toFixed(2)} received via CashApp`
                },
                'Zelle': {
                    emoji: '🏦',
                    bgColor: 'bg-blue-100',
                    title: 'Zelle Payment Confirmed',
                    message: `$${amount.toFixed(2)} received via Zelle`
                },
                'Cash': {
                    emoji: '💵',
                    bgColor: 'bg-gray-100',
                    title: 'Cash Payment Received',
                    message: `$${amount.toFixed(2)} cash payment`
                },
                'Card': {
                    emoji: '💳',
                    bgColor: 'bg-purple-100',
                    title: 'Card Payment Processed',
                    message: `$${amount.toFixed(2)} charged to card`
                }
            };

            const data = notificationData[method] || notificationData['Cash'];

            emoji.textContent = data.emoji;
            icon.className = `w-8 h-8 rounded-full flex items-center justify-center ${data.bgColor}`;
            title.textContent = data.title;
            message.textContent = data.message;

            // Show notification
            notification.classList.remove('hidden');
            notification.style.transform = 'translateX(100%)';

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Auto hide after 4 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.classList.add('hidden');
                }, 300);
            }, 4000);
        }

        // NFC Detection Functions
        function startNFCDetection(paymentType) {
            stopNFCDetection(); // Clear any existing interval

            const statusElement = paymentType === 'cashapp' ?
                document.getElementById('nfcStatus') :
                document.getElementById('zelleNfcStatus');

            let dotCount = 0;
            nfcCheckInterval = setInterval(() => {
                dotCount = (dotCount + 1) % 4;
                const dots = '.'.repeat(dotCount);
                statusElement.textContent = `Scanning for NFC device${dots}`;
            }, 500);
        }

        function stopNFCDetection() {
            if (nfcCheckInterval) {
                clearInterval(nfcCheckInterval);
                nfcCheckInterval = null;
            }
        }

        // Enhanced notification for NFC payments
        function showNFCPaymentNotification(method, amount) {
            const notification = document.getElementById('paymentNotification');
            const icon = document.getElementById('notificationIcon');
            const emoji = document.getElementById('notificationEmoji');
            const title = document.getElementById('notificationTitle');
            const message = document.getElementById('notificationMessage');

            // Set notification content for NFC payments
            const notificationData = {
                'CashApp NFC': {
                    emoji: '📡',
                    bgColor: 'bg-green-100',
                    title: 'NFC Payment Successful',
                    message: `$${amount.toFixed(2)} via CashApp NFC`
                },
                'Zelle NFC': {
                    emoji: '📡',
                    bgColor: 'bg-blue-100',
                    title: 'NFC Payment Confirmed',
                    message: `$${amount.toFixed(2)} via Zelle NFC`
                }
            };

            const data = notificationData[method] || {
                emoji: '💚',
                bgColor: 'bg-green-100',
                title: 'Payment Received',
                message: `$${amount.toFixed(2)} payment confirmed`
            };

            emoji.textContent = data.emoji;
            icon.className = `w-8 h-8 rounded-full flex items-center justify-center ${data.bgColor}`;
            title.textContent = data.title;
            message.textContent = data.message;

            // Show notification with enhanced animation for NFC
            notification.classList.remove('hidden');
            notification.style.transform = 'translateX(100%) scale(0.8)';

            // Animate in with bounce effect for NFC
            setTimeout(() => {
                notification.style.transform = 'translateX(0) scale(1)';
                notification.style.transition = 'all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
            }, 100);

            // Auto hide after 5 seconds (longer for NFC to show the technology)
            setTimeout(() => {
                notification.style.transform = 'translateX(100%) scale(0.8)';
                setTimeout(() => {
                    notification.classList.add('hidden');
                    notification.style.transition = 'all 0.3s ease';
                }, 300);
            }, 5000);
        }

        // Global variables for NFC state
        let currentCashAppMethod = 'nfc';
        let currentZelleMethod = 'nfc';
        let nfcCheckInterval = null;
        let currentEditingProductId = null;
        let tempImageData = null;
        let currentCustomerFilter = 'all';

        // Customer data
        const customers = [
            {
                id: 'cust001',
                name: 'Marcus Johnson',
                email: '<EMAIL>',
                phone: '(*************',
                orders: 23,
                totalSpent: 487.65,
                status: 'loyalty',
                lastVisit: '2024-01-15',
                joinDate: '2023-08-12',
                favoriteItem: 'Signature Sandwich',
                loyaltyPoints: 245
            },
            {
                id: 'cust002',
                name: 'Sarah Williams',
                email: '<EMAIL>',
                phone: '(*************',
                orders: 15,
                totalSpent: 298.50,
                status: 'active',
                lastVisit: '2024-01-14',
                joinDate: '2023-10-05',
                favoriteItem: 'Chicken & Waffle',
                loyaltyPoints: 0
            },
            {
                id: 'cust003',
                name: 'David Chen',
                email: '<EMAIL>',
                phone: '(*************',
                orders: 31,
                totalSpent: 672.80,
                status: 'loyalty',
                lastVisit: '2024-01-13',
                joinDate: '2023-06-20',
                favoriteItem: 'Family Feast',
                loyaltyPoints: 336
            },
            {
                id: 'cust004',
                name: 'Jennifer Davis',
                email: '<EMAIL>',
                phone: '(*************',
                orders: 8,
                totalSpent: 156.40,
                status: 'active',
                lastVisit: '2024-01-12',
                joinDate: '2023-12-01',
                favoriteItem: 'Spicy Deluxe',
                loyaltyPoints: 0
            },
            {
                id: 'cust005',
                name: 'Michael Brown',
                email: '<EMAIL>',
                phone: '(*************',
                orders: 45,
                totalSpent: 892.30,
                status: 'loyalty',
                lastVisit: '2024-01-11',
                joinDate: '2023-04-15',
                favoriteItem: 'Tender Combo',
                loyaltyPoints: 446
            },
            {
                id: 'cust006',
                name: 'Lisa Rodriguez',
                email: '<EMAIL>',
                phone: '(*************',
                orders: 3,
                totalSpent: 67.45,
                status: 'inactive',
                lastVisit: '2023-12-20',
                joinDate: '2023-11-10',
                favoriteItem: 'Seasoned Fries',
                loyaltyPoints: 0
            }
        ];

        // Initialize NFC GIF with fallback
        function initializeNFCAnimation() {
            const nfcImages = document.querySelectorAll('.nfc-gif');
            nfcImages.forEach(img => {
                img.onerror = function() {
                    // Fallback to CSS animation if GIF not found
                    this.style.display = 'none';
                    const parent = this.parentElement;
                    parent.innerHTML = `
                        <div class="w-20 h-20 border-4 border-green-500 rounded-full animate-pulse"></div>
                        <div class="absolute w-12 h-12 border-2 border-green-400 rounded-full animate-ping"></div>
                        <div class="absolute text-2xl">📡</div>
                    `;
                };
            });
        }

        // Initialize brand logos
        function initializeBrandLogos() {
            const logoImages = document.querySelectorAll('.brand-logo');
            logoImages.forEach(img => {
                // Test if image loads successfully
                const testImg = new Image();
                testImg.onload = function() {
                    // Image loaded successfully, keep it visible
                    img.style.display = 'block';
                };
                testImg.onerror = function() {
                    // Image failed to load, show fallback
                    img.style.display = 'none';
                    const fallback = img.nextElementSibling;
                    if (fallback && fallback.classList.contains('logo-fallback')) {
                        fallback.style.display = 'flex';
                    }
                };
                testImg.src = img.src;
            });
        }

        // Initialize dashboard platter image
        function initializePlatterImage() {
            const platterImg = document.querySelector('img[src="H_Chicken_Platter.png"]');
            if (platterImg) {
                const testImg = new Image();
                testImg.onload = function() {
                    // Image loaded successfully
                    platterImg.style.display = 'block';
                };
                testImg.onerror = function() {
                    // Image failed to load, show fallback
                    platterImg.style.display = 'none';
                    const fallback = platterImg.nextElementSibling;
                    if (fallback) {
                        fallback.style.display = 'flex';
                    }
                };
                testImg.src = platterImg.src;
            }
        }

        // CashApp Payment Functions
        function showCashAppPayment() {
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) * 1.08;
            document.getElementById('cashAppAmount').textContent = total.toFixed(2);
            document.getElementById('cashAppAmountNFC').textContent = total.toFixed(2);
            document.getElementById('cashAppStatus').innerHTML = '⏳ Waiting for payment...';
            document.getElementById('cashAppStatus').className = 'text-sm text-yellow-600';

            // Reset to NFC mode by default
            switchCashAppMethod('nfc');

            // Hide checkout modal and show CashApp modal
            document.getElementById('checkoutModal').classList.add('hidden');
            document.getElementById('cashAppModal').classList.remove('hidden');

            // Start NFC detection simulation
            startNFCDetection('cashapp');

            // Simulate QR code generation (for QR mode)
            setTimeout(() => {
                document.getElementById('cashAppQR').innerHTML = `
                    <div class="bg-white p-2 rounded">
                        <div class="grid grid-cols-8 gap-px">
                            ${Array(64).fill().map(() =>
                                `<div class="w-2 h-2 ${Math.random() > 0.5 ? 'bg-black' : 'bg-white'}"></div>`
                            ).join('')}
                        </div>
                    </div>
                `;
            }, 500);
        }

        function switchCashAppMethod(method) {
            currentCashAppMethod = method;

            // Update tab appearance
            const nfcTab = document.getElementById('cashAppNFCTab');
            const qrTab = document.getElementById('cashAppQRTab');
            const simulateBtn = document.getElementById('simulateCashAppBtn');

            if (method === 'nfc') {
                nfcTab.className = 'flex-1 py-2 px-4 rounded-md text-sm font-medium bg-green-500 text-white transition-colors';
                qrTab.className = 'flex-1 py-2 px-4 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors';

                document.getElementById('cashAppNFCSection').classList.remove('hidden');
                document.getElementById('cashAppQRSection').classList.add('hidden');
                document.getElementById('nfcInstructions').classList.remove('hidden');
                document.getElementById('qrInstructions').classList.add('hidden');

                simulateBtn.innerHTML = '📡 Simulate NFC Tap';
                simulateBtn.onclick = simulateCashAppNFC;

                startNFCDetection('cashapp');
            } else {
                qrTab.className = 'flex-1 py-2 px-4 rounded-md text-sm font-medium bg-green-500 text-white transition-colors';
                nfcTab.className = 'flex-1 py-2 px-4 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors';

                document.getElementById('cashAppQRSection').classList.remove('hidden');
                document.getElementById('cashAppNFCSection').classList.add('hidden');
                document.getElementById('qrInstructions').classList.remove('hidden');
                document.getElementById('nfcInstructions').classList.add('hidden');

                simulateBtn.innerHTML = '✓ Simulate QR Payment';
                simulateBtn.onclick = simulateCashAppPayment;

                stopNFCDetection();
            }
        }

        function hideCashAppPayment() {
            stopNFCDetection();
            document.getElementById('cashAppModal').classList.add('hidden');
            document.getElementById('checkoutModal').classList.remove('hidden');
        }

        function simulateCashAppNFC() {
            // Show processing indicator in header
            document.getElementById('paymentProcessingIndicator').classList.remove('hidden');
            document.getElementById('paymentProcessingText').textContent = 'Processing CashApp NFC Payment...';

            // Add visual effect to NFC container
            const nfcContainer = document.querySelector('#cashAppNFCSection .nfc-container');
            nfcContainer.style.boxShadow = '0 0 20px rgba(34, 197, 94, 0.5)';
            nfcContainer.style.transform = 'scale(1.05)';
            nfcContainer.style.transition = 'all 0.3s ease';

            // Update NFC status
            document.getElementById('nfcStatus').textContent = 'NFC device detected...';

            // Simulate NFC handshake
            setTimeout(() => {
                document.getElementById('nfcStatus').textContent = 'Authenticating with CashApp...';
                nfcContainer.style.boxShadow = '0 0 30px rgba(34, 197, 94, 0.7)';

                setTimeout(() => {
                    document.getElementById('nfcStatus').textContent = 'Payment authorized!';
                    document.getElementById('cashAppStatus').innerHTML = '✅ NFC Payment received!';
                    document.getElementById('cashAppStatus').className = 'text-sm text-green-600';
                    nfcContainer.style.boxShadow = '0 0 40px rgba(34, 197, 94, 1)';

                    // Complete the payment after a short delay
                    setTimeout(() => {
                        stopNFCDetection();
                        nfcContainer.style.boxShadow = '';
                        nfcContainer.style.transform = '';
                        document.getElementById('cashAppModal').classList.add('hidden');
                        document.getElementById('paymentProcessingIndicator').classList.add('hidden');
                        processPayment('CashApp NFC');
                    }, 1000);
                }, 1500);
            }, 1000);
        }

        function simulateCashAppPayment() {
            // Show processing indicator in header
            document.getElementById('paymentProcessingIndicator').classList.remove('hidden');
            document.getElementById('paymentProcessingText').textContent = 'Processing CashApp Payment...';

            // Update status to processing
            document.getElementById('cashAppStatus').innerHTML = '🔄 Processing payment...';
            document.getElementById('cashAppStatus').className = 'text-sm text-blue-600';

            // Simulate payment processing delay
            setTimeout(() => {
                document.getElementById('cashAppStatus').innerHTML = '✅ Payment received!';
                document.getElementById('cashAppStatus').className = 'text-sm text-green-600';

                // Complete the payment after a short delay
                setTimeout(() => {
                    document.getElementById('cashAppModal').classList.add('hidden');
                    document.getElementById('paymentProcessingIndicator').classList.add('hidden');
                    processPayment('CashApp');
                }, 1000);
            }, 2000);
        }

        // Zelle Payment Functions
        function showZellePayment() {
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) * 1.08;
            document.getElementById('zelleAmount').textContent = total.toFixed(2);
            document.getElementById('zelleAmountNFC').textContent = total.toFixed(2);
            document.getElementById('zelleStatus').innerHTML = '⏳ Waiting for payment...';
            document.getElementById('zelleStatus').className = 'text-sm text-yellow-600';

            // Reset to NFC mode by default
            switchZelleMethod('nfc');

            // Hide checkout modal and show Zelle modal
            document.getElementById('checkoutModal').classList.add('hidden');
            document.getElementById('zelleModal').classList.remove('hidden');

            // Start NFC detection simulation
            startNFCDetection('zelle');
        }

        function switchZelleMethod(method) {
            currentZelleMethod = method;

            // Update tab appearance
            const nfcTab = document.getElementById('zelleNFCTab');
            const emailTab = document.getElementById('zelleEmailTab');
            const simulateBtn = document.getElementById('simulateZelleBtn');

            if (method === 'nfc') {
                nfcTab.className = 'flex-1 py-2 px-4 rounded-md text-sm font-medium bg-blue-600 text-white transition-colors';
                emailTab.className = 'flex-1 py-2 px-4 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors';

                document.getElementById('zelleNFCSection').classList.remove('hidden');
                document.getElementById('zelleEmailSection').classList.add('hidden');
                document.getElementById('zelleNfcInstructions').classList.remove('hidden');
                document.getElementById('zelleEmailInstructions').classList.add('hidden');

                simulateBtn.innerHTML = '📡 Simulate NFC Tap';
                simulateBtn.onclick = simulateZelleNFC;

                startNFCDetection('zelle');
            } else {
                emailTab.className = 'flex-1 py-2 px-4 rounded-md text-sm font-medium bg-blue-600 text-white transition-colors';
                nfcTab.className = 'flex-1 py-2 px-4 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors';

                document.getElementById('zelleEmailSection').classList.remove('hidden');
                document.getElementById('zelleNFCSection').classList.add('hidden');
                document.getElementById('zelleEmailInstructions').classList.remove('hidden');
                document.getElementById('zelleNfcInstructions').classList.add('hidden');

                simulateBtn.innerHTML = '✓ Simulate Email Payment';
                simulateBtn.onclick = simulateZellePayment;

                stopNFCDetection();
            }
        }

        function hideZellePayment() {
            stopNFCDetection();
            document.getElementById('zelleModal').classList.add('hidden');
            document.getElementById('checkoutModal').classList.remove('hidden');
        }

        function simulateZelleNFC() {
            // Show processing indicator in header
            document.getElementById('paymentProcessingIndicator').classList.remove('hidden');
            document.getElementById('paymentProcessingText').textContent = 'Processing Zelle NFC Payment...';

            // Add visual effect to NFC container
            const nfcContainer = document.querySelector('#zelleNFCSection .nfc-container');
            nfcContainer.style.boxShadow = '0 0 20px rgba(37, 99, 235, 0.5)';
            nfcContainer.style.transform = 'scale(1.05)';
            nfcContainer.style.transition = 'all 0.3s ease';

            // Update NFC status
            document.getElementById('zelleNfcStatus').textContent = 'NFC device detected...';

            // Simulate NFC handshake with bank verification
            setTimeout(() => {
                document.getElementById('zelleNfcStatus').textContent = 'Connecting to bank...';
                nfcContainer.style.boxShadow = '0 0 25px rgba(37, 99, 235, 0.6)';

                setTimeout(() => {
                    document.getElementById('zelleNfcStatus').textContent = 'Bank authentication...';
                    document.getElementById('zelleStatus').innerHTML = '🏦 Bank verification in progress';
                    document.getElementById('zelleStatus').className = 'text-sm text-blue-600';
                    nfcContainer.style.boxShadow = '0 0 30px rgba(37, 99, 235, 0.7)';

                    setTimeout(() => {
                        document.getElementById('zelleNfcStatus').textContent = 'Payment authorized!';
                        document.getElementById('zelleStatus').innerHTML = '✅ Zelle NFC Payment confirmed!';
                        document.getElementById('zelleStatus').className = 'text-sm text-green-600';
                        nfcContainer.style.boxShadow = '0 0 40px rgba(34, 197, 94, 1)';

                        // Complete the payment after a short delay
                        setTimeout(() => {
                            stopNFCDetection();
                            nfcContainer.style.boxShadow = '';
                            nfcContainer.style.transform = '';
                            document.getElementById('zelleModal').classList.add('hidden');
                            document.getElementById('paymentProcessingIndicator').classList.add('hidden');
                            processPayment('Zelle NFC');
                        }, 1000);
                    }, 2000);
                }, 1500);
            }, 1000);
        }

        function simulateZellePayment() {
            // Show processing indicator in header
            document.getElementById('paymentProcessingIndicator').classList.remove('hidden');
            document.getElementById('paymentProcessingText').textContent = 'Processing Zelle Payment...';

            // Update status to processing
            document.getElementById('zelleStatus').innerHTML = '🔄 Verifying with bank...';
            document.getElementById('zelleStatus').className = 'text-sm text-blue-600';

            // Simulate bank verification delay (longer than CashApp)
            setTimeout(() => {
                document.getElementById('zelleStatus').innerHTML = '🏦 Bank confirmation received';
                document.getElementById('zelleStatus').className = 'text-sm text-blue-600';

                setTimeout(() => {
                    document.getElementById('zelleStatus').innerHTML = '✅ Payment confirmed!';
                    document.getElementById('zelleStatus').className = 'text-sm text-green-600';

                    // Complete the payment after a short delay
                    setTimeout(() => {
                        document.getElementById('zelleModal').classList.add('hidden');
                        document.getElementById('paymentProcessingIndicator').classList.add('hidden');
                        processPayment('Zelle');
                    }, 1000);
                }, 1500);
            }, 3000);
        }

        function closePaymentSuccess() {
            document.getElementById('paymentSuccessModal').classList.add('hidden');
        }

        function addRecentOrder(orderItems, total, paymentMethod = 'Cash') {
            const recentOrdersList = document.getElementById('recentOrdersList');
            const orderDiv = document.createElement('div');
            orderDiv.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';

            // Payment method icons
            const paymentIcons = {
                'Cash': '💵',
                'CashApp': '💚',
                'CashApp NFC': '📡💚',
                'Zelle': '🏦',
                'Zelle NFC': '📡🏦',
                'Card': '💳'
            };

            orderDiv.innerHTML = `
                <div>
                    <p class="font-medium">Order #${orderNumber}</p>
                    <p class="text-sm text-gray-600">${orderItems.length} items • Just now</p>
                    <p class="text-xs text-gray-500">${paymentIcons[paymentMethod]} ${paymentMethod}</p>
                </div>
                <div class="text-right">
                    <p class="font-bold text-green-600">$${total.toFixed(2)}</p>
                    <p class="text-xs text-gray-500">Completed</p>
                </div>
            `;

            // Remove empty state if it exists
            const emptyState = recentOrdersList.querySelector('.text-center');
            if (emptyState) {
                emptyState.remove();
            }

            recentOrdersList.insertBefore(orderDiv, recentOrdersList.firstChild);

            // Keep only last 5 orders
            while (recentOrdersList.children.length > 5) {
                recentOrdersList.removeChild(recentOrdersList.lastChild);
            }
        }

        // Product Management functions
        function renderInventoryProducts() {
            const grid = document.getElementById('inventoryProductsGrid');

            grid.innerHTML = products.map(product => {
                const isLowStock = product.stock <= product.lowStockAlert;
                const isOutOfStock = product.stock === 0;

                return `
                    <div class="neu-card rounded-lg overflow-hidden">
                        <div class="h-48 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center relative overflow-hidden">
                            ${product.image ?
                                `<img src="${product.image}" alt="${product.name}" class="w-full h-full object-cover" />` :
                                `<div class="text-6xl">${product.emoji}</div>`
                            }
                            <button onclick="showImageUpload('${product.id}')" class="absolute bottom-2 right-2 bg-primary-600 hover:bg-primary-700 text-white rounded-full w-8 h-8 flex items-center justify-center text-lg shadow-lg add-image-btn">
                                +
                            </button>
                            <div class="absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium ${
                                isOutOfStock ? 'bg-red-100 text-red-800' :
                                isLowStock ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                            }">
                                ${isOutOfStock ? 'Out of Stock' : isLowStock ? 'Low Stock' : 'In Stock'}
                            </div>
                            <div class="absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-700">
                                ${getCategoryIcon(product.category)} ${getCategoryName(product.category)}
                            </div>
                        </div>

                        <div class="p-4">
                            <div class="mb-3">
                                <h3 class="font-semibold text-gray-900 mb-1">${product.name}</h3>
                                <p class="text-sm text-gray-600">${product.description}</p>
                            </div>

                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <p class="text-lg font-bold text-primary-600">$${product.price.toFixed(2)}</p>
                                    <p class="text-xs text-gray-500">Cost: $${product.cost.toFixed(2)}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-xs text-gray-500">SKU</p>
                                    <p class="text-sm font-mono">${product.sku}</p>
                                </div>
                            </div>

                            <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700">Inventory</span>
                                    <button onclick="adjustInventory('${product.id}')" class="text-xs text-primary-600 hover:text-primary-700">
                                        Adjust
                                    </button>
                                </div>

                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">Current Stock:</span>
                                    <span class="font-medium ${isLowStock ? 'text-red-600' : 'text-gray-900'}">
                                        ${product.stock} ${product.unit}
                                    </span>
                                </div>

                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">Alert Level:</span>
                                    <span class="text-gray-900">${product.lowStockAlert}</span>
                                </div>

                                <div class="mt-2">
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="h-2 rounded-full transition-all ${
                                            isOutOfStock ? 'bg-red-500' :
                                            isLowStock ? 'bg-yellow-500' : 'bg-green-500'
                                        }" style="width: ${Math.min(100, (product.stock / (product.lowStockAlert * 2)) * 100)}%"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="flex space-x-2">
                                <button onclick="editProduct('${product.id}')" class="neu-button flex-1 px-3 py-2 rounded-lg text-sm">
                                    ✏️ Edit
                                </button>
                                <button onclick="adjustInventory('${product.id}')" class="neu-button px-3 py-2 rounded-lg text-sm">
                                    📦 Stock
                                </button>
                                <button onclick="deleteProduct('${product.id}')" class="text-red-600 hover:text-red-700 px-2 py-2 rounded-lg text-sm">
                                    🗑️
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function renderLowStockAlert() {
            const lowStockProducts = products.filter(p => p.stock <= p.lowStockAlert);
            const lowStockItems = document.getElementById('lowStockItems');
            const lowStockCount = document.getElementById('lowStockCount');
            const lowStockAlert = document.getElementById('lowStockAlert');

            if (lowStockProducts.length === 0) {
                lowStockAlert.classList.add('hidden');
                return;
            }

            lowStockAlert.classList.remove('hidden');
            lowStockCount.textContent = lowStockProducts.length;

            lowStockItems.innerHTML = lowStockProducts.map(product => {
                const isOutOfStock = product.stock === 0;

                return `
                    <div class="flex items-center justify-between p-4 rounded-lg border-2 ${
                        isOutOfStock ? 'bg-red-50 border-red-200' : 'bg-yellow-50 border-yellow-200'
                    }">
                        <div class="flex items-center space-x-4">
                            <div class="h-12 w-12 bg-white rounded-lg flex items-center justify-center text-xl shadow-sm">
                                ${product.emoji}
                            </div>
                            <div>
                                <h4 class="font-medium ${isOutOfStock ? 'text-red-900' : 'text-yellow-900'}">${product.name}</h4>
                                <div class="flex items-center space-x-4 text-sm">
                                    <span class="font-medium ${isOutOfStock ? 'text-red-700' : 'text-yellow-700'}">
                                        Current: ${product.stock} ${product.unit}
                                    </span>
                                    <span class="text-gray-600">
                                        Alert at: ${product.lowStockAlert} ${product.unit}
                                    </span>
                                    <span class="text-gray-500 font-mono">
                                        SKU: ${product.sku}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center space-x-2">
                            <div class="px-3 py-1 rounded-full text-xs font-medium ${
                                isOutOfStock ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                            }">
                                ${isOutOfStock ? 'Out of Stock' : 'Low Stock'}
                            </div>
                            <button onclick="restockProduct('${product.id}')" class="bg-primary-600 text-white px-3 py-1 rounded-lg text-sm font-medium hover:bg-primary-700">
                                Restock
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            // Update inventory stats
            document.getElementById('lowStockItems').textContent = lowStockProducts.filter(p => p.stock > 0 && p.stock <= p.lowStockAlert).length;
            document.getElementById('outOfStockItems').textContent = lowStockProducts.filter(p => p.stock === 0).length;

            const totalValue = products.reduce((sum, p) => sum + (p.stock * p.cost), 0);
            document.getElementById('inventoryValue').textContent = `$${totalValue.toFixed(2)}`;
        }

        function getCategoryIcon(category) {
            const icons = {
                'signature-sandwiches': '🍗',
                'tender-combos': '🍗',
                'fried-chicken': '🍗',
                'waffle-chicken': '🧇',
                'sides': '🍟',
                'beverages': '🥤',
                'family-deals': '👨‍👩‍👧‍👦'
            };
            return icons[category] || '📦';
        }

        function getCategoryName(category) {
            const names = {
                'signature-sandwiches': 'Signature Sandwiches',
                'tender-combos': 'Tender Combos',
                'fried-chicken': 'Fried Chicken',
                'waffle-chicken': 'Waffle & Chicken',
                'sides': 'Sides',
                'beverages': 'Beverages',
                'family-deals': 'Family Deals'
            };
            return names[category] || 'Other';
        }

        function adjustInventory(productId) {
            const product = products.find(p => p.id === productId);
            const newStock = prompt(`Current stock: ${product.stock} ${product.unit}\nEnter new stock level:`, product.stock);

            if (newStock !== null && !isNaN(newStock) && newStock >= 0) {
                product.stock = parseInt(newStock);
                renderInventoryProducts();
                renderLowStockAlert();
                renderProducts(); // Update POS view too
                alert(`Stock updated for ${product.name}`);
            }
        }

        function restockProduct(productId) {
            const product = products.find(p => p.id === productId);
            const restockAmount = prompt(`Restock ${product.name}\nCurrent: ${product.stock} ${product.unit}\nAdd quantity:`, product.lowStockAlert * 2);

            if (restockAmount !== null && !isNaN(restockAmount) && restockAmount > 0) {
                product.stock += parseInt(restockAmount);
                renderInventoryProducts();
                renderLowStockAlert();
                renderProducts(); // Update POS view too
                alert(`Added ${restockAmount} ${product.unit} to ${product.name}`);
            }
        }

        function editProduct(productId) {
            const product = products.find(p => p.id === productId);
            alert(`Edit Product: ${product.name}\n\nThis would open a detailed edit modal in the full application.`);
        }

        function deleteProduct(productId) {
            const product = products.find(p => p.id === productId);
            if (confirm(`Are you sure you want to delete ${product.name}?`)) {
                const index = products.findIndex(p => p.id === productId);
                products.splice(index, 1);
                renderInventoryProducts();
                renderLowStockAlert();
                renderProducts(); // Update POS view too
                alert(`${product.name} has been deleted`);
            }
        }

        function showAddProductModal() {
            alert('Add Product Modal\n\nThis would open a detailed form to add new menu items in the full application.');
        }

        function showCategoryManager() {
            alert('Category Manager\n\nThis would open the category management interface in the full application.');
        }

        // Image Management Functions
        function showImageUpload(productId) {
            currentEditingProductId = productId;
            const product = products.find(p => p.id === productId);

            // Update modal content
            document.getElementById('currentItemName').textContent = product.name;

            if (product.image) {
                document.getElementById('currentItemImage').src = product.image;
                document.getElementById('currentItemImage').classList.remove('hidden');
                document.getElementById('currentItemEmoji').classList.add('hidden');
            } else {
                document.getElementById('currentItemEmoji').textContent = product.emoji;
                document.getElementById('currentItemImage').classList.add('hidden');
                document.getElementById('currentItemEmoji').classList.remove('hidden');
            }

            // Reset temp data
            tempImageData = null;

            // Show modal
            document.getElementById('imageUploadModal').classList.remove('hidden');
        }

        function hideImageUpload() {
            document.getElementById('imageUploadModal').classList.add('hidden');
            currentEditingProductId = null;
            tempImageData = null;

            // Reset file input
            document.getElementById('imageUploadInput').value = '';
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Validate file type
            const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
            if (!validTypes.includes(file.type)) {
                alert('Please upload a JPEG, JPG, or PNG image.');
                return;
            }

            // Validate file size (5MB limit)
            if (file.size > 5 * 1024 * 1024) {
                alert('Image size must be less than 5MB.');
                return;
            }

            // Create file reader
            const reader = new FileReader();
            reader.onload = function(e) {
                tempImageData = e.target.result;

                // Update preview
                document.getElementById('currentItemImage').src = tempImageData;
                document.getElementById('currentItemImage').classList.remove('hidden');
                document.getElementById('currentItemEmoji').classList.add('hidden');
            };
            reader.readAsDataURL(file);
        }

        function selectGalleryImage(emoji) {
            tempImageData = null; // Clear any uploaded image

            // Update preview
            document.getElementById('currentItemEmoji').textContent = emoji;
            document.getElementById('currentItemImage').classList.add('hidden');
            document.getElementById('currentItemEmoji').classList.remove('hidden');

            // Store the emoji selection
            tempImageData = 'emoji:' + emoji;
        }

        function saveImageChange() {
            if (!currentEditingProductId) return;

            const product = products.find(p => p.id === currentEditingProductId);
            if (!product) return;

            if (tempImageData) {
                if (tempImageData.startsWith('emoji:')) {
                    // User selected an emoji
                    product.emoji = tempImageData.replace('emoji:', '');
                    product.image = null;
                } else {
                    // User uploaded an image
                    product.image = tempImageData;
                }
            }

            // Update displays
            renderProducts();
            renderInventoryProducts();

            // Hide modal
            hideImageUpload();

            // Show success message
            alert(`Image updated for ${product.name}!`);
        }

        // Customer Management Functions
        function renderCustomers() {
            const tbody = document.getElementById('customersTableBody');
            const searchTerm = document.getElementById('customerSearch').value.toLowerCase();
            const statusFilter = document.getElementById('customerStatusFilter').value;
            const sortBy = document.getElementById('customerSort').value;

            let filteredCustomers = customers.filter(customer => {
                const matchesSearch = customer.name.toLowerCase().includes(searchTerm) ||
                                    customer.email.toLowerCase().includes(searchTerm) ||
                                    customer.phone.includes(searchTerm);
                const matchesStatus = statusFilter === 'all' || customer.status === statusFilter;
                return matchesSearch && matchesStatus;
            });

            // Sort customers
            filteredCustomers.sort((a, b) => {
                switch(sortBy) {
                    case 'name-asc': return a.name.localeCompare(b.name);
                    case 'name-desc': return b.name.localeCompare(a.name);
                    case 'orders-desc': return b.orders - a.orders;
                    case 'spent-desc': return b.totalSpent - a.totalSpent;
                    case 'recent': return new Date(b.lastVisit) - new Date(a.lastVisit);
                    default: return 0;
                }
            });

            tbody.innerHTML = filteredCustomers.map(customer => {
                const statusColors = {
                    'active': 'bg-green-100 text-green-800',
                    'loyalty': 'bg-purple-100 text-purple-800',
                    'inactive': 'bg-gray-100 text-gray-800'
                };

                const statusIcons = {
                    'active': '✅',
                    'loyalty': '⭐',
                    'inactive': '😴'
                };

                return `
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-primary-600 font-semibold">${customer.name.charAt(0)}</span>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">${customer.name}</div>
                                    <div class="text-sm text-gray-500">Customer since ${new Date(customer.joinDate).toLocaleDateString()}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">${customer.email}</div>
                            <div class="text-sm text-gray-500">${customer.phone}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">${customer.orders}</div>
                            <div class="text-sm text-gray-500">orders</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">$${customer.totalSpent.toFixed(2)}</div>
                            ${customer.status === 'loyalty' ? `<div class="text-sm text-purple-600">${customer.loyaltyPoints} points</div>` : ''}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[customer.status]}">
                                ${statusIcons[customer.status]} ${customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${new Date(customer.lastVisit).toLocaleDateString()}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="viewCustomer('${customer.id}')" class="text-primary-600 hover:text-primary-900 mr-3">View</button>
                            <button onclick="editCustomer('${customer.id}')" class="text-indigo-600 hover:text-indigo-900">Edit</button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function filterCustomers() {
            renderCustomers();
        }

        function viewCustomer(customerId) {
            const customer = customers.find(c => c.id === customerId);
            alert(`Customer Details:\n\nName: ${customer.name}\nEmail: ${customer.email}\nPhone: ${customer.phone}\nTotal Orders: ${customer.orders}\nTotal Spent: $${customer.totalSpent.toFixed(2)}\nFavorite Item: ${customer.favoriteItem}\nStatus: ${customer.status}\n${customer.status === 'loyalty' ? `Loyalty Points: ${customer.loyaltyPoints}` : ''}`);
        }

        function editCustomer(customerId) {
            alert('Edit Customer\n\nThis would open a detailed customer edit form in the full application.');
        }

        function showAddCustomerModal() {
            alert('Add New Customer\n\nThis would open a form to add new customers with contact info, preferences, and loyalty enrollment.');
        }

        function exportCustomers() {
            alert('Export Customers\n\nThis would export customer data to CSV/Excel format for external analysis.');
        }

        // Sales Reports Functions
        function updateReportPeriod() {
            const period = document.getElementById('reportPeriod').value;

            // Update metrics based on selected period
            const metrics = {
                today: {
                    revenue: '$847.50',
                    orders: '47',
                    avgOrder: '$18.03',
                    peakHour: '12-1 PM'
                },
                week: {
                    revenue: '$5,234.80',
                    orders: '289',
                    avgOrder: '$18.11',
                    peakHour: '12-1 PM'
                },
                month: {
                    revenue: '$12,847.50',
                    orders: '697',
                    avgOrder: '$18.43',
                    peakHour: '12-1 PM'
                },
                quarter: {
                    revenue: '$38,542.30',
                    orders: '2,091',
                    avgOrder: '$18.43',
                    peakHour: '12-1 PM'
                },
                year: {
                    revenue: '$154,169.20',
                    orders: '8,364',
                    avgOrder: '$18.43',
                    peakHour: '12-1 PM'
                }
            };

            const data = metrics[period];
            document.getElementById('totalRevenue').textContent = data.revenue;
            document.getElementById('totalOrdersReport').textContent = data.orders;
            document.getElementById('avgOrderValue').textContent = data.avgOrder;
            document.getElementById('peakHour').textContent = data.peakHour;
        }

        function exportReports() {
            const period = document.getElementById('reportPeriod').value;
            alert(`Export Reports\n\nThis would export ${period} sales data including:\n- Revenue breakdown\n- Product performance\n- Payment method analysis\n- Customer insights\n- Hourly sales patterns`);
        }

        // Add button press effects
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.neu-button').forEach(button => {
                button.addEventListener('mousedown', () => {
                    button.style.boxShadow = 'inset 2px 2px 5px #a3b1c6, inset -2px -2px 5px #ffffff';
                });
                button.addEventListener('mouseup', () => {
                    button.style.boxShadow = '2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff';
                });
                button.addEventListener('mouseleave', () => {
                    button.style.boxShadow = '2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff';
                });
            });
        });
    </script>
</body>
</html>