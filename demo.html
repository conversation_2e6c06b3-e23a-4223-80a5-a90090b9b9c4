<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POSess - Interactive Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fef7ed',
                            100: '#fdedd3',
                            200: '#fbd6a5',
                            300: '#f8b86d',
                            400: '#f59332',
                            500: '#f37316',
                            600: '#e4570b',
                            700: '#bd420b',
                            800: '#973510',
                            900: '#7c2d12',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .neu-card {
            background: #e0e5ec;
            box-shadow: 2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff;
            transition: all 0.2s ease;
        }
        .neu-card:hover {
            box-shadow: inset 1px 1px 2px #a3b1c6, inset -1px -1px 2px #ffffff;
        }
        .neu-button {
            background: #e0e5ec;
            box-shadow: 2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .neu-button:active {
            box-shadow: inset 2px 2px 5px #a3b1c6, inset -2px -2px 5px #ffffff;
        }
        .hidden { display: none; }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Login Screen -->
    <div id="loginScreen" class="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center p-4">
        <div class="neu-card p-8 rounded-xl max-w-md w-full">
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span class="text-white font-bold text-2xl">🍗</span>
                </div>
                <h1 class="text-2xl font-bold text-gray-900">Welcome to Crispy Crown</h1>
                <p class="text-gray-600 mt-2">Louisiana Style Chicken POS</p>
            </div>
            
            <div class="space-y-4">
                <input type="email" placeholder="Enter your email" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500" value="<EMAIL>">
                <input type="password" placeholder="Enter your password" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500" value="demo123">
                <button onclick="login()" class="w-full bg-primary-600 text-white py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors">
                    Sign In
                </button>
            </div>
            
            <p class="text-center text-sm text-gray-500 mt-4">
                Demo credentials are pre-filled. Just click "Sign In"!
            </p>
        </div>
    </div>

    <!-- Main App -->
    <div id="mainApp" class="hidden">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">🍗</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">Crispy Crown</h1>
                        <p class="text-xs text-gray-500">Louisiana Style Chicken</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span>Shift Active</span>
                    </div>
                    <div class="relative">
                        <button class="neu-button px-4 py-2 rounded-lg text-gray-700 font-medium">
                            🛒 Cart (<span id="cartCount">0</span>)
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <div class="flex">
            <!-- Sidebar -->
            <aside class="w-64 bg-white shadow-sm h-screen">
                <nav class="p-4 space-y-2">
                    <button onclick="showSection('dashboard')" class="nav-item w-full text-left flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-primary-100 text-primary-700">
                        📊 Dashboard
                    </button>
                    <button onclick="showSection('pos')" class="nav-item w-full text-left flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        🛒 POS
                    </button>
                    <button onclick="showSection('products')" class="nav-item w-full text-left flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        📦 Products
                    </button>
                    <button onclick="showSection('customers')" class="nav-item w-full text-left flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        👥 Customers
                    </button>
                    <button onclick="showSection('analytics')" class="nav-item w-full text-left flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                        📈 Analytics
                    </button>
                </nav>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 p-6">
                <!-- Dashboard Section -->
                <div id="dashboard" class="section">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Dashboard</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-green-100 mr-4">
                                    <span class="text-green-600 text-xl">💵</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Today's Sales</p>
                                    <p class="text-2xl font-bold text-gray-900" id="totalSales">$0.00</p>
                                </div>
                            </div>
                        </div>
                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-blue-100 mr-4">
                                    <span class="text-blue-600 text-xl">🛒</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Orders</p>
                                    <p class="text-2xl font-bold text-gray-900" id="totalOrders">0</p>
                                </div>
                            </div>
                        </div>
                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-purple-100 mr-4">
                                    <span class="text-purple-600 text-xl">👥</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Customers</p>
                                    <p class="text-2xl font-bold text-gray-900">89</p>
                                </div>
                            </div>
                        </div>
                        <div class="neu-card p-6 rounded-lg">
                            <div class="flex items-center">
                                <div class="p-3 rounded-lg bg-orange-100 mr-4">
                                    <span class="text-orange-600 text-xl">🍗</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Chicken Items</p>
                                    <p class="text-2xl font-bold text-gray-900">12</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- POS Section -->
                <div id="pos" class="section hidden">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Point of Sale</h2>
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Products -->
                        <div class="lg:col-span-2">
                            <div class="neu-card p-6 rounded-lg">
                                <h3 class="text-lg font-semibold mb-4">Products</h3>
                                <div class="grid grid-cols-2 md:grid-cols-3 gap-4" id="productGrid">
                                    <!-- Products will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                        
                        <!-- Cart -->
                        <div>
                            <div class="neu-card p-6 rounded-lg">
                                <h3 class="text-lg font-semibold mb-4">Cart</h3>
                                <div id="cartItems" class="space-y-3 mb-4">
                                    <p class="text-gray-500 text-center py-4">Cart is empty</p>
                                </div>
                                <div class="border-t pt-4">
                                    <div class="flex justify-between font-bold text-lg">
                                        <span>Total:</span>
                                        <span id="cartTotal">$0.00</span>
                                    </div>
                                    <button onclick="checkout()" class="w-full bg-primary-600 text-white py-3 rounded-lg font-medium mt-4 hover:bg-primary-700 transition-colors">
                                        Checkout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other sections (simplified) -->
                <div id="products" class="section hidden">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Product Management</h2>
                    <div class="neu-card p-6 rounded-lg">
                        <p class="text-gray-600">Product management interface would be here in the full app.</p>
                        <p class="text-sm text-gray-500 mt-2">Features: Add/edit products, manage inventory, categories, pricing, etc.</p>
                    </div>
                </div>

                <div id="customers" class="section hidden">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Customer Management</h2>
                    <div class="neu-card p-6 rounded-lg">
                        <p class="text-gray-600">Customer management interface would be here in the full app.</p>
                        <p class="text-sm text-gray-500 mt-2">Features: Customer database, loyalty points, purchase history, etc.</p>
                    </div>
                </div>

                <div id="analytics" class="section hidden">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Analytics Dashboard</h2>
                    <div class="neu-card p-6 rounded-lg">
                        <p class="text-gray-600">Analytics dashboard would be here in the full app.</p>
                        <p class="text-sm text-gray-500 mt-2">Features: Sales reports, trends, top products, customer insights, etc.</p>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Checkout Modal -->
    <div id="checkoutModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div class="neu-card p-6 rounded-xl max-w-md w-full">
            <h3 class="text-xl font-bold mb-4">Checkout</h3>
            <div class="space-y-4">
                <div class="flex justify-between">
                    <span>Subtotal:</span>
                    <span id="checkoutSubtotal">$0.00</span>
                </div>
                <div class="flex justify-between">
                    <span>Tax (8%):</span>
                    <span id="checkoutTax">$0.00</span>
                </div>
                <div class="flex justify-between font-bold text-lg border-t pt-2">
                    <span>Total:</span>
                    <span id="checkoutTotal">$0.00</span>
                </div>
                
                <div class="space-y-2">
                    <h4 class="font-medium">Payment Method:</h4>
                    <div class="grid grid-cols-2 gap-2">
                        <button onclick="processPayment('cash')" class="neu-button p-3 rounded-lg text-center">
                            💵 Cash
                        </button>
                        <button onclick="processPayment('cashapp')" class="neu-button p-3 rounded-lg text-center">
                            💚 CashApp
                        </button>
                        <button onclick="processPayment('zelle')" class="neu-button p-3 rounded-lg text-center">
                            🏦 Zelle
                        </button>
                        <button onclick="processPayment('card')" class="neu-button p-3 rounded-lg text-center">
                            💳 Card
                        </button>
                    </div>
                </div>
                
                <div class="flex space-x-3">
                    <button onclick="closeCheckout()" class="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Demo data and functionality
        let cart = [];
        let totalSales = 0;
        let totalOrders = 0;
        
        const products = [
            { id: 1, name: 'Spicy Chicken Sandwich', price: 8.99, emoji: '🍗' },
            { id: 2, name: '3-Piece Chicken Tenders', price: 7.49, emoji: '🍗' },
            { id: 3, name: 'Fried Chicken Leg', price: 3.99, emoji: '🍗' },
            { id: 4, name: 'Chicken Wings (6pc)', price: 9.99, emoji: '🍗' },
            { id: 5, name: 'Cajun Fries', price: 3.49, emoji: '🍟' },
            { id: 6, name: 'Mac & Cheese', price: 4.99, emoji: '🧀' },
            { id: 7, name: 'Coleslaw', price: 2.99, emoji: '🥗' },
            { id: 8, name: 'Biscuit', price: 1.99, emoji: '🥖' },
            { id: 9, name: 'Sweet Tea', price: 2.49, emoji: '🧊' },
            { id: 10, name: 'Lemonade', price: 2.99, emoji: '🍋' },
            { id: 11, name: 'Family Meal (8pc)', price: 24.99, emoji: '🍗' },
            { id: 12, name: 'Hot Sauce', price: 0.50, emoji: '🌶️' }
        ];

        function login() {
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');
            initializeApp();
        }

        function initializeApp() {
            renderProducts();
            updateCartDisplay();
        }

        function renderProducts() {
            const grid = document.getElementById('productGrid');
            grid.innerHTML = products.map(product => `
                <button onclick="addToCart(${product.id})" class="neu-button p-4 rounded-lg text-center hover:shadow-inner">
                    <div class="text-3xl mb-2">${product.emoji}</div>
                    <div class="font-medium">${product.name}</div>
                    <div class="text-primary-600 font-bold">$${product.price.toFixed(2)}</div>
                </button>
            `).join('');
        }

        function addToCart(productId) {
            const product = products.find(p => p.id === productId);
            const existingItem = cart.find(item => item.id === productId);
            
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({ ...product, quantity: 1 });
            }
            
            updateCartDisplay();
        }

        function removeFromCart(productId) {
            cart = cart.filter(item => item.id !== productId);
            updateCartDisplay();
        }

        function updateCartDisplay() {
            const cartItems = document.getElementById('cartItems');
            const cartCount = document.getElementById('cartCount');
            const cartTotal = document.getElementById('cartTotal');
            
            if (cart.length === 0) {
                cartItems.innerHTML = '<p class="text-gray-500 text-center py-4">Cart is empty</p>';
            } else {
                cartItems.innerHTML = cart.map(item => `
                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div>
                            <div class="font-medium">${item.name}</div>
                            <div class="text-sm text-gray-600">$${item.price.toFixed(2)} × ${item.quantity}</div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="font-bold">$${(item.price * item.quantity).toFixed(2)}</span>
                            <button onclick="removeFromCart(${item.id})" class="text-red-500 hover:text-red-700">×</button>
                        </div>
                    </div>
                `).join('');
            }
            
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const itemCount = cart.reduce((sum, item) => sum + item.quantity, 0);
            
            cartCount.textContent = itemCount;
            cartTotal.textContent = `$${total.toFixed(2)}`;
        }

        function checkout() {
            if (cart.length === 0) {
                alert('Cart is empty!');
                return;
            }
            
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const tax = subtotal * 0.08;
            const total = subtotal + tax;
            
            document.getElementById('checkoutSubtotal').textContent = `$${subtotal.toFixed(2)}`;
            document.getElementById('checkoutTax').textContent = `$${tax.toFixed(2)}`;
            document.getElementById('checkoutTotal').textContent = `$${total.toFixed(2)}`;
            
            document.getElementById('checkoutModal').classList.remove('hidden');
        }

        function closeCheckout() {
            document.getElementById('checkoutModal').classList.add('hidden');
        }

        function processPayment(method) {
            const total = parseFloat(document.getElementById('checkoutTotal').textContent.replace('$', ''));
            
            // Simulate payment processing
            setTimeout(() => {
                alert(`Payment of $${total.toFixed(2)} processed via ${method.toUpperCase()}!`);
                
                // Update stats
                totalSales += total;
                totalOrders += 1;
                
                document.getElementById('totalSales').textContent = `$${totalSales.toFixed(2)}`;
                document.getElementById('totalOrders').textContent = totalOrders;
                
                // Clear cart
                cart = [];
                updateCartDisplay();
                closeCheckout();
            }, 1000);
        }

        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.add('hidden');
            });
            
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('bg-primary-100', 'text-primary-700');
                item.classList.add('text-gray-700', 'hover:bg-gray-100');
            });
            
            // Show selected section
            document.getElementById(sectionName).classList.remove('hidden');
            
            // Add active class to clicked nav item
            event.target.classList.add('bg-primary-100', 'text-primary-700');
            event.target.classList.remove('text-gray-700', 'hover:bg-gray-100');
        }

        // Add button press effects
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.neu-button').forEach(button => {
                button.addEventListener('mousedown', () => {
                    button.style.boxShadow = 'inset 2px 2px 5px #a3b1c6, inset -2px -2px 5px #ffffff';
                });
                button.addEventListener('mouseup', () => {
                    button.style.boxShadow = '2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff';
                });
                button.addEventListener('mouseleave', () => {
                    button.style.boxShadow = '2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff';
                });
            });
        });
    </script>
</body>
</html>
