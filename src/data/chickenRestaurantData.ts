import { Product, Category } from '../types';

// Chicken Restaurant Categories
export const chickenCategories: Category[] = [
  {
    id: 'chicken-main',
    name: 'Chicken Entrees',
    color: '#f37316',
    icon: '🍗',
    sortOrder: 1,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'sides',
    name: 'Sides',
    color: '#10b981',
    icon: '🍟',
    sortOrder: 2,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'beverages',
    name: 'Beverages',
    color: '#3b82f6',
    icon: '🥤',
    sortOrder: 3,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'desserts',
    name: 'Desserts',
    color: '#ec4899',
    icon: '🍰',
    sortOrder: 4,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'family-meals',
    name: 'Family Meals',
    color: '#8b5cf6',
    icon: '👨‍👩‍👧‍👦',
    sortOrder: 5,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Chicken Restaurant Products
export const chickenProducts: Product[] = [
  // Chicken Entrees
  {
    id: 'spicy-chicken-sandwich',
    businessId: 'crispy-crown',
    name: 'Spicy Chicken Sandwich',
    description: 'Crispy fried chicken breast with spicy mayo, pickles, and lettuce on a brioche bun',
    price: 8.99,
    cost: 3.50,
    sku: 'SCS001',
    barcode: '123456789001',
    image: '/images/spicy-chicken-sandwich.jpg',
    category: chickenCategories[0],
    modifiers: [
      {
        id: 'spice-level',
        name: 'Spice Level',
        type: 'single',
        required: false,
        options: [
          { id: 'mild', name: 'Mild', price: 0 },
          { id: 'medium', name: 'Medium', price: 0 },
          { id: 'hot', name: 'Hot', price: 0 },
          { id: 'extra-hot', name: 'Extra Hot', price: 0.50 }
        ]
      }
    ],
    inventory: {
      trackInventory: true,
      currentStock: 45,
      lowStockAlert: 10,
      unit: 'pieces'
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'chicken-tenders-3pc',
    businessId: 'crispy-crown',
    name: '3-Piece Chicken Tenders',
    description: 'Hand-breaded chicken tenders with your choice of dipping sauce',
    price: 7.49,
    cost: 2.80,
    sku: 'CT3001',
    barcode: '123456789002',
    image: '/images/chicken-tenders.jpg',
    category: chickenCategories[0],
    modifiers: [
      {
        id: 'sauce',
        name: 'Dipping Sauce',
        type: 'single',
        required: true,
        options: [
          { id: 'honey-mustard', name: 'Honey Mustard', price: 0 },
          { id: 'bbq', name: 'BBQ Sauce', price: 0 },
          { id: 'ranch', name: 'Ranch', price: 0 },
          { id: 'buffalo', name: 'Buffalo Sauce', price: 0 },
          { id: 'honey', name: 'Honey', price: 0 }
        ]
      }
    ],
    inventory: {
      trackInventory: true,
      currentStock: 32,
      lowStockAlert: 8,
      unit: 'servings'
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'fried-chicken-leg',
    businessId: 'crispy-crown',
    name: 'Fried Chicken Leg',
    description: 'Crispy Louisiana-style fried chicken leg with signature seasoning',
    price: 3.99,
    cost: 1.50,
    sku: 'FCL001',
    barcode: '123456789003',
    image: '/images/chicken-leg.jpg',
    category: chickenCategories[0],
    modifiers: [],
    inventory: {
      trackInventory: true,
      currentStock: 28,
      lowStockAlert: 5,
      unit: 'pieces'
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'chicken-wings-6pc',
    businessId: 'crispy-crown',
    name: 'Chicken Wings (6pc)',
    description: 'Six crispy chicken wings tossed in your choice of sauce',
    price: 9.99,
    cost: 4.20,
    sku: 'CW6001',
    barcode: '123456789004',
    image: '/images/chicken-wings.jpg',
    category: chickenCategories[0],
    modifiers: [
      {
        id: 'wing-sauce',
        name: 'Wing Sauce',
        type: 'single',
        required: true,
        options: [
          { id: 'buffalo', name: 'Buffalo', price: 0 },
          { id: 'bbq', name: 'BBQ', price: 0 },
          { id: 'honey-garlic', name: 'Honey Garlic', price: 0 },
          { id: 'lemon-pepper', name: 'Lemon Pepper', price: 0 },
          { id: 'cajun-dry', name: 'Cajun Dry Rub', price: 0 }
        ]
      }
    ],
    inventory: {
      trackInventory: true,
      currentStock: 18,
      lowStockAlert: 6,
      unit: 'servings'
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // Sides
  {
    id: 'cajun-fries',
    businessId: 'crispy-crown',
    name: 'Cajun Fries',
    description: 'Crispy seasoned fries with authentic Cajun spices',
    price: 3.49,
    cost: 0.80,
    sku: 'CF001',
    barcode: '123456789005',
    image: '/images/cajun-fries.jpg',
    category: chickenCategories[1],
    modifiers: [
      {
        id: 'size',
        name: 'Size',
        type: 'single',
        required: true,
        options: [
          { id: 'regular', name: 'Regular', price: 0 },
          { id: 'large', name: 'Large', price: 1.50 }
        ]
      }
    ],
    inventory: {
      trackInventory: true,
      currentStock: 50,
      lowStockAlert: 15,
      unit: 'servings'
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'mac-cheese',
    businessId: 'crispy-crown',
    name: 'Mac & Cheese',
    description: 'Creamy three-cheese macaroni and cheese',
    price: 4.99,
    cost: 1.20,
    sku: 'MC001',
    barcode: '123456789006',
    image: '/images/mac-cheese.jpg',
    category: chickenCategories[1],
    modifiers: [],
    inventory: {
      trackInventory: true,
      currentStock: 25,
      lowStockAlert: 8,
      unit: 'servings'
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'coleslaw',
    businessId: 'crispy-crown',
    name: 'Coleslaw',
    description: 'Fresh cabbage slaw with creamy dressing',
    price: 2.99,
    cost: 0.60,
    sku: 'CS001',
    barcode: '123456789007',
    image: '/images/coleslaw.jpg',
    category: chickenCategories[1],
    modifiers: [],
    inventory: {
      trackInventory: true,
      currentStock: 30,
      lowStockAlert: 10,
      unit: 'servings'
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'buttermilk-biscuit',
    businessId: 'crispy-crown',
    name: 'Buttermilk Biscuit',
    description: 'Warm, flaky buttermilk biscuit with honey butter',
    price: 1.99,
    cost: 0.40,
    sku: 'BB001',
    barcode: '123456789008',
    image: '/images/biscuit.jpg',
    category: chickenCategories[1],
    modifiers: [],
    inventory: {
      trackInventory: true,
      currentStock: 40,
      lowStockAlert: 12,
      unit: 'pieces'
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // Beverages
  {
    id: 'sweet-tea',
    businessId: 'crispy-crown',
    name: 'Sweet Tea',
    description: 'Southern-style sweet iced tea',
    price: 2.49,
    cost: 0.30,
    sku: 'ST001',
    barcode: '123456789009',
    image: '/images/sweet-tea.jpg',
    category: chickenCategories[2],
    modifiers: [
      {
        id: 'size',
        name: 'Size',
        type: 'single',
        required: true,
        options: [
          { id: 'small', name: 'Small', price: 0 },
          { id: 'medium', name: 'Medium', price: 0.50 },
          { id: 'large', name: 'Large', price: 1.00 }
        ]
      }
    ],
    inventory: {
      trackInventory: false,
      currentStock: 0,
      lowStockAlert: 0,
      unit: 'cups'
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'fresh-lemonade',
    businessId: 'crispy-crown',
    name: 'Fresh Lemonade',
    description: 'Freshly squeezed lemonade',
    price: 2.99,
    cost: 0.50,
    sku: 'FL001',
    barcode: '123456789010',
    image: '/images/lemonade.jpg',
    category: chickenCategories[2],
    modifiers: [
      {
        id: 'size',
        name: 'Size',
        type: 'single',
        required: true,
        options: [
          { id: 'small', name: 'Small', price: 0 },
          { id: 'medium', name: 'Medium', price: 0.50 },
          { id: 'large', name: 'Large', price: 1.00 }
        ]
      }
    ],
    inventory: {
      trackInventory: false,
      currentStock: 0,
      lowStockAlert: 0,
      unit: 'cups'
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },

  // Family Meals
  {
    id: 'family-meal-8pc',
    businessId: 'crispy-crown',
    name: 'Family Meal (8pc Chicken)',
    description: '8 pieces of mixed fried chicken with 2 large sides and 4 biscuits',
    price: 24.99,
    cost: 12.00,
    sku: 'FM8001',
    barcode: '123456789011',
    image: '/images/family-meal.jpg',
    category: chickenCategories[4],
    modifiers: [
      {
        id: 'sides',
        name: 'Choose 2 Large Sides',
        type: 'multiple',
        required: true,
        maxSelections: 2,
        options: [
          { id: 'cajun-fries', name: 'Cajun Fries', price: 0 },
          { id: 'mac-cheese', name: 'Mac & Cheese', price: 0 },
          { id: 'coleslaw', name: 'Coleslaw', price: 0 },
          { id: 'mashed-potatoes', name: 'Mashed Potatoes', price: 0 }
        ]
      }
    ],
    inventory: {
      trackInventory: true,
      currentStock: 15,
      lowStockAlert: 3,
      unit: 'meals'
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Business Information
export const chickenBusinessInfo = {
  name: 'Crispy Crown',
  tagline: 'Louisiana Style Chicken',
  description: 'Authentic Louisiana-style fried chicken with bold flavors and Southern hospitality',
  colors: {
    primary: '#f37316', // Orange
    secondary: '#dc2626', // Red
    accent: '#059669' // Green
  },
  logo: '🍗',
  address: {
    street: '1234 Louisiana Ave',
    city: 'Washington',
    state: 'DC',
    zipCode: '20001',
    phone: '(202) 555-WING'
  }
};
