/* Additional styles for components */

/* Menu Display */
.menu-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.menu-tabs button {
  padding: 8px 15px;
  background: rgba(0, 0, 0, 0.2);
  border: none;
  border-radius: 8px;
  color: var(--light-text);
  cursor: pointer;
  transition: all 0.3s ease;
}

.menu-tabs button.active {
  background: var(--secondary-color);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.menu-item-details h4 {
  margin: 0 0 5px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  color: var(--accent-color);
  font-weight: 600;
}

.package-icon {
  font-size: 2rem;
  background: rgba(0, 0, 0, 0.3);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Client Profile */
.profile-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.profile-avatar {
  width: 60px;
  height: 60px;
  background: var(--secondary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  font-weight: 600;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.profile-basic-info h4 {
  margin: 0 0 5px 0;
}

.profile-basic-info p {
  margin: 0;
  opacity: 0.7;
  font-size: 0.9rem;
}

.profile-section {
  margin-bottom: 20px;
}

.profile-section h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--secondary-color);
}

.preferences-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preference-tag {
  background: rgba(79, 156, 255, 0.2);
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.9rem;
}

.order-history {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  background: rgba(0, 0, 0, 0.2);
  padding: 8px 12px;
  border-radius: 8px;
}

.notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.edit-notes-btn {
  background: none;
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  font-size: 0.9rem;
}

.notes-editor textarea {
  width: 100%;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: var(--light-text);
  padding: 10px;
  resize: vertical;
}

.notes-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.notes-actions button {
  padding: 6px 12px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
}

.notes-actions button:first-child {
  background: rgba(0, 0, 0, 0.3);
  color: var(--light-text);
}

.notes-actions button:last-child {
  background: var(--secondary-color);
  color: white;
}

/* Payment Guide */
.payment-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.copy-button {
  background: rgba(0, 0, 0, 0.3);
  border: none;
  color: var(--light-text);
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.8rem;
  margin-top: 5px;
}

.payment-info {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 15px;
  margin-top: 10px;
}

.payment-info h4 
</augment_code_snippet>