import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { User, Business, Order, CartItem, Customer, Shift, PaymentMethod, POSState } from '../types';
import { AuthService } from '../services/authService';

// Action types
type POSAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_BUSINESS'; payload: Business | null }
  | { type: 'SET_CURRENT_ORDER'; payload: Order | null }
  | { type: 'ADD_TO_CART'; payload: CartItem }
  | { type: 'REMOVE_FROM_CART'; payload: string }
  | { type: 'UPDATE_CART_ITEM'; payload: { id: string; updates: Partial<CartItem> } }
  | { type: 'CLEAR_CART' }
  | { type: 'SET_SELECTED_CUSTOMER'; payload: Customer | null }
  | { type: 'SET_ACTIVE_SHIFT'; payload: Shift | null }
  | { type: 'SET_PAYMENT_METHOD'; payload: PaymentMethod | null }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

// Extended state interface
interface ExtendedPOSState extends POSState {
  user: User | null;
  business: Business | null;
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: ExtendedPOSState = {
  user: null,
  business: null,
  currentOrder: null,
  cart: [],
  selectedCustomer: null,
  activeShift: null,
  paymentMethod: null,
  isLoading: true,
  error: null,
};

// Reducer
function posReducer(state: ExtendedPOSState, action: POSAction): ExtendedPOSState {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload };
    
    case 'SET_BUSINESS':
      return { ...state, business: action.payload };
    
    case 'SET_CURRENT_ORDER':
      return { ...state, currentOrder: action.payload };
    
    case 'ADD_TO_CART':
      const existingItemIndex = state.cart.findIndex(
        item => item.productId === action.payload.productId &&
        JSON.stringify(item.selectedModifiers) === JSON.stringify(action.payload.selectedModifiers)
      );
      
      if (existingItemIndex >= 0) {
        const updatedCart = [...state.cart];
        updatedCart[existingItemIndex] = {
          ...updatedCart[existingItemIndex],
          quantity: updatedCart[existingItemIndex].quantity + action.payload.quantity,
          subtotal: updatedCart[existingItemIndex].subtotal + action.payload.subtotal
        };
        return { ...state, cart: updatedCart };
      } else {
        return { ...state, cart: [...state.cart, action.payload] };
      }
    
    case 'REMOVE_FROM_CART':
      return {
        ...state,
        cart: state.cart.filter(item => item.id !== action.payload)
      };
    
    case 'UPDATE_CART_ITEM':
      return {
        ...state,
        cart: state.cart.map(item =>
          item.id === action.payload.id
            ? { ...item, ...action.payload.updates }
            : item
        )
      };
    
    case 'CLEAR_CART':
      return { ...state, cart: [] };
    
    case 'SET_SELECTED_CUSTOMER':
      return { ...state, selectedCustomer: action.payload };
    
    case 'SET_ACTIVE_SHIFT':
      return { ...state, activeShift: action.payload };
    
    case 'SET_PAYMENT_METHOD':
      return { ...state, paymentMethod: action.payload };
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    default:
      return state;
  }
}

// Context
const POSContext = createContext<{
  state: ExtendedPOSState;
  dispatch: React.Dispatch<POSAction>;
  // Helper functions
  addToCart: (item: CartItem) => void;
  removeFromCart: (itemId: string) => void;
  updateCartItem: (itemId: string, updates: Partial<CartItem>) => void;
  clearCart: () => void;
  getCartTotal: () => number;
  getCartItemCount: () => number;
} | null>(null);

// Provider component
export function POSProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(posReducer, initialState);

  // Initialize auth state
  useEffect(() => {
    const unsubscribe = AuthService.onAuthStateChange(async (firebaseUser) => {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      if (firebaseUser) {
        try {
          const user = await AuthService.getUserProfile(firebaseUser.uid);
          const business = user ? await AuthService.getBusinessProfile(user.businessId) : null;
          
          dispatch({ type: 'SET_USER', payload: user });
          dispatch({ type: 'SET_BUSINESS', payload: business });
        } catch (error) {
          console.error('Error loading user data:', error);
          dispatch({ type: 'SET_ERROR', payload: 'Failed to load user data' });
        }
      } else {
        dispatch({ type: 'SET_USER', payload: null });
        dispatch({ type: 'SET_BUSINESS', payload: null });
      }
      
      dispatch({ type: 'SET_LOADING', payload: false });
    });

    return unsubscribe;
  }, []);

  // Helper functions
  const addToCart = (item: CartItem) => {
    dispatch({ type: 'ADD_TO_CART', payload: item });
  };

  const removeFromCart = (itemId: string) => {
    dispatch({ type: 'REMOVE_FROM_CART', payload: itemId });
  };

  const updateCartItem = (itemId: string, updates: Partial<CartItem>) => {
    dispatch({ type: 'UPDATE_CART_ITEM', payload: { id: itemId, updates } });
  };

  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
  };

  const getCartTotal = () => {
    return state.cart.reduce((total, item) => total + item.subtotal, 0);
  };

  const getCartItemCount = () => {
    return state.cart.reduce((count, item) => count + item.quantity, 0);
  };

  const value = {
    state,
    dispatch,
    addToCart,
    removeFromCart,
    updateCartItem,
    clearCart,
    getCartTotal,
    getCartItemCount,
  };

  return (
    <POSContext.Provider value={value}>
      {children}
    </POSContext.Provider>
  );
}

// Hook to use POS context
export function usePOS() {
  const context = useContext(POSContext);
  if (!context) {
    throw new Error('usePOS must be used within a POSProvider');
  }
  return context;
}
