import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || '';
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || '';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Helper functions for common Supabase operations
export const supabaseHelpers = {
  // Authentication helpers
  auth: {
    // Sign up a new user
    signUp: async (email: string, password: string) => {
      return await supabase.auth.signUp({ email, password });
    },
    
    // Sign in an existing user
    signIn: async (email: string, password: string) => {
      return await supabase.auth.signInWithPassword({ email, password });
    },
    
    // Sign out the current user
    signOut: async () => {
      return await supabase.auth.signOut();
    },
    
    // Get the current user
    getCurrentUser: async () => {
      return await supabase.auth.getUser();
    },
    
    // Set up auth state change listener
    onAuthStateChange: (callback: (event: any, session: any) => void) => {
      return supabase.auth.onAuthStateChange(callback);
    },
    
    // Reset password
    resetPassword: async (email: string) => {
      return await supabase.auth.resetPasswordForEmail(email);
    },
    
    // Update user profile
    updateProfile: async (updates: any) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('No user logged in');
      
      return await supabase.from('profiles').upsert({
        id: user.id,
        ...updates,
        updated_at: new Date()
      });
    }
  },
  
  // Database helpers
  db: {
    // Generic fetch function
    fetch: async (table: string, options: any = {}) => {
      let query = supabase.from(table).select(options.select || '*');
      
      // Apply filters if provided
      if (options.filters) {
        options.filters.forEach((filter: any) => {
          query = query.filter(filter.column, filter.operator, filter.value);
        });
      }
      
      // Apply order if provided
      if (options.order) {
        query = query.order(options.order.column, { ascending: options.order.ascending });
      }
      
      // Apply pagination if provided
      if (options.pagination) {
        query = query.range(
          options.pagination.from, 
          options.pagination.to
        );
      }
      
      return await query;
    },
    
    // Insert a new record
    insert: async (table: string, data: any) => {
      return await supabase.from(table).insert(data);
    },
    
    // Update an existing record
    update: async (table: string, id: string, data: any, idColumn: string = 'id') => {
      return await supabase.from(table).update(data).eq(idColumn, id);
    },
    
    // Delete a record
    delete: async (table: string, id: string, idColumn: string = 'id') => {
      return await supabase.from(table).delete().eq(idColumn, id);
    },
    
    // Upsert (insert or update)
    upsert: async (table: string, data: any) => {
      return await supabase.from(table).upsert(data);
    }
  },
  
  // Storage helpers
  storage: {
    // Upload a file
    upload: async (bucket: string, path: string, file: File) => {
      return await supabase.storage.from(bucket).upload(path, file);
    },
    
    // Download a file
    download: async (bucket: string, path: string) => {
      return await supabase.storage.from(bucket).download(path);
    },
    
    // Get a public URL for a file
    getPublicUrl: (bucket: string, path: string) => {
      return supabase.storage.from(bucket).getPublicUrl(path);
    },
    
    // Delete a file
    delete: async (bucket: string, paths: string[]) => {
      return await supabase.storage.from(bucket).remove(paths);
    },
    
    // List files in a bucket
    list: async (bucket: string, path: string = '') => {
      return await supabase.storage.from(bucket).list(path);
    }
  },
  
  // Realtime helpers
  realtime: {
    // Subscribe to changes in a table
    subscribe: (table: string, callback: (payload: any) => void) => {
      return supabase
        .channel(`public:${table}`)
        .on('postgres_changes', { event: '*', schema: 'public', table }, callback)
        .subscribe();
    },
    
    // Unsubscribe from changes
    unsubscribe: (subscription: any) => {
      supabase.removeChannel(subscription);
    }
  }
};

export default supabase;
