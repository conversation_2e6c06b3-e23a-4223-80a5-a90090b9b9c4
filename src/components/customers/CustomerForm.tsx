import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { User, Mail, Phone, FileText } from 'lucide-react';
import { usePOS } from '../../contexts/POSContext';
import { CustomerService } from '../../services/customerService';
import { Customer } from '../../types';
import Button from '../ui/Button';
import Input from '../ui/Input';

interface CustomerFormData {
  name: string;
  email: string;
  phone: string;
  notes: string;
}

interface CustomerFormProps {
  customer?: Customer | null;
  onSave: () => void;
  onCancel: () => void;
}

const CustomerForm: React.FC<CustomerFormProps> = ({ customer, onSave, onCancel }) => {
  const { state } = usePOS();
  const [isLoading, setIsLoading] = useState(false);

  const { register, handleSubmit, formState: { errors } } = useForm<CustomerFormData>({
    defaultValues: {
      name: customer?.name || '',
      email: customer?.email || '',
      phone: customer?.phone || '',
      notes: customer?.notes || ''
    }
  });

  const onSubmit = async (data: CustomerFormData) => {
    if (!state.business?.id) return;

    setIsLoading(true);
    try {
      if (customer) {
        await CustomerService.updateCustomer(customer.id, data);
      } else {
        await CustomerService.createCustomer({
          businessId: state.business.id,
          name: data.name,
          email: data.email,
          phone: data.phone,
          notes: data.notes,
          loyaltyPoints: 0,
          totalSpent: 0,
          visitCount: 0
        });
      }
      
      onSave();
    } catch (error) {
      console.error('Error saving customer:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <Input
        label="Full Name *"
        placeholder="Enter customer's full name"
        leftIcon={<User size={16} />}
        {...register('name', { required: 'Name is required' })}
        error={errors.name?.message}
      />

      <Input
        label="Email Address"
        type="email"
        placeholder="Enter customer's email"
        leftIcon={<Mail size={16} />}
        {...register('email', {
          pattern: {
            value: /^\S+@\S+$/i,
            message: 'Invalid email address'
          }
        })}
        error={errors.email?.message}
      />

      <Input
        label="Phone Number"
        placeholder="Enter customer's phone number"
        leftIcon={<Phone size={16} />}
        {...register('phone')}
        error={errors.phone?.message}
      />

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Notes
        </label>
        <textarea
          {...register('notes')}
          placeholder="Enter any additional notes, preferences, or birthday information"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-surface dark:border-gray-600 dark:text-gray-100"
          rows={4}
        />
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          Tip: Include birthday in format "Birthday: MM/DD" for special offers
        </p>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button
          type="button"
          variant="ghost"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          isLoading={isLoading}
        >
          {customer ? 'Update Customer' : 'Add Customer'}
        </Button>
      </div>
    </form>
  );
};

export default CustomerForm;
