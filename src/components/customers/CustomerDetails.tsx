import React, { useState } from 'react';
import { 
  User, 
  Mail, 
  Phone, 
  Calendar,
  DollarSign,
  ShoppingBag,
  Star,
  Gift,
  Edit,
  MessageSquare,
  Send
} from 'lucide-react';
import { CustomerService } from '../../services/customerService';
import { Customer, Order } from '../../types';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Input from '../ui/Input';

interface CustomerDetailsProps {
  customer: Customer;
  orders: Order[];
  onEdit: () => void;
  onClose: () => void;
}

const CustomerDetails: React.FC<CustomerDetailsProps> = ({ customer, orders, onEdit, onClose }) => {
  const [loyaltyPoints, setLoyaltyPoints] = useState(0);
  const [isAddingPoints, setIsAddingPoints] = useState(false);
  const [isRedeemingPoints, setIsRedeemingPoints] = useState(false);

  const handleAddLoyaltyPoints = async () => {
    if (loyaltyPoints <= 0) return;

    try {
      setIsAddingPoints(true);
      await CustomerService.addLoyaltyPoints(customer.id, loyaltyPoints);
      setLoyaltyPoints(0);
      // Refresh customer data would happen in parent component
    } catch (error) {
      console.error('Error adding loyalty points:', error);
    } finally {
      setIsAddingPoints(false);
    }
  };

  const handleRedeemLoyaltyPoints = async () => {
    if (loyaltyPoints <= 0 || loyaltyPoints > customer.loyaltyPoints) return;

    try {
      setIsRedeemingPoints(true);
      await CustomerService.redeemLoyaltyPoints(customer.id, loyaltyPoints);
      setLoyaltyPoints(0);
      // Refresh customer data would happen in parent component
    } catch (error) {
      console.error('Error redeeming loyalty points:', error);
    } finally {
      setIsRedeemingPoints(false);
    }
  };

  const handleSendSMSReceipt = async (order: Order) => {
    if (!customer.phone) {
      alert('Customer has no phone number on file');
      return;
    }

    try {
      await CustomerService.sendSMSReceipt(customer, order.orderNumber, order.total);
      alert('SMS receipt sent successfully');
    } catch (error) {
      console.error('Error sending SMS receipt:', error);
      alert('Failed to send SMS receipt');
    }
  };

  const handleSendEmailReceipt = async (order: Order) => {
    if (!customer.email) {
      alert('Customer has no email address on file');
      return;
    }

    try {
      await CustomerService.sendEmailReceipt(customer, order.orderNumber, order.total, order.items);
      alert('Email receipt sent successfully');
    } catch (error) {
      console.error('Error sending email receipt:', error);
      alert('Failed to send email receipt');
    }
  };

  const formatDate = (date?: Date) => {
    if (!date) return 'Never';
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const loyaltyDiscount = CustomerService.calculateLoyaltyDiscount(customer.loyaltyPoints);

  return (
    <div className="space-y-6">
      {/* Customer Info */}
      <Card variant="neu">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5" />
              Customer Information
            </CardTitle>
            <Button variant="neu" size="sm" onClick={onEdit}>
              <Edit size={14} />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100">
                  {customer.name}
                </h3>
              </div>
              
              {customer.email && (
                <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                  <Mail className="h-4 w-4" />
                  <span>{customer.email}</span>
                </div>
              )}
              
              {customer.phone && (
                <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                  <Phone className="h-4 w-4" />
                  <span>{customer.phone}</span>
                </div>
              )}
              
              <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                <Calendar className="h-4 w-4" />
                <span>Customer since {formatDate(customer.createdAt)}</span>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">Total Spent</span>
                </div>
                <span className="font-bold text-green-600">${customer.totalSpent.toFixed(2)}</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center space-x-2">
                  <ShoppingBag className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">Total Visits</span>
                </div>
                <span className="font-bold text-blue-600">{customer.visitCount}</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Star className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium">Loyalty Points</span>
                </div>
                <span className="font-bold text-yellow-600">{customer.loyaltyPoints}</span>
              </div>
            </div>
          </div>
          
          {customer.notes && (
            <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Notes</h4>
              <p className="text-gray-600 dark:text-gray-400">{customer.notes}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Loyalty Management */}
      <Card variant="neu">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Gift className="mr-2 h-5 w-5" />
            Loyalty Program
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Current Balance</h4>
              <div className="flex items-center space-x-2">
                <Star className="h-6 w-6 text-yellow-500" />
                <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {customer.loyaltyPoints}
                </span>
                <span className="text-gray-500">points</span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Worth ${loyaltyDiscount.toFixed(2)} in rewards
              </p>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  placeholder="Points"
                  value={loyaltyPoints}
                  onChange={(e) => setLoyaltyPoints(parseInt(e.target.value) || 0)}
                  className="flex-1"
                />
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleAddLoyaltyPoints}
                  disabled={loyaltyPoints <= 0}
                  isLoading={isAddingPoints}
                >
                  Add
                </Button>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                className="w-full"
                onClick={handleRedeemLoyaltyPoints}
                disabled={loyaltyPoints <= 0 || loyaltyPoints > customer.loyaltyPoints}
                isLoading={isRedeemingPoints}
              >
                Redeem Points
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order History */}
      <Card variant="neu">
        <CardHeader>
          <CardTitle className="flex items-center">
            <ShoppingBag className="mr-2 h-5 w-5" />
            Order History ({orders.length})
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          {orders.length === 0 ? (
            <div className="text-center py-8">
              <ShoppingBag className="mx-auto h-8 w-8 text-gray-400 mb-2" />
              <p className="text-gray-500 dark:text-gray-400">No orders yet</p>
            </div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {orders.map(order => (
                <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {order.orderNumber}
                      </span>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        order.status === 'paid' 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : order.status === 'voided'
                          ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                      }`}>
                        {order.status.toUpperCase()}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {formatDate(order.createdAt)} • {order.items.length} items
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <span className="font-bold text-gray-900 dark:text-gray-100">
                      ${order.total.toFixed(2)}
                    </span>
                    
                    <div className="flex items-center space-x-1">
                      {customer.phone && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSendSMSReceipt(order)}
                          title="Send SMS Receipt"
                        >
                          <MessageSquare size={14} />
                        </Button>
                      )}
                      
                      {customer.email && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSendEmailReceipt(order)}
                          title="Send Email Receipt"
                        >
                          <Send size={14} />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-end space-x-3">
        <Button variant="ghost" onClick={onClose}>
          Close
        </Button>
      </div>
    </div>
  );
};

export default CustomerDetails;
