import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  User,
  Mail,
  Phone,
  Gift,
  Clock,
  DollarSign,
  ShoppingBag,
  Star
} from 'lucide-react';
import { usePOS } from '../../contexts/POSContext';
import { CustomerService } from '../../services/customerService';
import { Customer, Order } from '../../types';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { Modal, ModalHeader, ModalBody, ModalFooter } from '../ui/Modal';
import CustomerForm from './CustomerForm';
import CustomerDetails from './CustomerDetails';

const CustomerDirectory: React.FC = () => {
  const { state } = usePOS();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [showCustomerForm, setShowCustomerForm] = useState(false);
  const [showCustomerDetails, setShowCustomerDetails] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [customerOrders, setCustomerOrders] = useState<Order[]>([]);
  const [sortBy, setSortBy] = useState<'name' | 'recent' | 'spending'>('name');

  useEffect(() => {
    loadCustomers();
  }, [state.business?.id]);

  useEffect(() => {
    filterCustomers();
  }, [customers, searchQuery, sortBy]);

  useEffect(() => {
    if (selectedCustomer) {
      loadCustomerOrders(selectedCustomer.id);
    }
  }, [selectedCustomer]);

  const loadCustomers = async () => {
    if (!state.business?.id) return;

    try {
      setIsLoading(true);
      const data = await CustomerService.getCustomers(state.business.id);
      setCustomers(data);
    } catch (error) {
      console.error('Error loading customers:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadCustomerOrders = async (customerId: string) => {
    try {
      const orders = await CustomerService.getCustomerOrders(customerId);
      setCustomerOrders(orders);
    } catch (error) {
      console.error('Error loading customer orders:', error);
      setCustomerOrders([]);
    }
  };

  const filterCustomers = () => {
    let filtered = [...customers];

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(customer =>
        customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        customer.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        customer.phone?.includes(searchQuery)
      );
    }

    // Sort customers
    switch (sortBy) {
      case 'recent':
        filtered.sort((a, b) => {
          if (!a.lastVisit) return 1;
          if (!b.lastVisit) return -1;
          return b.lastVisit.getTime() - a.lastVisit.getTime();
        });
        break;
      case 'spending':
        filtered.sort((a, b) => b.totalSpent - a.totalSpent);
        break;
      case 'name':
      default:
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
    }

    setFilteredCustomers(filtered);
  };

  const handleViewCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowCustomerDetails(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowCustomerForm(true);
  };

  const handleDeleteCustomer = async (customerId: string) => {
    if (!confirm('Are you sure you want to delete this customer?')) return;

    try {
      await CustomerService.deleteCustomer(customerId);
      await loadCustomers();
    } catch (error) {
      console.error('Error deleting customer:', error);
    }
  };

  const handleCustomerSaved = () => {
    setShowCustomerForm(false);
    setSelectedCustomer(null);
    loadCustomers();
  };

  const formatDate = (date?: Date) => {
    if (!date) return 'Never';
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i} variant="neu" className="animate-pulse">
            <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Customers</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your customer database and loyalty program
          </p>
        </div>
        
        <Button
          variant="primary"
          leftIcon={<Plus size={16} />}
          onClick={() => {
            setSelectedCustomer(null);
            setShowCustomerForm(true);
          }}
        >
          Add Customer
        </Button>
      </div>

      {/* Search and Sort */}
      <Card variant="neu">
        <CardContent className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="Search customers..."
              leftIcon={<Search size={16} />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <div className="flex items-center space-x-4">
            <label className="text-sm text-gray-700 dark:text-gray-300">Sort by:</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'name' | 'recent' | 'spending')}
              className="px-3 py-2 border border-gray-300 rounded-lg bg-white dark:bg-dark-surface dark:border-gray-600 dark:text-gray-100"
            >
              <option value="name">Name</option>
              <option value="recent">Recent Visit</option>
              <option value="spending">Highest Spending</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Customer List */}
      {filteredCustomers.length === 0 ? (
        <Card variant="neu" className="text-center py-12">
          <User className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            No customers found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {searchQuery
              ? 'Try adjusting your search criteria'
              : 'Add your first customer to start building your database'
            }
          </p>
          <Button
            variant="primary"
            leftIcon={<Plus size={16} />}
            onClick={() => {
              setSelectedCustomer(null);
              setShowCustomerForm(true);
            }}
          >
            Add Customer
          </Button>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredCustomers.map(customer => (
            <Card key={customer.id} variant="neu" className="hover:shadow-neu-pressed transition-shadow">
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center">
                      <User className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                    </div>
                    
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                        {customer.name}
                      </h3>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {customer.email && (
                          <span className="flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {customer.email}
                          </span>
                        )}
                        {customer.phone && (
                          <span className="flex items-center">
                            <Phone className="h-3 w-3 mr-1" />
                            {customer.phone}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-6">
                    <div className="text-right">
                      <div className="flex items-center space-x-1">
                        <DollarSign className="h-3 w-3 text-gray-500" />
                        <p className="font-medium text-gray-900 dark:text-gray-100">
                          ${customer.totalSpent.toFixed(2)}
                        </p>
                      </div>
                      <div className="flex items-center space-x-1 mt-1">
                        <Star className="h-3 w-3 text-yellow-500" />
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {customer.loyaltyPoints} points
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="neu"
                        size="sm"
                        onClick={() => handleViewCustomer(customer)}
                      >
                        View
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditCustomer(customer)}
                      >
                        <Edit size={14} />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteCustomer(customer.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                      >
                        <Trash2 size={14} />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Customer Form Modal */}
      <Modal
        isOpen={showCustomerForm}
        onClose={() => setShowCustomerForm(false)}
        title={selectedCustomer ? 'Edit Customer' : 'Add New Customer'}
        size="md"
      >
        <CustomerForm
          customer={selectedCustomer}
          onSave={handleCustomerSaved}
          onCancel={() => setShowCustomerForm(false)}
        />
      </Modal>

      {/* Customer Details Modal */}
      <Modal
        isOpen={showCustomerDetails}
        onClose={() => setShowCustomerDetails(false)}
        title="Customer Details"
        size="lg"
      >
        {selectedCustomer && (
          <CustomerDetails
            customer={selectedCustomer}
            orders={customerOrders}
            onEdit={() => {
              setShowCustomerDetails(false);
              setShowCustomerForm(true);
            }}
            onClose={() => setShowCustomerDetails(false)}
          />
        )}
      </Modal>
    </div>
  );
};

export default CustomerDirectory;
