import React, { useState, useEffect } from 'react';
import { 
  CreditCard, 
  Smartphone, 
  DollarSign, 
  QrCode,
  Check,
  Copy,
  ExternalLink,
  AlertCircle
} from 'lucide-react';
import { PaymentService } from '../../services/paymentService';
import { PaymentMethod, Payment } from '../../types';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { Modal, ModalHeader, ModalBody, ModalFooter } from '../ui/Modal';

interface PaymentInterfaceProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  orderNumber: string;
  paymentMethods: PaymentMethod[];
  onPaymentComplete: (payment: Payment) => void;
}

const PaymentInterface: React.FC<PaymentInterfaceProps> = ({
  isOpen,
  onClose,
  amount,
  orderNumber,
  paymentMethods,
  onPaymentComplete
}) => {
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null);
  const [paymentInstructions, setPaymentInstructions] = useState<any>(null);
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [cashReceived, setCashReceived] = useState<number>(amount);
  const [splitCount, setSplitCount] = useState<number>(1);
  const [confirmationReference, setConfirmationReference] = useState<string>('');

  useEffect(() => {
    if (selectedMethod) {
      generatePaymentInstructions();
    }
  }, [selectedMethod, amount, orderNumber]);

  const generatePaymentInstructions = async () => {
    if (!selectedMethod) return;

    const instructions = PaymentService.generatePaymentInstructions(
      selectedMethod,
      amount,
      orderNumber
    );
    
    setPaymentInstructions(instructions);

    // Generate QR code if available
    if (instructions.qrCode) {
      try {
        const qrCodeData = await instructions.qrCode;
        setQrCode(qrCodeData);
      } catch (error) {
        console.error('Error generating QR code:', error);
      }
    }
  };

  const handlePaymentMethodSelect = (method: PaymentMethod) => {
    setSelectedMethod(method);
    setQrCode(null);
    setConfirmationReference('');
  };

  const handleCashPayment = async () => {
    if (!selectedMethod) return;

    setIsProcessing(true);
    try {
      const payment = await PaymentService.processManualPayment(
        '', // orderId will be set by parent
        selectedMethod,
        amount,
        `Cash received: $${cashReceived.toFixed(2)}`
      );
      
      onPaymentComplete(payment);
      onClose();
    } catch (error) {
      console.error('Error processing cash payment:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDigitalPaymentConfirmation = async () => {
    if (!selectedMethod || !confirmationReference.trim()) return;

    setIsProcessing(true);
    try {
      const payment = await PaymentService.processManualPayment(
        '', // orderId will be set by parent
        selectedMethod,
        amount,
        confirmationReference
      );
      
      onPaymentComplete(payment);
      onClose();
    } catch (error) {
      console.error('Error processing digital payment:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const calculateChange = () => {
    return Math.max(0, cashReceived - amount);
  };

  const calculateSplitAmount = () => {
    const { amountPerPerson, remainder } = PaymentService.calculateSplitPayment(amount, splitCount);
    return { amountPerPerson, remainder };
  };

  const activePaymentMethods = paymentMethods.filter(method => method.isActive);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Payment Processing"
      size="lg"
      closeOnOverlayClick={false}
    >
      <div className="space-y-6">
        {/* Order Summary */}
        <Card variant="neu">
          <CardContent>
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-semibold text-lg">Order #{orderNumber}</h3>
                <p className="text-gray-600 dark:text-gray-400">Total Amount</p>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                  ${amount.toFixed(2)}
                </p>
                {splitCount > 1 && (
                  <p className="text-sm text-gray-500">
                    ${calculateSplitAmount().amountPerPerson.toFixed(2)} per person
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Split Payment Option */}
        <Card variant="neu">
          <CardContent>
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Split Payment</label>
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  min="1"
                  max="10"
                  value={splitCount}
                  onChange={(e) => setSplitCount(parseInt(e.target.value) || 1)}
                  className="w-16 text-center"
                />
                <span className="text-sm text-gray-500">people</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Method Selection */}
        {!selectedMethod ? (
          <Card variant="neu">
            <CardHeader>
              <CardTitle>Select Payment Method</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {activePaymentMethods.map((method) => {
                  const info = PaymentService.getPaymentMethodInfo(method);
                  return (
                    <Button
                      key={method.id}
                      variant="neu"
                      className="h-auto p-4 flex flex-col items-center space-y-2"
                      onClick={() => handlePaymentMethodSelect(method)}
                    >
                      <span className="text-2xl">{info.icon}</span>
                      <div className="text-center">
                        <p className="font-medium">{info.name}</p>
                        <p className="text-xs text-gray-500">{info.description}</p>
                      </div>
                    </Button>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        ) : (
          /* Payment Processing */
          <Card variant="neu">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <span className="text-2xl mr-2">
                    {PaymentService.getPaymentMethodInfo(selectedMethod).icon}
                  </span>
                  Pay with {selectedMethod.name}
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedMethod(null)}
                >
                  Change Method
                </Button>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {selectedMethod.type === 'cash' ? (
                /* Cash Payment */
                <div className="space-y-4">
                  <Input
                    label="Cash Received"
                    type="number"
                    step="0.01"
                    value={cashReceived}
                    onChange={(e) => setCashReceived(parseFloat(e.target.value) || 0)}
                    leftIcon={<DollarSign size={16} />}
                  />
                  
                  {cashReceived >= amount && (
                    <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-green-800 dark:text-green-400">
                          Change Due:
                        </span>
                        <span className="text-lg font-bold text-green-800 dark:text-green-400">
                          ${calculateChange().toFixed(2)}
                        </span>
                      </div>
                    </div>
                  )}
                  
                  <Button
                    variant="primary"
                    size="lg"
                    className="w-full"
                    onClick={handleCashPayment}
                    disabled={cashReceived < amount || isProcessing}
                    isLoading={isProcessing}
                  >
                    Complete Cash Payment
                  </Button>
                </div>
              ) : (
                /* Digital Payment */
                <div className="space-y-4">
                  {paymentInstructions && (
                    <div className="space-y-3">
                      <h4 className="font-medium">{paymentInstructions.title}</h4>
                      
                      {/* QR Code */}
                      {qrCode && (
                        <div className="flex justify-center">
                          <img
                            src={qrCode}
                            alt="Payment QR Code"
                            className="w-48 h-48 border border-gray-200 dark:border-gray-700 rounded-lg"
                          />
                        </div>
                      )}
                      
                      {/* Payment Link */}
                      {paymentInstructions.paymentUrl && (
                        <div className="flex items-center space-x-2">
                          <Input
                            value={paymentInstructions.paymentUrl}
                            readOnly
                            className="flex-1"
                          />
                          <Button
                            variant="neu"
                            size="sm"
                            onClick={() => copyToClipboard(paymentInstructions.paymentUrl)}
                          >
                            <Copy size={16} />
                          </Button>
                          <Button
                            variant="neu"
                            size="sm"
                            onClick={() => window.open(paymentInstructions.paymentUrl, '_blank')}
                          >
                            <ExternalLink size={16} />
                          </Button>
                        </div>
                      )}
                      
                      {/* Instructions */}
                      <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                        <ol className="list-decimal list-inside space-y-1 text-sm">
                          {paymentInstructions.instructions.map((instruction: string, index: number) => (
                            <li key={index} className="text-blue-800 dark:text-blue-400">
                              {instruction}
                            </li>
                          ))}
                        </ol>
                      </div>
                    </div>
                  )}
                  
                  {/* Confirmation */}
                  <div className="space-y-3">
                    <Input
                      label="Confirmation Reference"
                      placeholder="Enter transaction ID or confirmation code"
                      value={confirmationReference}
                      onChange={(e) => setConfirmationReference(e.target.value)}
                      leftIcon={<Check size={16} />}
                    />
                    
                    <Button
                      variant="primary"
                      size="lg"
                      className="w-full"
                      onClick={handleDigitalPaymentConfirmation}
                      disabled={!confirmationReference.trim() || isProcessing}
                      isLoading={isProcessing}
                    >
                      Confirm Payment Received
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <Button
            variant="ghost"
            onClick={onClose}
            disabled={isProcessing}
          >
            Cancel
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default PaymentInterface;
