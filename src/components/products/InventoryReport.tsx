import React, { useState } from 'react';
import { 
  FileText, 
  Download, 
  Calendar, 
  TrendingUp, 
  TrendingDown,
  Package,
  DollarSign,
  AlertTriangle
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';

interface InventoryReportProps {
  products: any[];
}

const InventoryReport: React.FC<InventoryReportProps> = ({ products }) => {
  const [reportType, setReportType] = useState<'summary' | 'detailed' | 'valuation'>('summary');
  const [dateRange, setDateRange] = useState('today');

  // Calculate inventory metrics
  const metrics = {
    totalItems: products.filter(p => p.isActive).length,
    totalValue: products.reduce((sum, p) => sum + (p.inventory.currentStock * p.cost), 0),
    lowStockItems: products.filter(p => 
      p.inventory.trackInventory && 
      p.inventory.currentStock <= p.inventory.lowStockAlert &&
      p.isActive
    ).length,
    outOfStockItems: products.filter(p => 
      p.inventory.trackInventory && 
      p.inventory.currentStock === 0 &&
      p.isActive
    ).length,
    averageStockLevel: products.reduce((sum, p) => sum + p.inventory.currentStock, 0) / products.length,
    topValueItems: products
      .filter(p => p.isActive)
      .sort((a, b) => (b.inventory.currentStock * b.cost) - (a.inventory.currentStock * a.cost))
      .slice(0, 5),
    lowStockValue: products
      .filter(p => 
        p.inventory.trackInventory && 
        p.inventory.currentStock <= p.inventory.lowStockAlert &&
        p.isActive
      )
      .reduce((sum, p) => sum + (p.inventory.currentStock * p.cost), 0)
  };

  const generateReport = () => {
    // In a real app, this would generate and download a PDF/Excel report
    const reportData = {
      type: reportType,
      dateRange,
      generatedAt: new Date().toISOString(),
      metrics,
      products: products.filter(p => p.isActive)
    };
    
    console.log('Generated Report:', reportData);
    alert('Report generated! Check console for data.');
  };

  return (
    <div className="space-y-6">
      {/* Report Controls */}
      <Card variant="neu">
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            Inventory Reports
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Report Type
                </label>
                <select
                  value={reportType}
                  onChange={(e) => setReportType(e.target.value as any)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="summary">Summary Report</option>
                  <option value="detailed">Detailed Inventory</option>
                  <option value="valuation">Inventory Valuation</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date Range
                </label>
                <select
                  value={dateRange}
                  onChange={(e) => setDateRange(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="quarter">This Quarter</option>
                  <option value="year">This Year</option>
                </select>
              </div>
            </div>

            <Button
              variant="primary"
              onClick={generateReport}
              leftIcon={<Download size={16} />}
            >
              Generate Report
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Summary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card variant="neu">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-blue-100 mr-4">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Items</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.totalItems}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="neu">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-green-100 mr-4">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Value</p>
                <p className="text-2xl font-bold text-gray-900">${metrics.totalValue.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="neu">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-yellow-100 mr-4">
                <AlertTriangle className="h-6 w-6 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Low Stock</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.lowStockItems}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="neu">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-red-100 mr-4">
                <TrendingDown className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Out of Stock</p>
                <p className="text-2xl font-bold text-gray-900">{metrics.outOfStockItems}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Report Content */}
      {reportType === 'summary' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Value Items */}
          <Card variant="neu">
            <CardHeader>
              <CardTitle>Top Value Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {metrics.topValueItems.map((item, index) => (
                  <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{item.category.icon}</span>
                      <div>
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-gray-600">
                          {item.inventory.currentStock} {item.inventory.unit} × ${item.cost.toFixed(2)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-green-600">
                        ${(item.inventory.currentStock * item.cost).toFixed(2)}
                      </p>
                      <p className="text-xs text-gray-500">#{index + 1}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Stock Status Overview */}
          <Card variant="neu">
            <CardHeader>
              <CardTitle>Stock Status Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                    <span className="font-medium">In Stock</span>
                  </div>
                  <span className="font-bold">
                    {products.filter(p => 
                      p.inventory.trackInventory && 
                      p.inventory.currentStock > p.inventory.lowStockAlert &&
                      p.isActive
                    ).length}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                    <span className="font-medium">Low Stock</span>
                  </div>
                  <span className="font-bold">{metrics.lowStockItems}</span>
                </div>

                <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                    <span className="font-medium">Out of Stock</span>
                  </div>
                  <span className="font-bold">{metrics.outOfStockItems}</span>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-gray-500 rounded-full mr-3"></div>
                    <span className="font-medium">Not Tracked</span>
                  </div>
                  <span className="font-bold">
                    {products.filter(p => !p.inventory.trackInventory && p.isActive).length}
                  </span>
                </div>
              </div>

              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-700">
                  <strong>Average Stock Level:</strong> {metrics.averageStockLevel.toFixed(1)} units
                </p>
                <p className="text-sm text-blue-700 mt-1">
                  <strong>Low Stock Value:</strong> ${metrics.lowStockValue.toFixed(2)}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {reportType === 'detailed' && (
        <Card variant="neu">
          <CardHeader>
            <CardTitle>Detailed Inventory Report</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50 border-b border-gray-200">
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Item</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Category</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Current Stock</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Alert Level</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Unit Cost</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total Value</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {products.filter(p => p.isActive).map((product) => {
                    const isLowStock = product.inventory.trackInventory && 
                                      product.inventory.currentStock <= product.inventory.lowStockAlert;
                    const isOutOfStock = product.inventory.trackInventory && 
                                        product.inventory.currentStock === 0;
                    
                    return (
                      <tr key={product.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4">
                          <div className="flex items-center">
                            <span className="text-lg mr-2">{product.category.icon}</span>
                            <div>
                              <p className="font-medium">{product.name}</p>
                              <p className="text-sm text-gray-500">{product.sku}</p>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-4">
                          <span className="px-2 py-1 text-xs rounded-full bg-primary-100 text-primary-700">
                            {product.category.name}
                          </span>
                        </td>
                        <td className="px-4 py-4">
                          {product.inventory.trackInventory ? (
                            <span className={`font-medium ${
                              isOutOfStock ? 'text-red-600' : 
                              isLowStock ? 'text-yellow-600' : 'text-green-600'
                            }`}>
                              {product.inventory.currentStock} {product.inventory.unit}
                            </span>
                          ) : (
                            <span className="text-gray-500">Not tracked</span>
                          )}
                        </td>
                        <td className="px-4 py-4">
                          {product.inventory.trackInventory ? (
                            <span>{product.inventory.lowStockAlert} {product.inventory.unit}</span>
                          ) : (
                            <span className="text-gray-500">-</span>
                          )}
                        </td>
                        <td className="px-4 py-4">${product.cost.toFixed(2)}</td>
                        <td className="px-4 py-4 font-medium">
                          ${(product.inventory.currentStock * product.cost).toFixed(2)}
                        </td>
                        <td className="px-4 py-4">
                          {!product.inventory.trackInventory ? (
                            <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">
                              Not Tracked
                            </span>
                          ) : isOutOfStock ? (
                            <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">
                              Out of Stock
                            </span>
                          ) : isLowStock ? (
                            <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">
                              Low Stock
                            </span>
                          ) : (
                            <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                              In Stock
                            </span>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {reportType === 'valuation' && (
        <Card variant="neu">
          <CardHeader>
            <CardTitle>Inventory Valuation Report</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-4">Valuation by Category</h4>
                <div className="space-y-3">
                  {Array.from(new Set(products.map(p => p.category.name))).map(categoryName => {
                    const categoryProducts = products.filter(p => p.category.name === categoryName && p.isActive);
                    const categoryValue = categoryProducts.reduce((sum, p) => sum + (p.inventory.currentStock * p.cost), 0);
                    const categoryIcon = categoryProducts[0]?.category.icon;
                    
                    return (
                      <div key={categoryName} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center">
                          <span className="text-lg mr-2">{categoryIcon}</span>
                          <span className="font-medium">{categoryName}</span>
                        </div>
                        <span className="font-bold">${categoryValue.toFixed(2)}</span>
                      </div>
                    );
                  })}
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-4">Valuation Summary</h4>
                <div className="space-y-3">
                  <div className="flex justify-between p-3 bg-green-50 rounded-lg">
                    <span>Total Inventory Value:</span>
                    <span className="font-bold text-green-600">${metrics.totalValue.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between p-3 bg-yellow-50 rounded-lg">
                    <span>Low Stock Value:</span>
                    <span className="font-bold text-yellow-600">${metrics.lowStockValue.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between p-3 bg-blue-50 rounded-lg">
                    <span>Average Item Value:</span>
                    <span className="font-bold text-blue-600">
                      ${(metrics.totalValue / metrics.totalItems).toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default InventoryReport;
