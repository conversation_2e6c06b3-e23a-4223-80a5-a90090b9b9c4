import React from 'react';
import { Edit, Trash2, Package, AlertTriangle } from 'lucide-react';
import { Card, CardContent } from '../ui/Card';
import Button from '../ui/Button';

interface ProductListProps {
  products: any[];
  onEdit: (product: any) => void;
  onDelete: (productId: string) => void;
  onInventoryAdjust: (product: any) => void;
}

const ProductList: React.FC<ProductListProps> = ({
  products,
  onEdit,
  onDelete,
  onInventoryAdjust
}) => {
  return (
    <Card variant="neu">
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-50 border-b border-gray-200">
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Item
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Inventory
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  SKU
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {products.map((product) => {
                const isLowStock = product.inventory.trackInventory && 
                                  product.inventory.currentStock <= product.inventory.lowStockAlert;
                const isOutOfStock = product.inventory.trackInventory && 
                                    product.inventory.currentStock === 0;
                
                return (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 bg-primary-100 rounded-lg flex items-center justify-center text-xl">
                          {product.category.icon || '🍗'}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{product.name}</div>
                          <div className="text-xs text-gray-500 line-clamp-1 max-w-xs">{product.description}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <span className="px-2 py-1 text-xs rounded-full bg-primary-100 text-primary-700">
                        {product.category.name}
                      </span>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">${product.price.toFixed(2)}</div>
                      <div className="text-xs text-gray-500">Cost: ${product.cost.toFixed(2)}</div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      {product.inventory.trackInventory ? (
                        <div>
                          <div className="flex items-center">
                            {isOutOfStock && <AlertTriangle className="h-4 w-4 text-red-500 mr-1" />}
                            {isLowStock && !isOutOfStock && <AlertTriangle className="h-4 w-4 text-yellow-500 mr-1" />}
                            <span className={`text-sm font-medium ${
                              isOutOfStock ? 'text-red-600' : 
                              isLowStock ? 'text-yellow-600' : 'text-green-600'
                            }`}>
                              {product.inventory.currentStock} {product.inventory.unit}
                            </span>
                          </div>
                          <div className="text-xs text-gray-500">
                            Alert at {product.inventory.lowStockAlert}
                          </div>
                        </div>
                      ) : (
                        <span className="text-xs text-gray-500">Not tracked</span>
                      )}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
                      {product.sku || '-'}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="neu"
                          size="xs"
                          onClick={() => onInventoryAdjust(product)}
                          leftIcon={<Package size={14} />}
                        >
                          Stock
                        </Button>
                        <Button
                          variant="neu"
                          size="xs"
                          onClick={() => onEdit(product)}
                          leftIcon={<Edit size={14} />}
                        >
                          Edit
                        </Button>
                        <Button
                          variant="ghost"
                          size="xs"
                          onClick={() => onDelete(product.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 size={14} />
                        </Button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductList;
