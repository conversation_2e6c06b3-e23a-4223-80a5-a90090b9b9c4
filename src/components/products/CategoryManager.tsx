import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Tag, Palette } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { usePOS } from '../../contexts/POSContext';
import { ProductService } from '../../services/productService';
import { Category } from '../../types';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { Modal, ModalHeader, ModalBody, ModalFooter } from '../ui/Modal';

interface CategoryFormData {
  name: string;
  color: string;
  icon: string;
  sortOrder: number;
}

const CategoryManager: React.FC = () => {
  const { state } = usePOS();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);

  const { register, handleSubmit, reset, formState: { errors } } = useForm<CategoryFormData>();

  const colorOptions = [
    { name: 'Blue', value: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' },
    { name: 'Green', value: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' },
    { name: 'Purple', value: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400' },
    { name: 'Red', value: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' },
    { name: 'Yellow', value: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' },
    { name: 'Indigo', value: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400' },
    { name: 'Pink', value: 'bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400' },
    { name: 'Gray', value: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400' }
  ];

  useEffect(() => {
    loadCategories();
  }, [state.business?.id]);

  const loadCategories = async () => {
    if (!state.business?.id) return;

    try {
      setIsLoading(true);
      const data = await ProductService.getCategories(state.business.id);
      setCategories(data);
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    reset({
      name: category.name,
      color: category.color,
      icon: category.icon || '',
      sortOrder: category.sortOrder
    });
    setShowForm(true);
  };

  const handleDelete = async (categoryId: string) => {
    if (!confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      return;
    }

    try {
      await ProductService.deleteCategory(categoryId);
      await loadCategories();
    } catch (error) {
      console.error('Error deleting category:', error);
    }
  };

  const onSubmit = async (data: CategoryFormData) => {
    if (!state.business?.id) return;

    try {
      if (editingCategory) {
        await ProductService.updateCategory(editingCategory.id, data);
      } else {
        await ProductService.createCategory(state.business.id, data);
      }
      
      setShowForm(false);
      setEditingCategory(null);
      reset();
      await loadCategories();
    } catch (error) {
      console.error('Error saving category:', error);
    }
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingCategory(null);
    reset();
  };

  if (isLoading) {
    return (
      <Card variant="neu">
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card variant="neu">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Tag className="mr-2 h-5 w-5" />
              Categories
            </CardTitle>
            <Button
              variant="primary"
              size="sm"
              leftIcon={<Plus size={16} />}
              onClick={() => setShowForm(true)}
            >
              Add Category
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          {categories.length === 0 ? (
            <div className="text-center py-8">
              <Tag className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No categories yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Create categories to organize your products
              </p>
              <Button
                variant="primary"
                leftIcon={<Plus size={16} />}
                onClick={() => setShowForm(true)}
              >
                Add First Category
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {categories.map((category) => (
                <div
                  key={category.id}
                  className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${category.color}`}>
                      {category.icon && <span className="mr-1">{category.icon}</span>}
                      {category.name}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      Order: {category.sortOrder}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(category)}
                    >
                      <Edit size={14} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(category.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                    >
                      <Trash2 size={14} />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Category Form Modal */}
      <Modal
        isOpen={showForm}
        onClose={handleCancel}
        title={editingCategory ? 'Edit Category' : 'Add New Category'}
        size="md"
      >
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <Input
            label="Category Name *"
            placeholder="Enter category name"
            {...register('name', { required: 'Category name is required' })}
            error={errors.name?.message}
          />

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Color Theme
            </label>
            <div className="grid grid-cols-4 gap-2">
              {colorOptions.map((color) => (
                <label key={color.name} className="cursor-pointer">
                  <input
                    type="radio"
                    value={color.value}
                    {...register('color', { required: 'Please select a color' })}
                    className="sr-only"
                  />
                  <div className={`p-3 rounded-lg text-center text-sm font-medium transition-all ${color.value} hover:scale-105`}>
                    {color.name}
                  </div>
                </label>
              ))}
            </div>
            {errors.color && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.color.message}
              </p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Icon (Emoji)"
              placeholder="🍕"
              {...register('icon')}
            />
            
            <Input
              label="Sort Order"
              type="number"
              placeholder="1"
              {...register('sortOrder', { 
                required: 'Sort order is required',
                min: { value: 1, message: 'Sort order must be at least 1' }
              })}
              error={errors.sortOrder?.message}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
            >
              {editingCategory ? 'Update Category' : 'Create Category'}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default CategoryManager;
