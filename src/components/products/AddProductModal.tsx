import React, { useState } from 'react';
import { X, Plus, Trash2 } from 'lucide-react';
import Button from '../ui/Button';

interface AddProductModalProps {
  categories: any[];
  onSave: (productData: any) => void;
  onClose: () => void;
}

const AddProductModal: React.FC<AddProductModalProps> = ({
  categories,
  onSave,
  onClose
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    cost: '',
    sku: '',
    barcode: '',
    categoryId: categories[0]?.id || '',
    trackInventory: true,
    currentStock: '',
    lowStockAlert: '',
    unit: 'pieces',
    modifiers: [] as any[]
  });

  const [newModifier, setNewModifier] = useState({
    name: '',
    type: 'single',
    required: false,
    options: [{ name: '', price: '' }]
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAddModifier = () => {
    if (newModifier.name.trim()) {
      const modifier = {
        id: `modifier-${Date.now()}`,
        name: newModifier.name,
        type: newModifier.type,
        required: newModifier.required,
        options: newModifier.options
          .filter(opt => opt.name.trim())
          .map((opt, index) => ({
            id: `option-${Date.now()}-${index}`,
            name: opt.name,
            price: parseFloat(opt.price) || 0
          }))
      };

      setFormData(prev => ({
        ...prev,
        modifiers: [...prev.modifiers, modifier]
      }));

      setNewModifier({
        name: '',
        type: 'single',
        required: false,
        options: [{ name: '', price: '' }]
      });
    }
  };

  const handleRemoveModifier = (index: number) => {
    setFormData(prev => ({
      ...prev,
      modifiers: prev.modifiers.filter((_, i) => i !== index)
    }));
  };

  const handleAddModifierOption = () => {
    setNewModifier(prev => ({
      ...prev,
      options: [...prev.options, { name: '', price: '' }]
    }));
  };

  const handleRemoveModifierOption = (index: number) => {
    setNewModifier(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index)
    }));
  };

  const handleModifierOptionChange = (index: number, field: string, value: string) => {
    setNewModifier(prev => ({
      ...prev,
      options: prev.options.map((opt, i) => 
        i === index ? { ...opt, [field]: value } : opt
      )
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const selectedCategory = categories.find(cat => cat.id === formData.categoryId);
    
    const productData = {
      name: formData.name,
      description: formData.description,
      price: parseFloat(formData.price),
      cost: parseFloat(formData.cost) || 0,
      sku: formData.sku || null,
      barcode: formData.barcode || null,
      category: selectedCategory,
      modifiers: formData.modifiers,
      inventory: {
        trackInventory: formData.trackInventory,
        currentStock: formData.trackInventory ? parseInt(formData.currentStock) || 0 : 0,
        lowStockAlert: formData.trackInventory ? parseInt(formData.lowStockAlert) || 5 : 0,
        unit: formData.unit
      }
    };

    onSave(productData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Add New Menu Item</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Item Name *
              </label>
              <input
                type="text"
                required
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="e.g., Spicy Chicken Sandwich"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category *
              </label>
              <select
                required
                value={formData.categoryId}
                onChange={(e) => handleInputChange('categoryId', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.icon} {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="Describe the menu item..."
            />
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Selling Price * ($)
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                required
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="0.00"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cost Price ($)
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.cost}
                onChange={(e) => handleInputChange('cost', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="0.00"
              />
            </div>
          </div>

          {/* SKU and Barcode */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                SKU
              </label>
              <input
                type="text"
                value={formData.sku}
                onChange={(e) => handleInputChange('sku', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="e.g., SCS001"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Barcode
              </label>
              <input
                type="text"
                value={formData.barcode}
                onChange={(e) => handleInputChange('barcode', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="e.g., 123456789001"
              />
            </div>
          </div>

          {/* Inventory Management */}
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center mb-4">
              <input
                type="checkbox"
                id="trackInventory"
                checked={formData.trackInventory}
                onChange={(e) => handleInputChange('trackInventory', e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label htmlFor="trackInventory" className="ml-2 text-sm font-medium text-gray-700">
                Track inventory for this item
              </label>
            </div>

            {formData.trackInventory && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Current Stock
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={formData.currentStock}
                    onChange={(e) => handleInputChange('currentStock', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Low Stock Alert
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={formData.lowStockAlert}
                    onChange={(e) => handleInputChange('lowStockAlert', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="5"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Unit
                  </label>
                  <select
                    value={formData.unit}
                    onChange={(e) => handleInputChange('unit', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="pieces">Pieces</option>
                    <option value="servings">Servings</option>
                    <option value="lbs">Pounds</option>
                    <option value="kg">Kilograms</option>
                    <option value="cups">Cups</option>
                    <option value="bottles">Bottles</option>
                  </select>
                </div>
              </div>
            )}
          </div>

          {/* Modifiers */}
          <div className="border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Modifiers</h3>
            
            {/* Existing Modifiers */}
            {formData.modifiers.length > 0 && (
              <div className="mb-4 space-y-2">
                {formData.modifiers.map((modifier, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <span className="font-medium">{modifier.name}</span>
                      <span className="text-sm text-gray-500 ml-2">
                        ({modifier.type}, {modifier.required ? 'required' : 'optional'})
                      </span>
                      <div className="text-xs text-gray-500 mt-1">
                        {modifier.options.length} options
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveModifier(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {/* Add New Modifier */}
            <div className="space-y-4 border-t border-gray-200 pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Modifier Name
                  </label>
                  <input
                    type="text"
                    value={newModifier.name}
                    onChange={(e) => setNewModifier(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="e.g., Spice Level"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Type
                  </label>
                  <select
                    value={newModifier.type}
                    onChange={(e) => setNewModifier(prev => ({ ...prev, type: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="single">Single Choice</option>
                    <option value="multiple">Multiple Choice</option>
                  </select>
                </div>

                <div className="flex items-end">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={newModifier.required}
                      onChange={(e) => setNewModifier(prev => ({ ...prev, required: e.target.checked }))}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700">Required</span>
                  </label>
                </div>
              </div>

              {/* Modifier Options */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Options
                </label>
                <div className="space-y-2">
                  {newModifier.options.map((option, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={option.name}
                        onChange={(e) => handleModifierOptionChange(index, 'name', e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="Option name"
                      />
                      <input
                        type="number"
                        step="0.01"
                        value={option.price}
                        onChange={(e) => handleModifierOptionChange(index, 'price', e.target.value)}
                        className="w-24 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="0.00"
                      />
                      {newModifier.options.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveModifierOption(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 size={16} />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
                
                <div className="flex space-x-2 mt-2">
                  <Button
                    type="button"
                    variant="neu"
                    size="sm"
                    onClick={handleAddModifierOption}
                    leftIcon={<Plus size={16} />}
                  >
                    Add Option
                  </Button>
                  <Button
                    type="button"
                    variant="primary"
                    size="sm"
                    onClick={handleAddModifier}
                    disabled={!newModifier.name.trim()}
                  >
                    Add Modifier
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <Button
              type="button"
              variant="neu"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
            >
              Add Menu Item
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddProductModal;

// EditProductModal - Similar to AddProductModal but pre-populated with existing data
export const EditProductModal: React.FC<{
  product: any;
  categories: any[];
  onSave: (productData: any) => void;
  onClose: () => void;
}> = ({ product, categories, onSave, onClose }) => {
  const [formData, setFormData] = useState({
    name: product.name || '',
    description: product.description || '',
    price: product.price?.toString() || '',
    cost: product.cost?.toString() || '',
    sku: product.sku || '',
    barcode: product.barcode || '',
    categoryId: product.category?.id || categories[0]?.id || '',
    trackInventory: product.inventory?.trackInventory || false,
    currentStock: product.inventory?.currentStock?.toString() || '',
    lowStockAlert: product.inventory?.lowStockAlert?.toString() || '',
    unit: product.inventory?.unit || 'pieces',
    modifiers: product.modifiers || []
  });

  // Rest of the component logic is identical to AddProductModal
  // Just change the title and submit button text

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const selectedCategory = categories.find(cat => cat.id === formData.categoryId);

    const productData = {
      name: formData.name,
      description: formData.description,
      price: parseFloat(formData.price),
      cost: parseFloat(formData.cost) || 0,
      sku: formData.sku || null,
      barcode: formData.barcode || null,
      category: selectedCategory,
      modifiers: formData.modifiers,
      inventory: {
        trackInventory: formData.trackInventory,
        currentStock: formData.trackInventory ? parseInt(formData.currentStock) || 0 : 0,
        lowStockAlert: formData.trackInventory ? parseInt(formData.lowStockAlert) || 5 : 0,
        unit: formData.unit
      }
    };

    onSave(productData);
  };

  // Return similar JSX but with "Edit Menu Item" title and "Update Menu Item" button
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Edit Menu Item</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X size={24} />
          </button>
        </div>
        {/* Form content identical to AddProductModal but with different submit button */}
        <div className="p-6">
          <p className="text-sm text-gray-600 mb-4">
            Editing: <strong>{product.name}</strong>
          </p>
          {/* Add the same form structure here */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <Button type="button" variant="neu" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" variant="primary" onClick={handleSubmit}>
              Update Menu Item
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
