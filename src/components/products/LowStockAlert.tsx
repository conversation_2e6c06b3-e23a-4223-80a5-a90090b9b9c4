import React from 'react';
import { AlertTriangle, Package, RefreshCw } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';

interface LowStockAlertProps {
  products: any[];
  onRestock: (product: any) => void;
}

const LowStockAlert: React.FC<LowStockAlertProps> = ({ products, onRestock }) => {
  if (products.length === 0) return null;

  return (
    <Card variant="neu" className="border-l-4 border-l-yellow-500">
      <CardHeader>
        <CardTitle className="flex items-center text-yellow-700">
          <AlertTriangle className="mr-2 h-5 w-5" />
          Low Stock Alert ({products.length} items)
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {products.map((product) => {
            const isOutOfStock = product.inventory.currentStock === 0;
            
            return (
              <div 
                key={product.id} 
                className={`flex items-center justify-between p-4 rounded-lg border-2 ${
                  isOutOfStock 
                    ? 'bg-red-50 border-red-200' 
                    : 'bg-yellow-50 border-yellow-200'
                }`}
              >
                <div className="flex items-center space-x-4">
                  <div className="h-12 w-12 bg-white rounded-lg flex items-center justify-center text-xl shadow-sm">
                    {product.category.icon || '🍗'}
                  </div>
                  <div>
                    <h4 className={`font-medium ${
                      isOutOfStock ? 'text-red-900' : 'text-yellow-900'
                    }`}>
                      {product.name}
                    </h4>
                    <div className="flex items-center space-x-4 text-sm">
                      <span className={`font-medium ${
                        isOutOfStock ? 'text-red-700' : 'text-yellow-700'
                      }`}>
                        Current: {product.inventory.currentStock} {product.inventory.unit}
                      </span>
                      <span className="text-gray-600">
                        Alert at: {product.inventory.lowStockAlert} {product.inventory.unit}
                      </span>
                      {product.sku && (
                        <span className="text-gray-500 font-mono">
                          SKU: {product.sku}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                    isOutOfStock 
                      ? 'bg-red-100 text-red-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {isOutOfStock ? 'Out of Stock' : 'Low Stock'}
                  </div>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={() => onRestock(product)}
                    leftIcon={<RefreshCw size={14} />}
                  >
                    Restock
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start space-x-2">
            <Package className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h5 className="font-medium text-blue-900">Inventory Management Tips</h5>
              <ul className="text-sm text-blue-700 mt-1 space-y-1">
                <li>• Set appropriate low stock alerts based on your sales volume</li>
                <li>• Track popular items more closely during peak hours</li>
                <li>• Consider automatic reordering for high-volume items</li>
                <li>• Review and adjust stock levels regularly</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LowStockAlert;
