import React from 'react';
import { Edit, Trash2, Package, AlertTriangle, TrendingUp, TrendingDown } from 'lucide-react';
import { Card, CardContent } from '../ui/Card';
import Button from '../ui/Button';

interface ProductCardProps {
  product: any;
  onEdit: (product: any) => void;
  onDelete: (productId: string) => void;
  onInventoryAdjust: (product: any) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onEdit,
  onDelete,
  onInventoryAdjust
}) => {
  const isLowStock = product.inventory.trackInventory && 
                    product.inventory.currentStock <= product.inventory.lowStockAlert;
  const isOutOfStock = product.inventory.trackInventory && 
                      product.inventory.currentStock === 0;

  const getStockStatus = () => {
    if (!product.inventory.trackInventory) return null;
    
    if (isOutOfStock) {
      return { text: 'Out of Stock', color: 'text-red-600', bg: 'bg-red-100' };
    } else if (isLowStock) {
      return { text: 'Low Stock', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    } else {
      return { text: 'In Stock', color: 'text-green-600', bg: 'bg-green-100' };
    }
  };

  const stockStatus = getStockStatus();

  return (
    <Card variant="neu" className="overflow-hidden hover:shadow-lg transition-shadow">
      <CardContent className="p-0">
        {/* Product Image */}
        <div className="h-48 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center relative">
          {product.image ? (
            <img 
              src={product.image} 
              alt={product.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="text-6xl">
              {product.category.icon || '🍗'}
            </div>
          )}
          
          {/* Stock Status Badge */}
          {stockStatus && (
            <div className={`absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium ${stockStatus.bg} ${stockStatus.color}`}>
              {stockStatus.text}
            </div>
          )}
          
          {/* Category Badge */}
          <div className="absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-700">
            {product.category.icon} {product.category.name}
          </div>
        </div>

        {/* Product Info */}
        <div className="p-4">
          <div className="mb-3">
            <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2">{product.name}</h3>
            <p className="text-sm text-gray-600 line-clamp-2">{product.description}</p>
          </div>

          {/* Price and SKU */}
          <div className="flex items-center justify-between mb-3">
            <div>
              <p className="text-lg font-bold text-primary-600">${product.price.toFixed(2)}</p>
              <p className="text-xs text-gray-500">Cost: ${product.cost.toFixed(2)}</p>
            </div>
            {product.sku && (
              <div className="text-right">
                <p className="text-xs text-gray-500">SKU</p>
                <p className="text-sm font-mono">{product.sku}</p>
              </div>
            )}
          </div>

          {/* Inventory Info */}
          {product.inventory.trackInventory && (
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Inventory</span>
                <button
                  onClick={() => onInventoryAdjust(product)}
                  className="text-xs text-primary-600 hover:text-primary-700"
                >
                  Adjust
                </button>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Current Stock:</span>
                <span className={`font-medium ${isLowStock ? 'text-red-600' : 'text-gray-900'}`}>
                  {product.inventory.currentStock} {product.inventory.unit}
                </span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Alert Level:</span>
                <span className="text-gray-900">{product.inventory.lowStockAlert}</span>
              </div>

              {/* Stock Level Bar */}
              <div className="mt-2">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all ${
                      isOutOfStock ? 'bg-red-500' : 
                      isLowStock ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ 
                      width: `${Math.min(100, (product.inventory.currentStock / (product.inventory.lowStockAlert * 2)) * 100)}%` 
                    }}
                  ></div>
                </div>
              </div>
            </div>
          )}

          {/* Modifiers */}
          {product.modifiers && product.modifiers.length > 0 && (
            <div className="mb-4">
              <p className="text-sm font-medium text-gray-700 mb-2">Modifiers:</p>
              <div className="flex flex-wrap gap-1">
                {product.modifiers.map((modifier: any, index: number) => (
                  <span 
                    key={index}
                    className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full"
                  >
                    {modifier.name}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-2">
            <Button
              variant="neu"
              size="sm"
              onClick={() => onEdit(product)}
              className="flex-1"
              leftIcon={<Edit size={14} />}
            >
              Edit
            </Button>
            <Button
              variant="neu"
              size="sm"
              onClick={() => onInventoryAdjust(product)}
              leftIcon={<Package size={14} />}
            >
              Stock
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(product.id)}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 size={14} />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductCard;
