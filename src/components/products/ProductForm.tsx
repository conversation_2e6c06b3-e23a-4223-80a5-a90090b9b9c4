import React, { useState, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { Upload, X, Plus, Minus } from 'lucide-react';
import { usePOS } from '../../contexts/POSContext';
import { ProductService } from '../../services/productService';
import { Product, Category, Modifier, ModifierOption } from '../../types';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { Card, CardContent } from '../ui/Card';

interface ProductFormData {
  name: string;
  description: string;
  price: number;
  cost: number;
  sku: string;
  barcode: string;
  categoryId: string;
  trackInventory: boolean;
  currentStock: number;
  lowStockAlert: number;
  unit: string;
}

interface ProductFormProps {
  product?: Product | null;
  categories: Category[];
  onSave: () => void;
  onCancel: () => void;
}

const ProductForm: React.FC<ProductFormProps> = ({ product, categories, onSave, onCancel }) => {
  const { state } = usePOS();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(product?.image || null);
  const [modifiers, setModifiers] = useState<Modifier[]>(product?.modifiers || []);

  const { register, handleSubmit, watch, formState: { errors } } = useForm<ProductFormData>({
    defaultValues: {
      name: product?.name || '',
      description: product?.description || '',
      price: product?.price || 0,
      cost: product?.cost || 0,
      sku: product?.sku || '',
      barcode: product?.barcode || '',
      categoryId: product?.category.id || (categories[0]?.id || ''),
      trackInventory: product?.inventory.trackInventory || false,
      currentStock: product?.inventory.currentStock || 0,
      lowStockAlert: product?.inventory.lowStockAlert || 5,
      unit: product?.inventory.unit || 'pcs'
    }
  });

  const trackInventory = watch('trackInventory');

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setImageFile(null);
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const addModifier = () => {
    const newModifier: Modifier = {
      id: `mod_${Date.now()}`,
      name: '',
      type: 'single',
      required: false,
      options: []
    };
    setModifiers([...modifiers, newModifier]);
  };

  const updateModifier = (index: number, updates: Partial<Modifier>) => {
    const updated = [...modifiers];
    updated[index] = { ...updated[index], ...updates };
    setModifiers(updated);
  };

  const removeModifier = (index: number) => {
    setModifiers(modifiers.filter((_, i) => i !== index));
  };

  const addModifierOption = (modifierIndex: number) => {
    const newOption: ModifierOption = {
      id: `opt_${Date.now()}`,
      name: '',
      priceAdjustment: 0
    };
    const updated = [...modifiers];
    updated[modifierIndex].options.push(newOption);
    setModifiers(updated);
  };

  const updateModifierOption = (modifierIndex: number, optionIndex: number, updates: Partial<ModifierOption>) => {
    const updated = [...modifiers];
    updated[modifierIndex].options[optionIndex] = { ...updated[modifierIndex].options[optionIndex], ...updates };
    setModifiers(updated);
  };

  const removeModifierOption = (modifierIndex: number, optionIndex: number) => {
    const updated = [...modifiers];
    updated[modifierIndex].options = updated[modifierIndex].options.filter((_, i) => i !== optionIndex);
    setModifiers(updated);
  };

  const onSubmit = async (data: ProductFormData) => {
    if (!state.business?.id) return;

    setIsLoading(true);
    try {
      const selectedCategory = categories.find(cat => cat.id === data.categoryId);
      if (!selectedCategory) {
        throw new Error('Selected category not found');
      }

      const productData = {
        businessId: state.business.id,
        name: data.name,
        description: data.description,
        price: data.price,
        cost: data.cost,
        sku: data.sku,
        barcode: data.barcode,
        category: selectedCategory,
        modifiers: modifiers.filter(mod => mod.name.trim() !== ''),
        inventory: {
          trackInventory: data.trackInventory,
          currentStock: data.trackInventory ? data.currentStock : 0,
          lowStockAlert: data.trackInventory ? data.lowStockAlert : 0,
          unit: data.unit
        },
        isActive: true
      };

      let productId: string;
      if (product) {
        await ProductService.updateProduct(product.id, productData);
        productId = product.id;
      } else {
        productId = await ProductService.createProduct(productData);
      }

      // Upload image if provided
      if (imageFile) {
        await ProductService.uploadProductImage(productId, imageFile);
      }

      onSave();
    } catch (error) {
      console.error('Error saving product:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Basic Information */}
      <Card variant="neu">
        <CardContent>
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">
            Basic Information
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Product Name *"
              placeholder="Enter product name"
              {...register('name', { required: 'Product name is required' })}
              error={errors.name?.message}
            />
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Category *
              </label>
              <select
                {...register('categoryId', { required: 'Category is required' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white dark:bg-dark-surface dark:border-gray-600 dark:text-gray-100"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.categoryId && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.categoryId.message}
                </p>
              )}
            </div>
          </div>

          <div className="mt-4">
            <Input
              label="Description"
              placeholder="Enter product description"
              {...register('description')}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <Input
              label="Price *"
              type="number"
              step="0.01"
              placeholder="0.00"
              {...register('price', { 
                required: 'Price is required',
                min: { value: 0, message: 'Price must be positive' }
              })}
              error={errors.price?.message}
            />
            
            <Input
              label="Cost"
              type="number"
              step="0.01"
              placeholder="0.00"
              {...register('cost', { 
                min: { value: 0, message: 'Cost must be positive' }
              })}
              error={errors.cost?.message}
            />
            
            <Input
              label="SKU"
              placeholder="Enter SKU"
              {...register('sku')}
            />
          </div>
        </CardContent>
      </Card>

      {/* Image Upload */}
      <Card variant="neu">
        <CardContent>
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">
            Product Image
          </h3>
          
          <div className="flex items-center space-x-4">
            {imagePreview ? (
              <div className="relative">
                <img
                  src={imagePreview}
                  alt="Product preview"
                  className="w-24 h-24 object-cover rounded-lg"
                />
                <button
                  type="button"
                  onClick={removeImage}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X size={12} />
                </button>
              </div>
            ) : (
              <div className="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                <Upload className="w-8 h-8 text-gray-400" />
              </div>
            )}
            
            <div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
              />
              <Button
                type="button"
                variant="neu"
                onClick={() => fileInputRef.current?.click()}
              >
                {imagePreview ? 'Change Image' : 'Upload Image'}
              </Button>
              <p className="text-xs text-gray-500 mt-1">
                Recommended: 400x400px, max 2MB
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Management */}
      <Card variant="neu">
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Inventory Management
            </h3>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                {...register('trackInventory')}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">Track Inventory</span>
            </label>
          </div>

          {trackInventory && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                label="Current Stock"
                type="number"
                placeholder="0"
                {...register('currentStock', { 
                  min: { value: 0, message: 'Stock must be positive' }
                })}
                error={errors.currentStock?.message}
              />
              
              <Input
                label="Low Stock Alert"
                type="number"
                placeholder="5"
                {...register('lowStockAlert', { 
                  min: { value: 0, message: 'Alert level must be positive' }
                })}
                error={errors.lowStockAlert?.message}
              />
              
              <Input
                label="Unit"
                placeholder="pcs, kg, lbs, etc."
                {...register('unit')}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modifiers */}
      <Card variant="neu">
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Modifiers
            </h3>
            <Button
              type="button"
              variant="neu"
              size="sm"
              leftIcon={<Plus size={14} />}
              onClick={addModifier}
            >
              Add Modifier
            </Button>
          </div>

          {modifiers.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400 text-center py-4">
              No modifiers added. Modifiers allow customers to customize products.
            </p>
          ) : (
            <div className="space-y-4">
              {modifiers.map((modifier, modIndex) => (
                <div key={modifier.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <input
                      type="text"
                      placeholder="Modifier name (e.g., Size, Toppings)"
                      value={modifier.name}
                      onChange={(e) => updateModifier(modIndex, { name: e.target.value })}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg mr-3 dark:bg-dark-surface dark:border-gray-600 dark:text-gray-100"
                    />
                    <div className="flex items-center space-x-2">
                      <select
                        value={modifier.type}
                        onChange={(e) => updateModifier(modIndex, { type: e.target.value as 'single' | 'multiple' })}
                        className="px-3 py-2 border border-gray-300 rounded-lg dark:bg-dark-surface dark:border-gray-600 dark:text-gray-100"
                      >
                        <option value="single">Single Choice</option>
                        <option value="multiple">Multiple Choice</option>
                      </select>
                      <label className="flex items-center space-x-1">
                        <input
                          type="checkbox"
                          checked={modifier.required}
                          onChange={(e) => updateModifier(modIndex, { required: e.target.checked })}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="text-sm">Required</span>
                      </label>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeModifier(modIndex)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X size={14} />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {modifier.options.map((option, optIndex) => (
                      <div key={option.id} className="flex items-center space-x-2">
                        <input
                          type="text"
                          placeholder="Option name"
                          value={option.name}
                          onChange={(e) => updateModifierOption(modIndex, optIndex, { name: e.target.value })}
                          className="flex-1 px-3 py-1 border border-gray-300 rounded dark:bg-dark-surface dark:border-gray-600 dark:text-gray-100"
                        />
                        <input
                          type="number"
                          step="0.01"
                          placeholder="Price adjustment"
                          value={option.priceAdjustment}
                          onChange={(e) => updateModifierOption(modIndex, optIndex, { priceAdjustment: parseFloat(e.target.value) || 0 })}
                          className="w-24 px-3 py-1 border border-gray-300 rounded dark:bg-dark-surface dark:border-gray-600 dark:text-gray-100"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeModifierOption(modIndex, optIndex)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Minus size={14} />
                        </Button>
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      leftIcon={<Plus size={14} />}
                      onClick={() => addModifierOption(modIndex)}
                      className="text-primary-600 hover:text-primary-700"
                    >
                      Add Option
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="ghost"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          isLoading={isLoading}
        >
          {product ? 'Update Product' : 'Create Product'}
        </Button>
      </div>
    </form>
  );
};

export default ProductForm;
