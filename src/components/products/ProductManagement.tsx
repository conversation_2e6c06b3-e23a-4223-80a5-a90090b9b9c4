import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  Grid, 
  List, 
  AlertTriangle,
  Package,
  Edit,
  Trash2,
  Eye,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import { chickenProducts, chickenCategories } from '../../data/chickenRestaurantData';
import ProductCard from './ProductCard';
import ProductList from './ProductList';
import AddProductModal from './AddProductModal';
import EditProductModal from './EditProductModal';
import InventoryAdjustmentModal from './InventoryAdjustmentModal';
import LowStockAlert from './LowStockAlert';
import CategoryManager from './CategoryManager';
import InventoryReport from './InventoryReport';

interface ProductManagementProps {
  className?: string;
}

const ProductManagement: React.FC<ProductManagementProps> = ({ className = '' }) => {
  const [products, setProducts] = useState(chickenProducts);
  const [categories, setCategories] = useState(chickenCategories);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showInventoryModal, setShowInventoryModal] = useState(false);
  const [showCategoryManager, setShowCategoryManager] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'stock' | 'category'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Filter and search products
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || product.category.id === selectedCategory;
    
    return matchesSearch && matchesCategory && product.isActive;
  });

  // Sort products
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    let aValue, bValue;
    
    switch (sortBy) {
      case 'name':
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      case 'price':
        aValue = a.price;
        bValue = b.price;
        break;
      case 'stock':
        aValue = a.inventory.currentStock;
        bValue = b.inventory.currentStock;
        break;
      case 'category':
        aValue = a.category.name.toLowerCase();
        bValue = b.category.name.toLowerCase();
        break;
      default:
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
    }
    
    if (sortOrder === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  // Get low stock products
  const lowStockProducts = products.filter(product => 
    product.inventory.trackInventory && 
    product.inventory.currentStock <= product.inventory.lowStockAlert &&
    product.isActive
  );

  // Calculate inventory stats
  const inventoryStats = {
    totalProducts: products.filter(p => p.isActive).length,
    lowStockItems: lowStockProducts.length,
    outOfStockItems: products.filter(p => p.inventory.trackInventory && p.inventory.currentStock === 0 && p.isActive).length,
    totalValue: products.reduce((sum, p) => sum + (p.inventory.currentStock * p.cost), 0)
  };

  const handleEditProduct = (product: any) => {
    setSelectedProduct(product);
    setShowEditModal(true);
  };

  const handleInventoryAdjustment = (product: any) => {
    setSelectedProduct(product);
    setShowInventoryModal(true);
  };

  const handleDeleteProduct = (productId: string) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      setProducts(products.map(p => 
        p.id === productId ? { ...p, isActive: false } : p
      ));
    }
  };

  const handleSaveProduct = (productData: any) => {
    if (selectedProduct) {
      // Update existing product
      setProducts(products.map(p => 
        p.id === selectedProduct.id ? { ...p, ...productData, updatedAt: new Date() } : p
      ));
    } else {
      // Add new product
      const newProduct = {
        ...productData,
        id: `product-${Date.now()}`,
        businessId: 'crispy-crown',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setProducts([...products, newProduct]);
    }
    setShowAddModal(false);
    setShowEditModal(false);
    setSelectedProduct(null);
  };

  const handleInventoryUpdate = (productId: string, newStock: number, notes: string) => {
    setProducts(products.map(p => 
      p.id === productId 
        ? { 
            ...p, 
            inventory: { ...p.inventory, currentStock: newStock },
            updatedAt: new Date()
          } 
        : p
    ));
    setShowInventoryModal(false);
    setSelectedProduct(null);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Menu Items & Inventory</h1>
          <p className="text-gray-600">Manage your chicken restaurant menu and track inventory</p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="neu"
            onClick={() => setShowCategoryManager(true)}
            leftIcon={<Package size={16} />}
          >
            Categories
          </Button>
          <Button
            variant="primary"
            onClick={() => setShowAddModal(true)}
            leftIcon={<Plus size={16} />}
          >
            Add Menu Item
          </Button>
        </div>
      </div>

      {/* Inventory Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card variant="neu">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-blue-100 mr-4">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Items</p>
                <p className="text-2xl font-bold text-gray-900">{inventoryStats.totalProducts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="neu">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-yellow-100 mr-4">
                <AlertTriangle className="h-6 w-6 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Low Stock</p>
                <p className="text-2xl font-bold text-gray-900">{inventoryStats.lowStockItems}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="neu">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-red-100 mr-4">
                <TrendingDown className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Out of Stock</p>
                <p className="text-2xl font-bold text-gray-900">{inventoryStats.outOfStockItems}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="neu">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-green-100 mr-4">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Inventory Value</p>
                <p className="text-2xl font-bold text-gray-900">${inventoryStats.totalValue.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Low Stock Alert */}
      {lowStockProducts.length > 0 && (
        <LowStockAlert 
          products={lowStockProducts}
          onRestock={handleInventoryAdjustment}
        />
      )}

      {/* Filters and Search */}
      <Card variant="neu">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type="text"
                  placeholder="Search menu items..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 w-64"
                />
              </div>

              {/* Category Filter */}
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.icon} {category.name}
                  </option>
                ))}
              </select>

              {/* Sort */}
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  setSortBy(field as any);
                  setSortOrder(order as any);
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="name-asc">Name A-Z</option>
                <option value="name-desc">Name Z-A</option>
                <option value="price-asc">Price Low-High</option>
                <option value="price-desc">Price High-Low</option>
                <option value="stock-asc">Stock Low-High</option>
                <option value="stock-desc">Stock High-Low</option>
                <option value="category-asc">Category A-Z</option>
              </select>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'primary' : 'neu'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid size={16} />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'primary' : 'neu'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List size={16} />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Display */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {sortedProducts.map(product => (
            <ProductCard
              key={product.id}
              product={product}
              onEdit={handleEditProduct}
              onDelete={handleDeleteProduct}
              onInventoryAdjust={handleInventoryAdjustment}
            />
          ))}
        </div>
      ) : (
        <ProductList
          products={sortedProducts}
          onEdit={handleEditProduct}
          onDelete={handleDeleteProduct}
          onInventoryAdjust={handleInventoryAdjustment}
        />
      )}

      {/* Empty State */}
      {sortedProducts.length === 0 && (
        <Card variant="neu">
          <CardContent className="p-12 text-center">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No menu items found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || selectedCategory !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'Get started by adding your first menu item'
              }
            </p>
            {!searchTerm && selectedCategory === 'all' && (
              <Button variant="primary" onClick={() => setShowAddModal(true)}>
                Add Menu Item
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Modals */}
      {showAddModal && (
        <AddProductModal
          categories={categories}
          onSave={handleSaveProduct}
          onClose={() => setShowAddModal(false)}
        />
      )}

      {showEditModal && selectedProduct && (
        <EditProductModal
          product={selectedProduct}
          categories={categories}
          onSave={handleSaveProduct}
          onClose={() => {
            setShowEditModal(false);
            setSelectedProduct(null);
          }}
        />
      )}

      {showInventoryModal && selectedProduct && (
        <InventoryAdjustmentModal
          product={selectedProduct}
          onSave={handleInventoryUpdate}
          onClose={() => {
            setShowInventoryModal(false);
            setSelectedProduct(null);
          }}
        />
      )}

      {showCategoryManager && (
        <CategoryManager
          categories={categories}
          onSave={setCategories}
          onClose={() => setShowCategoryManager(false)}
        />
      )}
    </div>
  );
};

export default ProductManagement;
