import React, { useState } from 'react';
import { X, Plus, Minus, RefreshCw, Package, AlertTriangle } from 'lucide-react';
import Button from '../ui/Button';

interface InventoryAdjustmentModalProps {
  product: any;
  onSave: (productId: string, newStock: number, notes: string) => void;
  onClose: () => void;
}

const InventoryAdjustmentModal: React.FC<InventoryAdjustmentModalProps> = ({
  product,
  onSave,
  onClose
}) => {
  const [adjustmentType, setAdjustmentType] = useState<'add' | 'remove' | 'set'>('add');
  const [quantity, setQuantity] = useState('1');
  const [notes, setNotes] = useState('');
  
  const currentStock = product.inventory.currentStock || 0;
  const isLowStock = product.inventory.trackInventory && 
                    currentStock <= product.inventory.lowStockAlert;
  const isOutOfStock = product.inventory.trackInventory && currentStock === 0;

  const calculateNewStock = () => {
    const qtyNum = parseInt(quantity) || 0;
    
    switch (adjustmentType) {
      case 'add':
        return currentStock + qtyNum;
      case 'remove':
        return Math.max(0, currentStock - qtyNum);
      case 'set':
        return qtyNum;
      default:
        return currentStock;
    }
  };

  const newStock = calculateNewStock();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(product.id, newStock, notes);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Inventory Adjustment</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Product Info */}
          <div className="flex items-center space-x-4">
            <div className="h-16 w-16 bg-primary-100 rounded-lg flex items-center justify-center text-2xl">
              {product.category.icon || '🍗'}
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{product.name}</h3>
              <p className="text-sm text-gray-600">SKU: {product.sku || 'N/A'}</p>
            </div>
          </div>

          {/* Current Stock */}
          <div className="p-4 rounded-lg bg-gray-50 border border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Package className="h-5 w-5 text-gray-500 mr-2" />
                <span className="text-sm font-medium text-gray-700">Current Stock</span>
              </div>
              <div className="flex items-center">
                {isOutOfStock && <AlertTriangle className="h-4 w-4 text-red-500 mr-1" />}
                {isLowStock && !isOutOfStock && <AlertTriangle className="h-4 w-4 text-yellow-500 mr-1" />}
                <span className={`text-lg font-bold ${
                  isOutOfStock ? 'text-red-600' : 
                  isLowStock ? 'text-yellow-600' : 'text-green-600'
                }`}>
                  {currentStock} {product.inventory.unit}
                </span>
              </div>
            </div>
            {isLowStock && (
              <p className="text-xs text-yellow-600 mt-1">
                Low stock alert at {product.inventory.lowStockAlert} {product.inventory.unit}
              </p>
            )}
          </div>

          {/* Adjustment Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Adjustment Type
            </label>
            <div className="grid grid-cols-3 gap-2">
              <button
                type="button"
                className={`p-3 rounded-lg flex flex-col items-center justify-center ${
                  adjustmentType === 'add' 
                    ? 'bg-green-100 text-green-700 border-2 border-green-300' 
                    : 'bg-gray-100 text-gray-700 border border-gray-200'
                }`}
                onClick={() => setAdjustmentType('add')}
              >
                <Plus className="h-5 w-5 mb-1" />
                <span className="text-sm font-medium">Add</span>
              </button>
              <button
                type="button"
                className={`p-3 rounded-lg flex flex-col items-center justify-center ${
                  adjustmentType === 'remove' 
                    ? 'bg-red-100 text-red-700 border-2 border-red-300' 
                    : 'bg-gray-100 text-gray-700 border border-gray-200'
                }`}
                onClick={() => setAdjustmentType('remove')}
              >
                <Minus className="h-5 w-5 mb-1" />
                <span className="text-sm font-medium">Remove</span>
              </button>
              <button
                type="button"
                className={`p-3 rounded-lg flex flex-col items-center justify-center ${
                  adjustmentType === 'set' 
                    ? 'bg-blue-100 text-blue-700 border-2 border-blue-300' 
                    : 'bg-gray-100 text-gray-700 border border-gray-200'
                }`}
                onClick={() => setAdjustmentType('set')}
              >
                <RefreshCw className="h-5 w-5 mb-1" />
                <span className="text-sm font-medium">Set</span>
              </button>
            </div>
          </div>

          {/* Quantity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {adjustmentType === 'add' ? 'Add Quantity' : 
               adjustmentType === 'remove' ? 'Remove Quantity' : 
               'Set New Quantity'}
            </label>
            <input
              type="number"
              min={adjustmentType === 'set' ? '0' : '1'}
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          {/* New Stock Preview */}
          <div className="p-4 rounded-lg bg-primary-50 border border-primary-200">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-primary-700">New Stock Level</span>
              <span className="text-lg font-bold text-primary-700">
                {newStock} {product.inventory.unit}
              </span>
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes (optional)
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="e.g., Restocked from supplier, Damaged items, etc."
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <Button
              type="button"
              variant="neu"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
            >
              Save Adjustment
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default InventoryAdjustmentModal;
