import React, { useState } from 'react';

const BookingManager = () => {
  const [bookingDate, setBookingDate] = useState('');
  const [eventType, setEventType] = useState('');
  const [guestCount, setGuestCount] = useState('');
  
  const handleSubmit = (e) => {
    e.preventDefault();
    // In a real app, this would send the booking request to your backend
    alert(`Booking request submitted for ${eventType} on ${bookingDate} with ${guestCount} guests`);
  };
  
  return (
    <div className="feature-card">
      <h3>Book Your Event</h3>
      
      <form className="booking-form" onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="event-date">Event Date</label>
          <input 
            type="date" 
            id="event-date" 
            value={bookingDate}
            onChange={(e) => setBookingDate(e.target.value)}
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="event-type">Event Type</label>
          <select 
            id="event-type" 
            value={eventType}
            onChange={(e) => setEventType(e.target.value)}
            required
          >
            <option value="">Select Event Type</option>
            <option value="wedding">Wedding</option>
            <option value="corporate">Corporate Event</option>
            <option value="birthday">Birthday Party</option>
            <option value="other">Other</option>
          </select>
        </div>
        
        <div className="form-group">
          <label htmlFor="guest-count">Number of Guests</label>
          <input 
            type="number" 
            id="guest-count" 
            min="1"
            value={guestCount}
            onChange={(e) => setGuestCount(e.target.value)}
            required
          />
        </div>
        
        <button type="submit" className="submit-button">Request Booking</button>
      </form>
      
      <div className="upcoming-events">
        <h4>Your Upcoming Events</h4>
        <p className="no-events">No upcoming events. Book your first event now!</p>
      </div>
    </div>
  );
};

export default BookingManager;