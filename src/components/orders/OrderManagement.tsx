import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Search,
  Filter,
  Eye,
  Printer,
  MoreHorizontal
} from 'lucide-react';
import { usePOS } from '../../contexts/POSContext';
import { OrderService } from '../../services/orderService';
import { Order } from '../../types';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { Modal, ModalHeader, ModalBody, ModalFooter } from '../ui/Modal';

const OrderManagement: React.FC = () => {
  const { state } = usePOS();
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<Order['status'] | 'all'>('all');
  const [selectedType, setSelectedType] = useState<Order['type'] | 'all'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  useEffect(() => {
    loadOrders();
  }, [state.business?.id]);

  useEffect(() => {
    filterOrders();
  }, [orders, selectedStatus, selectedType, searchQuery]);

  const loadOrders = async () => {
    if (!state.business?.id) return;

    try {
      setIsLoading(true);
      const data = await OrderService.getOrders(state.business.id, 100);
      setOrders(data);
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterOrders = () => {
    let filtered = orders;

    // Filter by status
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(order => order.status === selectedStatus);
    }

    // Filter by type
    if (selectedType !== 'all') {
      filtered = filtered.filter(order => order.type === selectedType);
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(order =>
        order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.customer?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.customer?.email?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredOrders(filtered);
  };

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  };

  const handleVoidOrder = async (orderId: string) => {
    if (!confirm('Are you sure you want to void this order?')) return;

    try {
      await OrderService.voidOrder(orderId, 'Voided by staff');
      await loadOrders();
    } catch (error) {
      console.error('Error voiding order:', error);
    }
  };

  const handleRefundOrder = async (orderId: string) => {
    const order = orders.find(o => o.id === orderId);
    if (!order) return;

    const refundAmount = prompt(`Enter refund amount (max: $${order.total.toFixed(2)}):`);
    if (!refundAmount) return;

    const amount = parseFloat(refundAmount);
    if (isNaN(amount) || amount <= 0 || amount > order.total) {
      alert('Invalid refund amount');
      return;
    }

    const reason = prompt('Enter refund reason:');
    if (!reason) return;

    try {
      await OrderService.refundOrder(orderId, amount, reason);
      await loadOrders();
    } catch (error) {
      console.error('Error refunding order:', error);
    }
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'open':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'paid':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'voided':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'refunded':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getTypeIcon = (type: Order['type']) => {
    switch (type) {
      case 'in-person':
        return '🏪';
      case 'online':
        return '💻';
      case 'phone':
        return '📞';
      case 'qr':
        return '📱';
      default:
        return '📋';
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i} variant="neu" className="animate-pulse">
            <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Orders</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage and track all customer orders
          </p>
        </div>
        
        <Button
          variant="neu"
          leftIcon={<RefreshCw size={16} />}
          onClick={loadOrders}
        >
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card variant="neu">
        <CardContent className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="Search orders..."
              leftIcon={<Search size={16} />}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <div className="flex items-center space-x-4">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as Order['status'] | 'all')}
              className="px-3 py-2 border border-gray-300 rounded-lg bg-white dark:bg-dark-surface dark:border-gray-600 dark:text-gray-100"
            >
              <option value="all">All Status</option>
              <option value="open">Open</option>
              <option value="paid">Paid</option>
              <option value="voided">Voided</option>
              <option value="refunded">Refunded</option>
            </select>
            
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value as Order['type'] | 'all')}
              className="px-3 py-2 border border-gray-300 rounded-lg bg-white dark:bg-dark-surface dark:border-gray-600 dark:text-gray-100"
            >
              <option value="all">All Types</option>
              <option value="in-person">In-Person</option>
              <option value="online">Online</option>
              <option value="phone">Phone</option>
              <option value="qr">QR Order</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Orders List */}
      {filteredOrders.length === 0 ? (
        <Card variant="neu" className="text-center py-12">
          <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            No orders found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {searchQuery || selectedStatus !== 'all' || selectedType !== 'all'
              ? 'Try adjusting your search or filter criteria'
              : 'Orders will appear here once customers start placing them'
            }
          </p>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredOrders.map(order => (
            <Card key={order.id} variant="neu" className="hover:shadow-neu-pressed transition-shadow">
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="text-2xl">{getTypeIcon(order.type)}</div>
                    
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                          {order.orderNumber}
                        </h3>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(order.status)}`}>
                          {order.status.toUpperCase()}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                        <span>{formatDate(order.createdAt)}</span>
                        {order.customer && (
                          <span>• {order.customer.name}</span>
                        )}
                        <span>• {order.items.length} items</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-lg font-bold text-gray-900 dark:text-gray-100">
                        ${order.total.toFixed(2)}
                      </p>
                      {order.payments.length > 0 && (
                        <p className="text-sm text-gray-500">
                          ${order.payments.reduce((sum, p) => sum + p.amount, 0).toFixed(2)} paid
                        </p>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="neu"
                        size="sm"
                        onClick={() => handleViewOrder(order)}
                      >
                        <Eye size={14} />
                      </Button>
                      
                      {order.status === 'open' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleVoidOrder(order.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                        >
                          <XCircle size={14} />
                        </Button>
                      )}
                      
                      {order.status === 'paid' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRefundOrder(order.id)}
                          className="text-purple-600 hover:text-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20"
                        >
                          <RefreshCw size={14} />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Order Details Modal */}
      <Modal
        isOpen={showOrderDetails}
        onClose={() => setShowOrderDetails(false)}
        title={`Order ${selectedOrder?.orderNumber}`}
        size="lg"
      >
        {selectedOrder && (
          <div className="space-y-6">
            {/* Order Info */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Order Details</h4>
                <div className="space-y-1 text-sm">
                  <p><span className="font-medium">Type:</span> {getTypeIcon(selectedOrder.type)} {selectedOrder.type}</p>
                  <p><span className="font-medium">Status:</span> 
                    <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(selectedOrder.status)}`}>
                      {selectedOrder.status.toUpperCase()}
                    </span>
                  </p>
                  <p><span className="font-medium">Created:</span> {formatDate(selectedOrder.createdAt)}</p>
                </div>
              </div>
              
              {selectedOrder.customer && (
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Customer</h4>
                  <div className="space-y-1 text-sm">
                    <p><span className="font-medium">Name:</span> {selectedOrder.customer.name}</p>
                    {selectedOrder.customer.email && (
                      <p><span className="font-medium">Email:</span> {selectedOrder.customer.email}</p>
                    )}
                    {selectedOrder.customer.phone && (
                      <p><span className="font-medium">Phone:</span> {selectedOrder.customer.phone}</p>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Order Items */}
            <div>
              <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">Items</h4>
              <div className="space-y-2">
                {selectedOrder.items.map((item, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                      <p className="font-medium">{item.productName}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        ${item.unitPrice.toFixed(2)} × {item.quantity}
                      </p>
                      {item.selectedModifiers.length > 0 && (
                        <div className="text-xs text-gray-500 mt-1">
                          {item.selectedModifiers.map(mod => mod.optionName).join(', ')}
                        </div>
                      )}
                    </div>
                    <p className="font-medium">${item.subtotal.toFixed(2)}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Totals */}
            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>${selectedOrder.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax:</span>
                  <span>${selectedOrder.tax.toFixed(2)}</span>
                </div>
                {selectedOrder.tip > 0 && (
                  <div className="flex justify-between">
                    <span>Tip:</span>
                    <span>${selectedOrder.tip.toFixed(2)}</span>
                  </div>
                )}
                {selectedOrder.discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount:</span>
                    <span>-${selectedOrder.discount.toFixed(2)}</span>
                  </div>
                )}
                <hr className="border-gray-200 dark:border-gray-700" />
                <div className="flex justify-between font-bold text-lg">
                  <span>Total:</span>
                  <span>${selectedOrder.total.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {/* Payments */}
            {selectedOrder.payments.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">Payments</h4>
                <div className="space-y-2">
                  {selectedOrder.payments.map((payment, index) => (
                    <div key={index} className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div>
                        <p className="font-medium">{payment.method.name}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {payment.processedAt && formatDate(payment.processedAt)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">${payment.amount.toFixed(2)}</p>
                        <p className={`text-xs ${
                          payment.status === 'completed' 
                            ? 'text-green-600' 
                            : payment.status === 'failed' 
                            ? 'text-red-600' 
                            : 'text-yellow-600'
                        }`}>
                          {payment.status.toUpperCase()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <Button
                variant="ghost"
                onClick={() => setShowOrderDetails(false)}
              >
                Close
              </Button>
              <Button
                variant="neu"
                leftIcon={<Printer size={16} />}
              >
                Print Receipt
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OrderManagement;
