import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Eye, EyeOff, Store, Mail, Lock, User, Phone } from 'lucide-react';
import { AuthService } from '../../services/authService';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import ThemeToggle from '../ui/ThemeToggle';

interface SignInForm {
  email: string;
  password: string;
}

interface SignUpForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  businessName: string;
  businessPhone: string;
}

const AuthPage: React.FC = () => {
  const [isSignUp, setIsSignUp] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const signInForm = useForm<SignInForm>();
  const signUpForm = useForm<SignUpForm>();

  const handleSignIn = async (data: SignInForm) => {
    setIsLoading(true);
    setError(null);
    
    try {
      await AuthService.signIn(data.email, data.password);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignUp = async (data: SignUpForm) => {
    if (data.password !== data.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      await AuthService.signUp(data.email, data.password, data.name, {
        name: data.businessName,
        phone: data.businessPhone
      });
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 dark:from-dark-bg dark:to-dark-surface flex items-center justify-center p-4">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="relative w-full max-w-md">
        {/* Theme toggle */}
        <div className="absolute top-0 right-0 -mt-12">
          <ThemeToggle />
        </div>

        <Card variant="neu" className="overflow-hidden">
          <CardHeader className="text-center pb-6">
            <div className="mx-auto w-16 h-16 bg-primary-600 rounded-2xl flex items-center justify-center mb-4 shadow-lg">
              <Store className="w-8 h-8 text-white" />
            </div>
            <CardTitle className="text-2xl">
              {isSignUp ? 'Create Your POS Account' : 'Welcome to POSess'}
            </CardTitle>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              {isSignUp 
                ? 'Set up your business and start selling today' 
                : 'Sign in to your point of sale system'
              }
            </p>
          </CardHeader>

          <CardContent>
            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-300 text-red-700 rounded-lg text-sm dark:bg-red-900/20 dark:border-red-800 dark:text-red-400">
                {error}
              </div>
            )}

            {isSignUp ? (
              <form onSubmit={signUpForm.handleSubmit(handleSignUp)} className="space-y-4">
                <Input
                  label="Full Name"
                  placeholder="Enter your full name"
                  leftIcon={<User size={16} />}
                  {...signUpForm.register('name', { required: 'Name is required' })}
                  error={signUpForm.formState.errors.name?.message}
                />

                <Input
                  label="Email Address"
                  type="email"
                  placeholder="Enter your email"
                  leftIcon={<Mail size={16} />}
                  {...signUpForm.register('email', { 
                    required: 'Email is required',
                    pattern: {
                      value: /^\S+@\S+$/i,
                      message: 'Invalid email address'
                    }
                  })}
                  error={signUpForm.formState.errors.email?.message}
                />

                <Input
                  label="Business Name"
                  placeholder="Enter your business name"
                  leftIcon={<Store size={16} />}
                  {...signUpForm.register('businessName', { required: 'Business name is required' })}
                  error={signUpForm.formState.errors.businessName?.message}
                />

                <Input
                  label="Business Phone"
                  placeholder="Enter business phone number"
                  leftIcon={<Phone size={16} />}
                  {...signUpForm.register('businessPhone')}
                />

                <Input
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Create a password"
                  leftIcon={<Lock size={16} />}
                  rightIcon={
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  }
                  {...signUpForm.register('password', { 
                    required: 'Password is required',
                    minLength: {
                      value: 6,
                      message: 'Password must be at least 6 characters'
                    }
                  })}
                  error={signUpForm.formState.errors.password?.message}
                />

                <Input
                  label="Confirm Password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Confirm your password"
                  leftIcon={<Lock size={16} />}
                  {...signUpForm.register('confirmPassword', { required: 'Please confirm your password' })}
                  error={signUpForm.formState.errors.confirmPassword?.message}
                />

                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  className="w-full"
                  isLoading={isLoading}
                >
                  Create Account
                </Button>
              </form>
            ) : (
              <form onSubmit={signInForm.handleSubmit(handleSignIn)} className="space-y-4">
                <Input
                  label="Email Address"
                  type="email"
                  placeholder="Enter your email"
                  leftIcon={<Mail size={16} />}
                  {...signInForm.register('email', { required: 'Email is required' })}
                  error={signInForm.formState.errors.email?.message}
                />

                <Input
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Enter your password"
                  leftIcon={<Lock size={16} />}
                  rightIcon={
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  }
                  {...signInForm.register('password', { required: 'Password is required' })}
                  error={signInForm.formState.errors.password?.message}
                />

                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  className="w-full"
                  isLoading={isLoading}
                >
                  Sign In
                </Button>
              </form>
            )}

            <div className="mt-6 text-center">
              <button
                type="button"
                onClick={() => {
                  setIsSignUp(!isSignUp);
                  setError(null);
                  signInForm.reset();
                  signUpForm.reset();
                }}
                className="text-primary-600 hover:text-primary-700 text-sm font-medium dark:text-primary-400"
              >
                {isSignUp 
                  ? 'Already have an account? Sign in' 
                  : "Don't have an account? Sign up"
                }
              </button>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-6 text-sm text-gray-500 dark:text-gray-400">
          <p>© 2024 POSess. A modern alternative to traditional POS systems.</p>
        </div>
      </div>
    </div>
  );
};

export default AuthPage;
