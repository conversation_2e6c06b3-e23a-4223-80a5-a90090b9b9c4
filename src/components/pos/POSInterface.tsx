import React, { useState, useEffect } from 'react';
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  X, 
  Calculator,
  CreditCard,
  DollarSign,
  Percent,
  User
} from 'lucide-react';
import { usePOS } from '../../contexts/POSContext';
import { ProductService } from '../../services/productService';
import { OrderService } from '../../services/orderService';
import { Product, Category, CartItem, Order, Customer } from '../../types';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { Modal, ModalHeader, ModalBody, ModalFooter } from '../ui/Modal';

const POSInterface: React.FC = () => {
  const { state, addToCart, removeFromCart, updateCartItem, clearCart, getCartTotal, getCartItemCount } = usePOS();
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCheckout, setShowCheckout] = useState(false);
  const [taxRate, setTaxRate] = useState(0.08);
  const [tipAmount, setTipAmount] = useState(0);
  const [discountAmount, setDiscountAmount] = useState(0);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  useEffect(() => {
    loadData();
  }, [state.business?.id]);

  useEffect(() => {
    filterProducts();
  }, [products, selectedCategory, searchQuery]);

  const loadData = async () => {
    if (!state.business?.id) return;

    try {
      setIsLoading(true);
      const [productsData, categoriesData] = await Promise.all([
        ProductService.getProducts(state.business.id),
        ProductService.getCategories(state.business.id)
      ]);
      
      setProducts(productsData);
      setCategories(categoriesData);
      setTaxRate(state.business.taxRate || 0.08);
    } catch (error) {
      console.error('Error loading POS data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterProducts = () => {
    let filtered = products;

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category.id === selectedCategory);
    }

    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredProducts(filtered);
  };

  const handleAddToCart = (product: Product) => {
    const cartItem: CartItem = {
      id: `${product.id}_${Date.now()}`,
      productId: product.id,
      productName: product.name,
      quantity: 1,
      unitPrice: product.price,
      selectedModifiers: [],
      subtotal: product.price,
      product: product
    };

    addToCart(cartItem);
  };

  const handleUpdateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId);
    } else {
      const item = state.cart.find(item => item.id === itemId);
      if (item) {
        const newSubtotal = item.unitPrice * newQuantity;
        updateCartItem(itemId, { quantity: newQuantity, subtotal: newSubtotal });
      }
    }
  };

  const calculateTotals = () => {
    const subtotal = getCartTotal();
    const tax = subtotal * taxRate;
    const total = subtotal + tax + tipAmount - discountAmount;
    
    return { subtotal, tax, total };
  };

  const handleCheckout = async () => {
    if (!state.business?.id || !state.user?.id || state.cart.length === 0) return;

    try {
      const { subtotal, tax, total } = calculateTotals();
      
      const orderData: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'> = {
        businessId: state.business.id,
        type: 'in-person',
        status: 'open',
        items: state.cart.map(item => ({
          id: item.id,
          productId: item.productId,
          productName: item.productName,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          selectedModifiers: item.selectedModifiers,
          subtotal: item.subtotal
        })),
        customer: selectedCustomer,
        subtotal,
        tax,
        tip: tipAmount,
        discount: discountAmount,
        total,
        payments: [],
        cashierId: state.user.id
      };

      const orderId = await OrderService.createOrder(orderData);
      console.log('Order created:', orderId);
      
      // Clear cart and close checkout
      clearCart();
      setShowCheckout(false);
      setTipAmount(0);
      setDiscountAmount(0);
      setSelectedCustomer(null);
      
    } catch (error) {
      console.error('Error creating order:', error);
    }
  };

  const { subtotal, tax, total } = calculateTotals();

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-screen">
        <div className="lg:col-span-2">
          <Card variant="neu" className="h-full animate-pulse">
            <div className="h-full bg-gray-200 dark:bg-gray-700 rounded"></div>
          </Card>
        </div>
        <div>
          <Card variant="neu" className="h-full animate-pulse">
            <div className="h-full bg-gray-200 dark:bg-gray-700 rounded"></div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-screen">
      {/* Products Section */}
      <div className="lg:col-span-2 space-y-4">
        {/* Search and Categories */}
        <Card variant="neu">
          <CardContent className="space-y-4">
            <Input
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === 'all' ? 'primary' : 'neu'}
                size="sm"
                onClick={() => setSelectedCategory('all')}
              >
                All
              </Button>
              {categories.map(category => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? 'primary' : 'neu'}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className={selectedCategory === category.id ? '' : category.color}
                >
                  {category.icon && <span className="mr-1">{category.icon}</span>}
                  {category.name}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Products Grid */}
        <Card variant="neu" className="flex-1 overflow-hidden">
          <CardContent className="h-full overflow-y-auto">
            {filteredProducts.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <ShoppingCart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">No products found</p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {filteredProducts.map(product => (
                  <Card
                    key={product.id}
                    variant="neu"
                    padding="none"
                    className="cursor-pointer hover:shadow-neu-pressed transition-shadow"
                    onClick={() => handleAddToCart(product)}
                  >
                    <div className="aspect-square bg-gray-100 dark:bg-gray-800 rounded-t-lg">
                      {product.image ? (
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-full h-full object-cover rounded-t-lg"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <ShoppingCart className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div className="p-3">
                      <h3 className="font-medium text-sm text-gray-900 dark:text-gray-100 truncate">
                        {product.name}
                      </h3>
                      <p className="text-lg font-bold text-primary-600 dark:text-primary-400">
                        ${product.price.toFixed(2)}
                      </p>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Cart Section */}
      <div className="space-y-4">
        <Card variant="neu">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <ShoppingCart className="mr-2 h-5 w-5" />
                Cart ({getCartItemCount()})
              </span>
              {state.cart.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearCart}
                  className="text-red-600 hover:text-red-700"
                >
                  Clear
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-3 max-h-96 overflow-y-auto">
            {state.cart.length === 0 ? (
              <div className="text-center py-8">
                <ShoppingCart className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                <p className="text-gray-500 dark:text-gray-400 text-sm">Cart is empty</p>
              </div>
            ) : (
              state.cart.map(item => (
                <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm text-gray-900 dark:text-gray-100 truncate">
                      {item.productName}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      ${item.unitPrice.toFixed(2)} each
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="neu"
                      size="sm"
                      onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                    >
                      <Minus size={12} />
                    </Button>
                    <span className="w-8 text-center text-sm font-medium">
                      {item.quantity}
                    </span>
                    <Button
                      variant="neu"
                      size="sm"
                      onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                    >
                      <Plus size={12} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFromCart(item.id)}
                      className="text-red-600 hover:text-red-700 ml-2"
                    >
                      <X size={12} />
                    </Button>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>

        {/* Order Summary */}
        {state.cart.length > 0 && (
          <Card variant="neu">
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>${subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Tax ({(taxRate * 100).toFixed(1)}%):</span>
                <span>${tax.toFixed(2)}</span>
              </div>
              {tipAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span>Tip:</span>
                  <span>${tipAmount.toFixed(2)}</span>
                </div>
              )}
              {discountAmount > 0 && (
                <div className="flex justify-between text-sm text-green-600">
                  <span>Discount:</span>
                  <span>-${discountAmount.toFixed(2)}</span>
                </div>
              )}
              <hr className="border-gray-200 dark:border-gray-700" />
              <div className="flex justify-between font-bold text-lg">
                <span>Total:</span>
                <span>${total.toFixed(2)}</span>
              </div>
              
              <Button
                variant="primary"
                size="lg"
                className="w-full"
                onClick={() => setShowCheckout(true)}
                leftIcon={<CreditCard size={16} />}
              >
                Checkout
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Checkout Modal */}
      <Modal
        isOpen={showCheckout}
        onClose={() => setShowCheckout(false)}
        title="Checkout"
        size="lg"
      >
        <div className="space-y-6">
          {/* Order Summary */}
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="font-semibold mb-3">Order Summary</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>${subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax:</span>
                <span>${tax.toFixed(2)}</span>
              </div>
              <div className="flex justify-between font-bold text-lg border-t pt-2">
                <span>Total:</span>
                <span>${total.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* Tip and Discount */}
          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Tip Amount"
              type="number"
              step="0.01"
              value={tipAmount}
              onChange={(e) => setTipAmount(parseFloat(e.target.value) || 0)}
              leftIcon={<DollarSign size={16} />}
            />
            <Input
              label="Discount Amount"
              type="number"
              step="0.01"
              value={discountAmount}
              onChange={(e) => setDiscountAmount(parseFloat(e.target.value) || 0)}
              leftIcon={<Percent size={16} />}
            />
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="ghost"
              onClick={() => setShowCheckout(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleCheckout}
              leftIcon={<CreditCard size={16} />}
            >
              Process Payment
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default POSInterface;
