import React from 'react';

const PaymentGuide = () => {
  return (
    <div className="feature-card">
      <h3>Payment Options</h3>
      
      <div className="payment-options">
        <div className="payment-option">
          <div className="payment-icon">💸</div>
          <div className="payment-details">
            <h4>CashApp</h4>
            <p>Send payment to: $YourCashAppHandle</p>
            <button className="copy-button">Copy Handle</button>
          </div>
        </div>
        
        <div className="payment-option">
          <div className="payment-icon">💳</div>
          <div className="payment-details">
            <h4>Zelle</h4>
            <p>Send payment to: <EMAIL></p>
            <button className="copy-button">Copy Email</button>
          </div>
        </div>
        
        <div className="payment-info">
          <h4>Payment Process</h4>
          <ol>
            <li>Select your package or custom order</li>
            <li>Receive a quote from our AI assistant</li>
            <li>Send 50% deposit to secure your booking</li>
            <li>Remaining balance due 48 hours before event</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default PaymentGuide;