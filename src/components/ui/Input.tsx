import React from 'react';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: 'default' | 'neu' | 'filled';
  error?: string;
  label?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className, 
    variant = 'default', 
    error,
    label,
    leftIcon,
    rightIcon,
    type = 'text',
    ...props 
  }, ref) => {
    const baseStyles = clsx(
      'w-full px-3 py-2 text-sm transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-1',
      'disabled:opacity-50 disabled:cursor-not-allowed'
    );

    const variants = {
      default: clsx(
        'border border-gray-300 rounded-lg bg-white',
        'focus:border-primary-500 focus:ring-primary-500',
        'dark:bg-dark-surface dark:border-gray-600 dark:text-gray-100',
        'dark:focus:border-primary-400'
      ),
      neu: clsx(
        'border-0 rounded-lg bg-neu-base shadow-neu-inset',
        'focus:shadow-neu-pressed focus:ring-primary-500',
        'dark:bg-dark-surface dark:shadow-neu-dark-inset',
        'dark:focus:shadow-neu-dark-pressed dark:text-gray-100'
      ),
      filled: clsx(
        'border-0 rounded-lg bg-gray-100',
        'focus:bg-white focus:ring-primary-500',
        'dark:bg-gray-800 dark:focus:bg-dark-surface dark:text-gray-100'
      )
    };

    const errorStyles = error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '';

    const inputElement = (
      <input
        type={type}
        className={twMerge(
          baseStyles,
          variants[variant],
          errorStyles,
          leftIcon && 'pl-10',
          rightIcon && 'pr-10',
          className
        )}
        ref={ref}
        {...props}
      />
    );

    if (!label && !leftIcon && !rightIcon && !error) {
      return inputElement;
    }

    return (
      <div className="w-full">
        {label && (
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-400 dark:text-gray-500">
                {leftIcon}
              </span>
            </div>
          )}
          
          {inputElement}
          
          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <span className="text-gray-400 dark:text-gray-500">
                {rightIcon}
              </span>
            </div>
          )}
        </div>
        
        {error && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {error}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
