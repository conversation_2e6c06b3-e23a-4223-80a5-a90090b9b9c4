import React from 'react';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'ghost' | 'neu';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    className, 
    variant = 'primary', 
    size = 'md', 
    isLoading = false,
    leftIcon,
    rightIcon,
    children, 
    disabled,
    ...props 
  }, ref) => {
    const baseStyles = clsx(
      'inline-flex items-center justify-center font-medium transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'transform active:scale-95'
    );

    const variants = {
      primary: clsx(
        'bg-primary-600 text-white shadow-lg',
        'hover:bg-primary-700 hover:shadow-xl hover:-translate-y-0.5',
        'focus:ring-primary-500',
        'dark:bg-primary-500 dark:hover:bg-primary-600'
      ),
      secondary: clsx(
        'bg-gray-200 text-gray-900 shadow-md',
        'hover:bg-gray-300 hover:shadow-lg hover:-translate-y-0.5',
        'focus:ring-gray-500',
        'dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600'
      ),
      success: clsx(
        'bg-green-600 text-white shadow-lg',
        'hover:bg-green-700 hover:shadow-xl hover:-translate-y-0.5',
        'focus:ring-green-500'
      ),
      danger: clsx(
        'bg-red-600 text-white shadow-lg',
        'hover:bg-red-700 hover:shadow-xl hover:-translate-y-0.5',
        'focus:ring-red-500'
      ),
      ghost: clsx(
        'bg-transparent text-gray-700 border border-gray-300',
        'hover:bg-gray-50 hover:border-gray-400',
        'focus:ring-gray-500',
        'dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-800'
      ),
      neu: clsx(
        'bg-neu-base text-gray-700 shadow-neu-outset',
        'hover:shadow-neu-pressed active:shadow-neu-inset',
        'focus:ring-primary-500',
        'dark:bg-dark-surface dark:text-gray-300',
        'dark:shadow-neu-dark-outset dark:hover:shadow-neu-dark-pressed',
        'dark:active:shadow-neu-dark-inset'
      )
    };

    const sizes = {
      sm: 'px-3 py-1.5 text-sm rounded-md',
      md: 'px-4 py-2 text-sm rounded-lg',
      lg: 'px-6 py-3 text-base rounded-lg',
      xl: 'px-8 py-4 text-lg rounded-xl'
    };

    return (
      <button
        className={twMerge(
          baseStyles,
          variants[variant],
          sizes[size],
          className
        )}
        ref={ref}
        disabled={disabled || isLoading}
        {...props}
      >
        {isLoading && (
          <svg 
            className="animate-spin -ml-1 mr-2 h-4 w-4" 
            xmlns="http://www.w3.org/2000/svg" 
            fill="none" 
            viewBox="0 0 24 24"
          >
            <circle 
              className="opacity-25" 
              cx="12" 
              cy="12" 
              r="10" 
              stroke="currentColor" 
              strokeWidth="4"
            />
            <path 
              className="opacity-75" 
              fill="currentColor" 
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        
        {!isLoading && leftIcon && (
          <span className="mr-2">{leftIcon}</span>
        )}
        
        {children}
        
        {!isLoading && rightIcon && (
          <span className="ml-2">{rightIcon}</span>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
