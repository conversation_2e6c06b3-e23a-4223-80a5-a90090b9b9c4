import React, { useState } from 'react';

// Mock data - would be fetched from Instagram API in production
const mockCocktails = [
  {
    id: 1,
    name: "Classic Mojito",
    description: "Fresh mint, lime, sugar, rum, and soda water",
    image: "https://images.unsplash.com/photo-1551024709-8f23befc6f87?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    price: "$12"
  },
  {
    id: 2,
    name: "Espresso Martini",
    description: "Vodka, coffee liqueur, and freshly brewed espresso",
    image: "https://images.unsplash.com/photo-1595968822901-2b5b5c0b4b42?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    price: "$14"
  },
  {
    id: 3,
    name: "Signature Old Fashioned",
    description: "Bourbon, bitters, sugar, and orange peel",
    image: "https://images.unsplash.com/photo-1470337458703-46ad1756a187?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    price: "$15"
  }
];

const mockPackages = [
  {
    id: 1,
    name: "Essential Package",
    description: "3 signature cocktails for up to 50 guests",
    price: "$500"
  },
  {
    id: 2,
    name: "Premium Package",
    description: "5 signature cocktails for up to 100 guests",
    price: "$900"
  }
];

const MenuDisplay = () => {
  const [activeTab, setActiveTab] = useState('cocktails');
  
  return (
    <div className="feature-card">
      <h3>Menu</h3>
      
      <div className="menu-tabs">
        <button 
          className={activeTab === 'cocktails' ? 'active' : ''} 
          onClick={() => setActiveTab('cocktails')}
        >
          Signature Cocktails
        </button>
        <button 
          className={activeTab === 'packages' ? 'active' : ''} 
          onClick={() => setActiveTab('packages')}
        >
          Packages
        </button>
      </div>
      
      <div className="menu-items">
        {activeTab === 'cocktails' ? (
          mockCocktails.map(cocktail => (
            <div key={cocktail.id} className="menu-item">
              <img 
                src={cocktail.image} 
                alt={cocktail.name} 
                className="menu-item-image" 
              />
              <div className="menu-item-details">
                <h4>{cocktail.name} <span className="price">{cocktail.price}</span></h4>
                <p>{cocktail.description}</p>
              </div>
            </div>
          ))
        ) : (
          mockPackages.map(pkg => (
            <div key={pkg.id} className="menu-item package">
              <div className="package-icon">📦</div>
              <div className="menu-item-details">
                <h4>{pkg.name} <span className="price">{pkg.price}</span></h4>
                <p>{pkg.description}</p>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default MenuDisplay;