import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  DollarSign, 
  ShoppingCart, 
  Users, 
  TrendingUp,
  TrendingDown,
  Package,
  Calendar,
  Download,
  RefreshCw,
  Clock,
  CreditCard
} from 'lucide-react';
import { usePOS } from '../../contexts/POSContext';
import { AnalyticsService } from '../../services/analyticsService';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Input from '../ui/Input';

const AnalyticsDashboard: React.FC = () => {
  const { state } = usePOS();
  const [dateRange, setDateRange] = useState<'today' | 'week' | 'month' | 'year'>('today');
  const [isLoading, setIsLoading] = useState(true);
  const [salesData, setSalesData] = useState<any>(null);
  const [inventoryData, setInventoryData] = useState<any>(null);
  const [customerData, setCustomerData] = useState<any>(null);
  const [weeklyComparison, setWeeklyComparison] = useState<any>(null);

  useEffect(() => {
    loadAnalyticsData();
  }, [state.business?.id, dateRange]);

  const loadAnalyticsData = async () => {
    if (!state.business?.id) return;

    setIsLoading(true);
    try {
      // Get date range
      const endDate = new Date();
      let startDate = new Date();
      
      switch (dateRange) {
        case 'week':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case 'year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default: // today
          startDate.setHours(0, 0, 0, 0);
          break;
      }

      // Load all analytics data in parallel
      const [sales, inventory, customers, weekly] = await Promise.all([
        AnalyticsService.getSalesAnalytics(state.business.id, startDate, endDate),
        AnalyticsService.getInventoryInsights(state.business.id),
        AnalyticsService.getCustomerInsights(state.business.id),
        AnalyticsService.getWeeklyComparison(state.business.id)
      ]);

      setSalesData(sales);
      setInventoryData(inventory);
      setCustomerData(customers);
      setWeeklyComparison(weekly);
    } catch (error) {
      console.error('Error loading analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportData = () => {
    if (!salesData) return;

    const exportData = [
      {
        date: new Date().toLocaleDateString(),
        period: dateRange,
        totalSales: salesData.totalSales,
        totalOrders: salesData.totalOrders,
        averageOrderValue: salesData.averageOrderValue
      }
    ];

    AnalyticsService.exportToCSV(exportData, `sales_report_${dateRange}_${new Date().toISOString().split('T')[0]}.csv`);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} variant="neu" className="animate-pulse">
              <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Analytics</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Business insights and performance metrics
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2 bg-white dark:bg-dark-surface rounded-lg shadow-sm">
            {(['today', 'week', 'month', 'year'] as const).map((range) => (
              <button
                key={range}
                onClick={() => setDateRange(range)}
                className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                  dateRange === range 
                    ? 'bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:text-primary-400' 
                    : 'text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800'
                }`}
              >
                {range.charAt(0).toUpperCase() + range.slice(1)}
              </button>
            ))}
          </div>
          
          <Button
            variant="neu"
            leftIcon={<RefreshCw size={16} />}
            onClick={loadAnalyticsData}
          >
            Refresh
          </Button>
          
          <Button
            variant="neu"
            leftIcon={<Download size={16} />}
            onClick={handleExportData}
          >
            Export
          </Button>
        </div>
      </div>

      {/* Sales Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card variant="neu">
          <CardContent className="flex items-center p-6">
            <div className="p-3 rounded-lg bg-green-100 dark:bg-green-900/20 mr-4">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Sales
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                ${salesData?.totalSales.toFixed(2) || '0.00'}
              </p>
              {weeklyComparison && (
                <div className={`flex items-center text-xs mt-1 ${
                  weeklyComparison.changePercent >= 0 
                    ? 'text-green-600' 
                    : 'text-red-600'
                }`}>
                  {weeklyComparison.changePercent >= 0 ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1" />
                  )}
                  <span>
                    {Math.abs(weeklyComparison.changePercent).toFixed(1)}% from last week
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card variant="neu">
          <CardContent className="flex items-center p-6">
            <div className="p-3 rounded-lg bg-blue-100 dark:bg-blue-900/20 mr-4">
              <ShoppingCart className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Orders
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {salesData?.totalOrders || '0'}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Avg. ${salesData?.averageOrderValue.toFixed(2) || '0.00'} per order
              </p>
            </div>
          </CardContent>
        </Card>

        <Card variant="neu">
          <CardContent className="flex items-center p-6">
            <div className="p-3 rounded-lg bg-purple-100 dark:bg-purple-900/20 mr-4">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Customers
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {customerData?.totalCustomers || '0'}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {customerData?.newCustomersThisMonth || '0'} new this month
              </p>
            </div>
          </CardContent>
        </Card>

        <Card variant="neu">
          <CardContent className="flex items-center p-6">
            <div className="p-3 rounded-lg bg-orange-100 dark:bg-orange-900/20 mr-4">
              <Package className="h-6 w-6 text-orange-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Inventory
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {inventoryData?.totalProducts || '0'}
              </p>
              <p className="text-xs text-red-500 mt-1">
                {inventoryData?.lowStockItems || '0'} low stock items
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sales Trends */}
      <Card variant="neu">
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="mr-2 h-5 w-5" />
            Sales Trends
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            {salesData?.salesByHour ? (
              <div className="w-full h-full">
                <div className="flex h-full items-end space-x-2">
                  {salesData.salesByHour.map((hour: any) => {
                    const height = salesData.totalSales > 0 
                      ? (hour.sales / Math.max(...salesData.salesByHour.map((h: any) => h.sales))) * 100 
                      : 0;
                    
                    return (
                      <div key={hour.hour} className="flex-1 flex flex-col items-center">
                        <div 
                          className="w-full bg-primary-500 dark:bg-primary-600 rounded-t-sm transition-all duration-500"
                          style={{ height: `${Math.max(height, 5)}%` }}
                        ></div>
                        <div className="text-xs text-gray-500 mt-1">
                          {hour.hour % 12 || 12}{hour.hour >= 12 ? 'pm' : 'am'}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400">No sales data available</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Top Products & Payment Methods */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card variant="neu">
          <CardHeader>
            <CardTitle>Top Products</CardTitle>
          </CardHeader>
          
          <CardContent>
            {salesData?.topProducts?.length > 0 ? (
              <div className="space-y-3">
                {salesData.topProducts.slice(0, 5).map((product: any, index: number) => (
                  <div key={product.productId} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-full">
                        <span className="text-xs font-medium">{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">
                          {product.productName}
                        </p>
                        <p className="text-xs text-gray-500">
                          {product.quantitySold} sold
                        </p>
                      </div>
                    </div>
                    <p className="font-medium text-gray-900 dark:text-gray-100">
                      ${product.revenue.toFixed(2)}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center text-gray-500 dark:text-gray-400 py-6">
                No product data available
              </p>
            )}
          </CardContent>
        </Card>

        <Card variant="neu">
          <CardHeader>
            <CardTitle>Payment Methods</CardTitle>
          </CardHeader>
          
          <CardContent>
            {salesData?.paymentMethodBreakdown?.length > 0 ? (
              <div className="space-y-3">
                {salesData.paymentMethodBreakdown.map((payment: any) => (
                  <div key={payment.method} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {payment.method}
                      </p>
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        ${payment.amount.toFixed(2)}
                      </p>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-primary-500 dark:bg-primary-600 h-2 rounded-full"
                        style={{ width: `${payment.percentage}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500 text-right">
                      {payment.percentage.toFixed(1)}%
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center text-gray-500 dark:text-gray-400 py-6">
                No payment data available
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Customer Insights */}
      <Card variant="neu">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5" />
            Customer Insights
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-3">
              <h3 className="font-medium text-gray-900 dark:text-gray-100">Customer Stats</h3>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">Total Customers</span>
                  <span className="font-medium">{customerData?.totalCustomers || 0}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">New This Month</span>
                  <span className="font-medium">{customerData?.newCustomersThisMonth || 0}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">Returning Customers</span>
                  <span className="font-medium">{customerData?.returningCustomers || 0}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">Avg. Lifetime Value</span>
                  <span className="font-medium">${customerData?.averageLifetimeValue?.toFixed(2) || '0.00'}</span>
                </div>
              </div>
            </div>
            
            <div className="md:col-span-2">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-3">Top Customers</h3>
              {customerData?.topCustomers?.length > 0 ? (
                <div className="space-y-2">
                  {customerData.topCustomers.map((customer: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <span className="font-medium">{customer.name}</span>
                      <span className="text-primary-600 dark:text-primary-400 font-medium">
                        ${customer.spent.toFixed(2)}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-gray-500 dark:text-gray-400 py-6">
                  No customer data available
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AnalyticsDashboard;
