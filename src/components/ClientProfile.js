import React, { useState } from 'react';

// Mock client data - would come from your database in production
const mockClient = {
  name: "<PERSON>",
  email: "<EMAIL>",
  phone: "************",
  preferences: ["Old Fashioned", "Espresso Martini", "Mojito"],
  pastOrders: [
    { date: "2023-05-15", package: "Premium Package", amount: "$900" },
    { date: "2022-12-10", package: "Essential Package", amount: "$500" }
  ],
  notes: "Prefers less sweet drinks. Allergic to strawberries."
};

const ClientProfile = ({ clientData }) => {
  const [activeClient, setActiveClient] = useState(clientData || mockClient);
  const [isEditing, setIsEditing] = useState(false);
  const [editedNotes, setEditedNotes] = useState(activeClient?.notes || "");
  
  const handleSaveNotes = () => {
    setActiveClient({
      ...activeClient,
      notes: editedNotes
    });
    setIsEditing(false);
  };
  
  if (!activeClient) {
    return (
      <div className="feature-card">
        <h3>Client Profile</h3>
        <p>No client information available yet.</p>
        <button className="create-profile-btn">Create Profile</button>
      </div>
    );
  }
  
  return (
    <div className="feature-card">
      <h3>Client Profile</h3>
      
      <div className="profile-header">
        <div className="profile-avatar">
          {activeClient.name.charAt(0)}
        </div>
        <div className="profile-basic-info">
          <h4>{activeClient.name}</h4>
          <p>{activeClient.email} • {activeClient.phone}</p>
        </div>
      </div>
      
      <div className="profile-section">
        <h4>Favorite Drinks</h4>
        <div className="preferences-list">
          {activeClient.preferences.map((drink, index) => (
            <span key={index} className="preference-tag">{drink}</span>
          ))}
        </div>
      </div>
      
      <div className="profile-section">
        <h4>Order History</h4>
        {activeClient.pastOrders.length > 0 ? (
          <div className="order-history">
            {activeClient.pastOrders.map((order, index) => (
              <div key={index} className="order-item">
                <span className="order-date">{order.date}</span>
                <span className="order-package">{order.package}</span>
                <span className="order-amount">{order.amount}</span>
              </div>
            ))}
          </div>
        ) : (
          <p>No past orders.</p>
        )}
      </div>
      
      <div className="profile-section">
        <div className="notes-header">
          <h4>Notes</h4>
          {!isEditing && (
            <button 
              className="edit-notes-btn"
              onClick={() => setIsEditing(true)}
            >
              Edit
            </button>
          )}
        </div>
        
        {isEditing ? (
          <div className="notes-editor">
            <textarea
              value={editedNotes}
              onChange={(e) => setEditedNotes(e.target.value)}
              rows={4}
            />
            <div className="notes-actions">
              <button onClick={() => setIsEditing(false)}>Cancel</button>
              <button onClick={handleSaveNotes}>Save</button>
            </div>
          </div>
        ) : (
          <p className="client-notes">{activeClient.notes}</p>
        )}
      </div>
    </div>
  );
};

export default ClientProfile;