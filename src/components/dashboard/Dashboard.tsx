import React, { useState, useEffect } from 'react';
import { 
  DollarSign, 
  ShoppingCart, 
  Users, 
  TrendingUp,
  Package,
  AlertTriangle,
  Clock,
  CreditCard
} from 'lucide-react';
import { usePOS } from '../../contexts/POSContext';
import { OrderService } from '../../services/orderService';
import { ProductService } from '../../services/productService';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';

interface DashboardStats {
  totalSales: number;
  totalOrders: number;
  averageOrderValue: number;
  lowStockCount: number;
}

const Dashboard: React.FC = () => {
  const { state } = usePOS();
  const [stats, setStats] = useState<DashboardStats>({
    totalSales: 0,
    totalOrders: 0,
    averageOrderValue: 0,
    lowStockCount: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadDashboardData = async () => {
      if (!state.business?.id) return;

      try {
        setIsLoading(true);
        
        // Load today's sales
        const salesData = await OrderService.getTodaysSales(state.business.id);
        
        // Load low stock products
        const lowStockProducts = await ProductService.getLowStockProducts(state.business.id);
        
        setStats({
          ...salesData,
          lowStockCount: lowStockProducts.length
        });
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDashboardData();
  }, [state.business?.id]);

  const statCards = [
    {
      title: "Today's Sales",
      value: `$${stats.totalSales.toFixed(2)}`,
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/20'
    },
    {
      title: "Orders",
      value: stats.totalOrders.toString(),
      icon: ShoppingCart,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20'
    },
    {
      title: "Avg Order Value",
      value: `$${stats.averageOrderValue.toFixed(2)}`,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20'
    },
    {
      title: "Low Stock Items",
      value: stats.lowStockCount.toString(),
      icon: AlertTriangle,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20'
    }
  ];

  const quickActions = [
    {
      title: 'New Sale',
      description: 'Start a new transaction',
      icon: ShoppingCart,
      href: '/pos',
      color: 'bg-primary-600 hover:bg-primary-700'
    },
    {
      title: 'Add Product',
      description: 'Add new product to inventory',
      icon: Package,
      href: '/products/new',
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      title: 'View Customers',
      description: 'Manage customer database',
      icon: Users,
      href: '/customers',
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      title: 'Analytics',
      description: 'View detailed reports',
      icon: TrendingUp,
      href: '/analytics',
      color: 'bg-purple-600 hover:bg-purple-700'
    }
  ];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} variant="neu" className="animate-pulse">
              <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Welcome back, {state.user?.name}!
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Here's what's happening at {state.business?.name} today.
          </p>
        </div>
        
        <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <Clock size={16} />
          <span>{new Date().toLocaleDateString()}</span>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} variant="neu" className="hover:shadow-neu-pressed transition-shadow">
              <CardContent className="flex items-center">
                <div className={`p-3 rounded-lg ${stat.bgColor} mr-4`}>
                  <Icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {stat.value}
                  </p>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card variant="neu">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {quickActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <Button
                    key={index}
                    variant="ghost"
                    className={`h-auto p-4 flex flex-col items-center space-y-2 ${action.color} text-white hover:opacity-90`}
                  >
                    <Icon size={24} />
                    <div className="text-center">
                      <p className="font-medium">{action.title}</p>
                      <p className="text-xs opacity-90">{action.description}</p>
                    </div>
                  </Button>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card variant="neu">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.totalOrders > 0 ? (
                <div className="text-center py-8">
                  <ShoppingCart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">
                    {stats.totalOrders} orders processed today
                  </p>
                  <Button variant="primary" size="sm" className="mt-4">
                    View All Orders
                  </Button>
                </div>
              ) : (
                <div className="text-center py-8">
                  <ShoppingCart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">
                    No orders yet today
                  </p>
                  <Button variant="primary" size="sm" className="mt-4">
                    Start First Sale
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Methods Status */}
      <Card variant="neu">
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="mr-2 h-5 w-5" />
            Payment Methods
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {state.business?.paymentMethods?.map((method, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-2 ${
                  method.isActive 
                    ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20' 
                    : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/20'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {method.name}
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    method.isActive 
                      ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' 
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                  }`}>
                    {method.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            )) || (
              <div className="col-span-3 text-center py-4 text-gray-500 dark:text-gray-400">
                No payment methods configured
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;
