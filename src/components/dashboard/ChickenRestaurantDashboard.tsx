import React from 'react';
import { 
  DollarSign, 
  ShoppingCart, 
  Users, 
  TrendingUp,
  Clock,
  AlertTriangle,
  Star,
  Flame
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';

const ChickenRestaurantDashboard: React.FC = () => {
  // Mock data for chicken restaurant
  const todaysStats = {
    sales: 2847.50,
    orders: 127,
    customers: 89,
    avgOrderValue: 22.43
  };

  const popularItems = [
    { name: 'Spicy Chicken Sandwich', sold: 34, emoji: '🍗' },
    { name: 'Family Meal (8pc)', sold: 18, emoji: '👨‍👩‍👧‍👦' },
    { name: 'Chicken Wings (6pc)', sold: 25, emoji: '🍗' },
    { name: 'Cajun Fries', sold: 42, emoji: '🍟' }
  ];

  const recentOrders = [
    { id: '#1247', customer: '<PERSON>', items: 'Family Meal + Drinks', total: 34.50, time: '2 min ago' },
    { id: '#1246', customer: '<PERSON>', items: 'Spicy Sandwich Combo', total: 18.99, time: '5 min ago' },
    { id: '#1245', customer: '<PERSON>', items: '8pc Chicken + Sides', total: 27.25, time: '8 min ago' },
    { id: '#1244', customer: 'James Wilson', items: 'Wings + Mac & Cheese', total: 14.98, time: '12 min ago' }
  ];

  const lowStockItems = [
    { name: 'Chicken Wings', stock: 6, alert: 10 },
    { name: 'Mac & Cheese', stock: 8, alert: 15 },
    { name: 'Buttermilk Biscuits', stock: 12, alert: 20 }
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold mb-2">Welcome to Crispy Crown! 🍗</h1>
            <p className="text-primary-100">Louisiana Style Chicken - Serving authentic flavors since today</p>
          </div>
          <div className="text-right">
            <p className="text-primary-200 text-sm">Today's Date</p>
            <p className="text-xl font-semibold">{new Date().toLocaleDateString()}</p>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card variant="neu">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-green-100 mr-4">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Today's Sales</p>
                <p className="text-2xl font-bold text-gray-900">${todaysStats.sales.toFixed(2)}</p>
                <p className="text-xs text-green-600">↗️ +15.3% from yesterday</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="neu">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-blue-100 mr-4">
                <ShoppingCart className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Orders</p>
                <p className="text-2xl font-bold text-gray-900">{todaysStats.orders}</p>
                <p className="text-xs text-gray-500">Avg. ${todaysStats.avgOrderValue} per order</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="neu">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-purple-100 mr-4">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Customers</p>
                <p className="text-2xl font-bold text-gray-900">{todaysStats.customers}</p>
                <p className="text-xs text-gray-500">12 new today</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="neu">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-orange-100 mr-4">
                <Flame className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Hot Items</p>
                <p className="text-2xl font-bold text-gray-900">🍗</p>
                <p className="text-xs text-gray-500">Spicy Sandwich leading</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Popular Items */}
        <Card variant="neu">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Star className="mr-2 h-5 w-5 text-yellow-500" />
              Today's Popular Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {popularItems.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{item.emoji}</span>
                    <div>
                      <p className="font-medium">{item.name}</p>
                      <p className="text-sm text-gray-600">{item.sold} sold today</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="w-12 h-2 bg-gray-200 rounded-full">
                      <div 
                        className="h-2 bg-primary-500 rounded-full" 
                        style={{ width: `${(item.sold / 50) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Orders */}
        <Card variant="neu">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5 text-blue-500" />
              Recent Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentOrders.map((order, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{order.id}</p>
                    <p className="text-sm text-gray-600">{order.customer} • {order.items}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-green-600">${order.total}</p>
                    <p className="text-xs text-gray-500">{order.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions & Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <Card variant="neu">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <Button variant="primary" className="h-20 flex flex-col items-center justify-center space-y-2">
                <ShoppingCart className="h-6 w-6" />
                <span className="text-sm font-medium">New Order</span>
              </Button>
              <Button variant="neu" className="h-20 flex flex-col items-center justify-center space-y-2">
                <span className="text-2xl">🍗</span>
                <span className="text-sm font-medium">Add Menu Item</span>
              </Button>
              <Button variant="neu" className="h-20 flex flex-col items-center justify-center space-y-2">
                <Users className="h-6 w-6" />
                <span className="text-sm font-medium">Customer Lookup</span>
              </Button>
              <Button variant="neu" className="h-20 flex flex-col items-center justify-center space-y-2">
                <TrendingUp className="h-6 w-6" />
                <span className="text-sm font-medium">Sales Report</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Low Stock Alert */}
        <Card variant="neu">
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="mr-2 h-5 w-5 text-red-500" />
              Low Stock Alert
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {lowStockItems.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                  <div>
                    <p className="font-medium text-red-900">{item.name}</p>
                    <p className="text-sm text-red-600">Current stock: {item.stock} (Alert at {item.alert})</p>
                  </div>
                  <Button variant="primary" size="sm">
                    Restock
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Restaurant Status */}
      <Card variant="neu">
        <CardHeader>
          <CardTitle>Restaurant Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="p-4 rounded-lg border-2 border-green-200 bg-green-50">
              <div className="flex items-center justify-between">
                <span className="font-medium">🔥 Fryer 1</span>
                <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span>
              </div>
            </div>
            <div className="p-4 rounded-lg border-2 border-green-200 bg-green-50">
              <div className="flex items-center justify-between">
                <span className="font-medium">🔥 Fryer 2</span>
                <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span>
              </div>
            </div>
            <div className="p-4 rounded-lg border-2 border-yellow-200 bg-yellow-50">
              <div className="flex items-center justify-between">
                <span className="font-medium">🧊 Freezer</span>
                <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">Check Temp</span>
              </div>
            </div>
            <div className="p-4 rounded-lg border-2 border-green-200 bg-green-50">
              <div className="flex items-center justify-between">
                <span className="font-medium">💳 POS System</span>
                <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Online</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ChickenRestaurantDashboard;
