import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Plus, 
  Edit, 
  Trash2, 
  RefreshCw,
  Mail,
  Key,
  Shield,
  CheckCircle,
  XCircle,
  User,
  UserPlus
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { usePOS } from '../../contexts/POSContext';
import { UserService } from '../../services/userService';
import { User, Permission } from '../../types';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { Modal, ModalHeader, ModalBody, ModalFooter } from '../ui/Modal';

interface UserFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: User['role'];
}

const UserManagement: React.FC = () => {
  const { state } = usePOS();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showUserForm, setShowUserForm] = useState(false);
  const [showPermissionsForm, setShowPermissionsForm] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userPermissions, setUserPermissions] = useState<Permission[]>([]);
  const [availablePermissions, setAvailablePermissions] = useState<any[]>([]);

  const { register, handleSubmit, reset, watch, formState: { errors } } = useForm<UserFormData>();
  const watchPassword = watch('password');

  useEffect(() => {
    loadUsers();
    setAvailablePermissions(UserService.getAvailablePermissions());
  }, [state.business?.id]);

  useEffect(() => {
    if (selectedUser) {
      setUserPermissions([...selectedUser.permissions]);
    }
  }, [selectedUser]);

  const loadUsers = async () => {
    if (!state.business?.id) return;

    try {
      setIsLoading(true);
      const data = await UserService.getBusinessUsers(state.business.id);
      setUsers(data);
    } catch (error) {
      console.error('Error loading users:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateUser = async (data: UserFormData) => {
    if (!state.business?.id) return;

    try {
      await UserService.createUser(state.business.id, {
        name: data.name,
        email: data.email,
        password: data.password,
        role: data.role
      });
      
      setShowUserForm(false);
      reset();
      await loadUsers();
    } catch (error) {
      console.error('Error creating user:', error);
    }
  };

  const handleDeactivateUser = async (userId: string) => {
    if (!confirm('Are you sure you want to deactivate this user?')) return;

    try {
      await UserService.deactivateUser(userId);
      await loadUsers();
    } catch (error) {
      console.error('Error deactivating user:', error);
    }
  };

  const handleReactivateUser = async (userId: string) => {
    try {
      await UserService.reactivateUser(userId);
      await loadUsers();
    } catch (error) {
      console.error('Error reactivating user:', error);
    }
  };

  const handleSendPasswordReset = async (email: string) => {
    if (!confirm(`Send password reset email to ${email}?`)) return;

    try {
      await UserService.sendPasswordReset(email);
      alert('Password reset email sent successfully');
    } catch (error) {
      console.error('Error sending password reset:', error);
      alert('Failed to send password reset email');
    }
  };

  const handleEditPermissions = (user: User) => {
    setSelectedUser(user);
    setShowPermissionsForm(true);
  };

  const handleTogglePermission = (action: string, resource: string) => {
    const newPermissions = [...userPermissions];
    const existingIndex = newPermissions.findIndex(
      p => p.action === action && p.resource === resource
    );

    if (existingIndex >= 0) {
      // Toggle existing permission
      newPermissions[existingIndex] = {
        ...newPermissions[existingIndex],
        allowed: !newPermissions[existingIndex].allowed
      };
    } else {
      // Add new permission
      newPermissions.push({ action, resource, allowed: true });
    }

    setUserPermissions(newPermissions);
  };

  const handleSavePermissions = async () => {
    if (!selectedUser) return;

    try {
      await UserService.updateUserPermissions(selectedUser.id, userPermissions);
      setShowPermissionsForm(false);
      setSelectedUser(null);
      await loadUsers();
    } catch (error) {
      console.error('Error updating permissions:', error);
    }
  };

  const hasPermission = (action: string, resource: string): boolean => {
    return userPermissions.some(
      p => p.action === action && p.resource === resource && p.allowed
    );
  };

  const getRoleBadgeColor = (role: User['role']) => {
    switch (role) {
      case 'owner':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'manager':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'cashier':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatDate = (date?: Date) => {
    if (!date) return 'Never';
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} variant="neu" className="animate-pulse">
            <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">User Management</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage staff accounts and permissions
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="primary"
            leftIcon={<UserPlus size={16} />}
            onClick={() => {
              setSelectedUser(null);
              setShowUserForm(true);
            }}
          >
            Add User
          </Button>
          
          <Button
            variant="neu"
            leftIcon={<RefreshCw size={16} />}
            onClick={loadUsers}
          >
            Refresh
          </Button>
        </div>
      </div>

      {/* User List */}
      {users.length === 0 ? (
        <Card variant="neu" className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            No users found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Add your first staff member to get started
          </p>
          <Button
            variant="primary"
            leftIcon={<UserPlus size={16} />}
            onClick={() => {
              setSelectedUser(null);
              setShowUserForm(true);
            }}
          >
            Add User
          </Button>
        </Card>
      ) : (
        <div className="space-y-4">
          {users.map(user => (
            <Card key={user.id} variant="neu" className="hover:shadow-neu-pressed transition-shadow">
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center">
                      <User className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                    </div>
                    
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                          {user.name}
                        </h3>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRoleBadgeColor(user.role)}`}>
                          {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                        </span>
                        {!user.isActive && (
                          <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                            Inactive
                          </span>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                        <span className="flex items-center">
                          <Mail className="h-3 w-3 mr-1" />
                          {user.email}
                        </span>
                        <span>Created: {formatDate(user.createdAt)}</span>
                        {user.lastLogin && (
                          <span>Last login: {formatDate(user.lastLogin)}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="neu"
                      size="sm"
                      onClick={() => handleEditPermissions(user)}
                      title="Edit Permissions"
                    >
                      <Shield size={14} />
                    </Button>
                    
                    <Button
                      variant="neu"
                      size="sm"
                      onClick={() => handleSendPasswordReset(user.email)}
                      title="Reset Password"
                    >
                      <Key size={14} />
                    </Button>
                    
                    {user.isActive ? (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeactivateUser(user.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                        title="Deactivate User"
                      >
                        <XCircle size={14} />
                      </Button>
                    ) : (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleReactivateUser(user.id)}
                        className="text-green-600 hover:text-green-700 hover:bg-green-50 dark:hover:bg-green-900/20"
                        title="Reactivate User"
                      >
                        <CheckCircle size={14} />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Add User Modal */}
      <Modal
        isOpen={showUserForm}
        onClose={() => setShowUserForm(false)}
        title="Add New User"
        size="md"
      >
        <form onSubmit={handleSubmit(handleCreateUser)} className="space-y-4">
          <Input
            label="Full Name *"
            placeholder="Enter user's full name"
            {...register('name', { required: 'Name is required' })}
            error={errors.name?.message}
          />

          <Input
            label="Email Address *"
            type="email"
            placeholder="Enter user's email"
            {...register('email', { 
              required: 'Email is required',
              pattern: {
                value: /^\S+@\S+$/i,
                message: 'Invalid email address'
              }
            })}
            error={errors.email?.message}
          />

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Role *
            </label>
            <select
              {...register('role', { required: 'Role is required' })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white dark:bg-dark-surface dark:border-gray-600 dark:text-gray-100"
            >
              <option value="">Select role</option>
              <option value="manager">Manager</option>
              <option value="cashier">Cashier</option>
            </select>
            {errors.role && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.role.message}
              </p>
            )}
          </div>

          <Input
            label="Password *"
            type="password"
            placeholder="Create a password"
            {...register('password', { 
              required: 'Password is required',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters'
              }
            })}
            error={errors.password?.message}
          />

          <Input
            label="Confirm Password *"
            type="password"
            placeholder="Confirm password"
            {...register('confirmPassword', { 
              required: 'Please confirm password',
              validate: value => value === watchPassword || 'Passwords do not match'
            })}
            error={errors.confirmPassword?.message}
          />

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={() => setShowUserForm(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
            >
              Create User
            </Button>
          </div>
        </form>
      </Modal>

      {/* Permissions Modal */}
      <Modal
        isOpen={showPermissionsForm}
        onClose={() => setShowPermissionsForm(false)}
        title={`Edit Permissions: ${selectedUser?.name}`}
        size="lg"
      >
        <div className="space-y-6">
          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
            <p className="text-yellow-800 dark:text-yellow-400 text-sm">
              <strong>Note:</strong> Permissions granted here override the default permissions for the user's role.
            </p>
          </div>

          <div className="space-y-4">
            {availablePermissions.reduce((acc: any, permission) => {
              // Group by resource
              const resource = permission.resource;
              if (!acc[resource]) {
                acc[resource] = [];
              }
              acc[resource].push(permission);
              return acc;
            }, {})
            .map((permissions: any[], resource: string) => (
              <Card key={resource} variant="neu">
                <CardHeader>
                  <CardTitle className="capitalize">{resource}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {permissions.map(permission => (
                      <div 
                        key={`${permission.action}_${permission.resource}`}
                        className="flex items-center space-x-2"
                      >
                        <input
                          type="checkbox"
                          id={`${permission.action}_${permission.resource}`}
                          checked={hasPermission(permission.action, permission.resource)}
                          onChange={() => handleTogglePermission(permission.action, permission.resource)}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <label 
                          htmlFor={`${permission.action}_${permission.resource}`}
                          className="text-sm text-gray-700 dark:text-gray-300"
                        >
                          {permission.description}
                        </label>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="ghost"
              onClick={() => setShowPermissionsForm(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleSavePermissions}
            >
              Save Permissions
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default UserManagement;
