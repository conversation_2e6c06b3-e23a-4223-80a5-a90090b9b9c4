import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Plus, Edit, Trash2, CreditCard, ToggleLeft, ToggleRight } from 'lucide-react';
import { PaymentService } from '../../services/paymentService';
import { AuthService } from '../../services/authService';
import { usePOS } from '../../contexts/POSContext';
import { PaymentMethod } from '../../types';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/Card';
import Button from '../ui/Button';
import Input from '../ui/Input';
import { Modal, ModalHeader, ModalBody, ModalFooter } from '../ui/Modal';

interface PaymentMethodFormData {
  name: string;
  type: PaymentMethod['type'];
  handle?: string;
  email?: string;
  apiKey?: string;
}

const PaymentMethodSettings: React.FC = () => {
  const { state, dispatch } = usePOS();
  const [showForm, setShowForm] = useState(false);
  const [editingMethod, setEditingMethod] = useState<PaymentMethod | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { register, handleSubmit, reset, watch, formState: { errors } } = useForm<PaymentMethodFormData>();
  const selectedType = watch('type');

  const paymentMethodTypes = [
    { value: 'cash', label: 'Cash', icon: '💵' },
    { value: 'cashapp', label: 'CashApp', icon: '💚' },
    { value: 'zelle', label: 'Zelle', icon: '🏦' },
    { value: 'venmo', label: 'Venmo', icon: '💙' },
    { value: 'qr', label: 'QR Payment', icon: '📱' },
    { value: 'card', label: 'Credit/Debit Card', icon: '💳' },
    { value: 'other', label: 'Other', icon: '💰' }
  ];

  const handleEdit = (method: PaymentMethod) => {
    setEditingMethod(method);
    reset({
      name: method.name,
      type: method.type,
      handle: method.config?.handle || '',
      email: method.config?.email || '',
      apiKey: method.config?.apiKey || ''
    });
    setShowForm(true);
  };

  const handleToggleActive = async (method: PaymentMethod) => {
    if (!state.business?.id) return;

    try {
      const updatedMethods = state.business.paymentMethods.map(m =>
        m.id === method.id ? { ...m, isActive: !m.isActive } : m
      );

      await AuthService.updateBusinessProfile(state.business.id, {
        paymentMethods: updatedMethods
      });

      // Update local state
      dispatch({
        type: 'SET_BUSINESS',
        payload: { ...state.business, paymentMethods: updatedMethods }
      });
    } catch (error) {
      console.error('Error updating payment method:', error);
    }
  };

  const handleDelete = async (methodId: string) => {
    if (!state.business?.id || !confirm('Are you sure you want to delete this payment method?')) {
      return;
    }

    try {
      const updatedMethods = state.business.paymentMethods.filter(m => m.id !== methodId);

      await AuthService.updateBusinessProfile(state.business.id, {
        paymentMethods: updatedMethods
      });

      // Update local state
      dispatch({
        type: 'SET_BUSINESS',
        payload: { ...state.business, paymentMethods: updatedMethods }
      });
    } catch (error) {
      console.error('Error deleting payment method:', error);
    }
  };

  const onSubmit = async (data: PaymentMethodFormData) => {
    if (!state.business?.id) return;

    setIsLoading(true);
    try {
      const config: any = {};
      
      // Set config based on payment type
      if (data.type === 'cashapp' || data.type === 'venmo') {
        config.handle = data.handle;
      } else if (data.type === 'zelle') {
        config.email = data.email;
      } else if (data.type === 'card') {
        config.apiKey = data.apiKey;
      }

      const newMethod: PaymentMethod = {
        id: editingMethod?.id || `pm_${Date.now()}`,
        type: data.type,
        name: data.name,
        isActive: true,
        config
      };

      // Validate the payment method
      if (!PaymentService.validatePaymentMethod(newMethod)) {
        throw new Error('Invalid payment method configuration');
      }

      let updatedMethods: PaymentMethod[];
      if (editingMethod) {
        updatedMethods = state.business.paymentMethods.map(m =>
          m.id === editingMethod.id ? newMethod : m
        );
      } else {
        updatedMethods = [...state.business.paymentMethods, newMethod];
      }

      await AuthService.updateBusinessProfile(state.business.id, {
        paymentMethods: updatedMethods
      });

      // Update local state
      dispatch({
        type: 'SET_BUSINESS',
        payload: { ...state.business, paymentMethods: updatedMethods }
      });

      setShowForm(false);
      setEditingMethod(null);
      reset();
    } catch (error) {
      console.error('Error saving payment method:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingMethod(null);
    reset();
  };

  return (
    <div className="space-y-6">
      <Card variant="neu">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <CreditCard className="mr-2 h-5 w-5" />
              Payment Methods
            </CardTitle>
            <Button
              variant="primary"
              size="sm"
              leftIcon={<Plus size={16} />}
              onClick={() => setShowForm(true)}
            >
              Add Payment Method
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          {!state.business?.paymentMethods?.length ? (
            <div className="text-center py-8">
              <CreditCard className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No payment methods configured
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Add payment methods to start accepting payments with POSess
              </p>
              <Button
                variant="primary"
                leftIcon={<Plus size={16} />}
                onClick={() => setShowForm(true)}
              >
                Add First Payment Method
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {state.business.paymentMethods.map((method) => {
                const info = PaymentService.getPaymentMethodInfo(method);
                return (
                  <div
                    key={method.id}
                    className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{info.icon}</span>
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-gray-100">
                          {method.name}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {info.description}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleToggleActive(method)}
                        className={`p-1 rounded-full transition-colors ${
                          method.isActive 
                            ? 'text-green-600 hover:text-green-700' 
                            : 'text-gray-400 hover:text-gray-500'
                        }`}
                      >
                        {method.isActive ? <ToggleRight size={24} /> : <ToggleLeft size={24} />}
                      </button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(method)}
                      >
                        <Edit size={14} />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(method.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                      >
                        <Trash2 size={14} />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Method Form Modal */}
      <Modal
        isOpen={showForm}
        onClose={handleCancel}
        title={editingMethod ? 'Edit Payment Method' : 'Add Payment Method'}
        size="md"
      >
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <Input
            label="Display Name *"
            placeholder="e.g., Business CashApp"
            {...register('name', { required: 'Display name is required' })}
            error={errors.name?.message}
          />

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Payment Type *
            </label>
            <select
              {...register('type', { required: 'Payment type is required' })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white dark:bg-dark-surface dark:border-gray-600 dark:text-gray-100"
            >
              <option value="">Select payment type</option>
              {paymentMethodTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.icon} {type.label}
                </option>
              ))}
            </select>
            {errors.type && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {errors.type.message}
              </p>
            )}
          </div>

          {/* Dynamic fields based on payment type */}
          {(selectedType === 'cashapp' || selectedType === 'venmo') && (
            <Input
              label={`${selectedType === 'cashapp' ? 'CashApp' : 'Venmo'} Handle *`}
              placeholder={selectedType === 'cashapp' ? '$YourHandle' : '@YourHandle'}
              {...register('handle', { required: 'Handle is required' })}
              error={errors.handle?.message}
            />
          )}

          {selectedType === 'zelle' && (
            <Input
              label="Zelle Email *"
              type="email"
              placeholder="<EMAIL>"
              {...register('email', { 
                required: 'Email is required',
                pattern: {
                  value: /^\S+@\S+$/i,
                  message: 'Invalid email address'
                }
              })}
              error={errors.email?.message}
            />
          )}

          {selectedType === 'card' && (
            <Input
              label="API Key"
              placeholder="Stripe or other payment processor API key"
              {...register('apiKey')}
              error={errors.apiKey?.message}
            />
          )}

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={isLoading}
            >
              {editingMethod ? 'Update Method' : 'Add Method'}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default PaymentMethodSettings;
