// Core POS Types for CraftPOS/IndieTill

export interface Business {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  taxRate: number;
  currency: string;
  logo?: string;
  theme: {
    primaryColor: string;
    secondaryColor: string;
    darkMode: boolean;
  };
  paymentMethods: PaymentMethod[];
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  id: string;
  businessId: string;
  email: string;
  name: string;
  role: 'owner' | 'manager' | 'cashier';
  permissions: Permission[];
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
}

export interface Permission {
  action: string;
  resource: string;
  allowed: boolean;
}

export interface Product {
  id: string;
  businessId: string;
  name: string;
  description?: string;
  price: number;
  cost?: number;
  sku?: string;
  barcode?: string;
  category: Category;
  image?: string;
  modifiers: Modifier[];
  inventory: {
    trackInventory: boolean;
    currentStock: number;
    lowStockAlert: number;
    unit: string;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Category {
  id: string;
  name: string;
  color: string;
  icon?: string;
  sortOrder: number;
}

export interface Modifier {
  id: string;
  name: string;
  type: 'single' | 'multiple';
  required: boolean;
  options: ModifierOption[];
}

export interface ModifierOption {
  id: string;
  name: string;
  priceAdjustment: number;
  isDefault?: boolean;
}

export interface Order {
  id: string;
  businessId: string;
  orderNumber: string;
  type: 'in-person' | 'online' | 'phone' | 'qr';
  status: 'open' | 'paid' | 'voided' | 'refunded';
  items: OrderItem[];
  customer?: Customer;
  subtotal: number;
  tax: number;
  tip: number;
  discount: number;
  total: number;
  payments: Payment[];
  cashierId: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  selectedModifiers: SelectedModifier[];
  subtotal: number;
  notes?: string;
}

export interface SelectedModifier {
  modifierId: string;
  modifierName: string;
  optionId: string;
  optionName: string;
  priceAdjustment: number;
}

export interface Customer {
  id: string;
  businessId: string;
  name: string;
  email?: string;
  phone?: string;
  loyaltyPoints: number;
  totalSpent: number;
  visitCount: number;
  lastVisit?: Date;
  notes?: string;
  createdAt: Date;
}

export interface Payment {
  id: string;
  orderId: string;
  method: PaymentMethod;
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  transactionId?: string;
  reference?: string;
  processedAt?: Date;
  metadata?: Record<string, any>;
}

export interface PaymentMethod {
  id: string;
  type: 'cash' | 'card' | 'cashapp' | 'zelle' | 'venmo' | 'qr' | 'other';
  name: string;
  isActive: boolean;
  config?: {
    handle?: string; // For CashApp, Venmo
    email?: string; // For Zelle
    qrCode?: string; // For QR payments
    apiKey?: string; // For Stripe, etc.
  };
}

export interface Shift {
  id: string;
  businessId: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  startingCash: number;
  endingCash?: number;
  totalSales: number;
  totalTax: number;
  totalTips: number;
  orderCount: number;
  status: 'active' | 'closed';
  notes?: string;
}

export interface InventoryTransaction {
  id: string;
  productId: string;
  type: 'sale' | 'restock' | 'adjustment' | 'waste';
  quantity: number;
  reason?: string;
  userId: string;
  createdAt: Date;
}

export interface Receipt {
  id: string;
  orderId: string;
  type: 'customer' | 'merchant';
  format: 'digital' | 'print';
  content: string;
  sentTo?: string; // email or phone
  createdAt: Date;
}

// Analytics Types
export interface SalesAnalytics {
  period: 'day' | 'week' | 'month' | 'year';
  totalSales: number;
  totalOrders: number;
  averageOrderValue: number;
  topProducts: ProductSales[];
  salesByHour: HourlySales[];
  paymentMethodBreakdown: PaymentMethodSales[];
}

export interface ProductSales {
  productId: string;
  productName: string;
  quantitySold: number;
  revenue: number;
  profit: number;
}

export interface HourlySales {
  hour: number;
  sales: number;
  orders: number;
}

export interface PaymentMethodSales {
  method: string;
  amount: number;
  percentage: number;
}

// UI State Types
export interface CartItem extends OrderItem {
  product: Product;
}

export interface POSState {
  currentOrder: Order | null;
  cart: CartItem[];
  selectedCustomer: Customer | null;
  activeShift: Shift | null;
  paymentMethod: PaymentMethod | null;
}
