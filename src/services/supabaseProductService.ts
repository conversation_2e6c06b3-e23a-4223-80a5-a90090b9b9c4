import { supabase, supabaseHelpers } from '../config/supabase';
import { Product, Category } from '../types';

export class SupabaseProductService {
  // Get all products for a business
  static async getProducts(businessId: string): Promise<Product[]> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          category:categories(*)
        `)
        .eq('business_id', businessId)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;

      return data.map(this.mapProductFromDB);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch products');
    }
  }

  // Get single product
  static async getProduct(productId: string): Promise<Product | null> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          category:categories(*)
        `)
        .eq('id', productId)
        .single();

      if (error) return null;

      return this.mapProductFromDB(data);
    } catch (error: any) {
      return null;
    }
  }

  // Create new product
  static async createProduct(productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const { data, error } = await supabaseHelpers.db.insert('products', {
        business_id: productData.businessId,
        category_id: productData.category.id,
        name: productData.name,
        description: productData.description,
        price: productData.price,
        cost: productData.cost,
        sku: productData.sku,
        barcode: productData.barcode,
        image_url: productData.image,
        modifiers: productData.modifiers || [],
        inventory: {
          track_inventory: productData.inventory.trackInventory,
          current_stock: productData.inventory.currentStock,
          low_stock_alert: productData.inventory.lowStockAlert,
          unit: productData.inventory.unit
        },
        is_active: productData.isActive
      });

      if (error) throw error;

      return data[0].id;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to create product');
    }
  }

  // Update product
  static async updateProduct(productId: string, updates: Partial<Product>): Promise<void> {
    try {
      const updateData: any = {};

      if (updates.name) updateData.name = updates.name;
      if (updates.description !== undefined) updateData.description = updates.description;
      if (updates.price) updateData.price = updates.price;
      if (updates.cost !== undefined) updateData.cost = updates.cost;
      if (updates.sku !== undefined) updateData.sku = updates.sku;
      if (updates.barcode !== undefined) updateData.barcode = updates.barcode;
      if (updates.image !== undefined) updateData.image_url = updates.image;
      if (updates.modifiers) updateData.modifiers = updates.modifiers;
      if (updates.isActive !== undefined) updateData.is_active = updates.isActive;
      if (updates.category) updateData.category_id = updates.category.id;
      
      if (updates.inventory) {
        updateData.inventory = {
          track_inventory: updates.inventory.trackInventory,
          current_stock: updates.inventory.currentStock,
          low_stock_alert: updates.inventory.lowStockAlert,
          unit: updates.inventory.unit
        };
      }

      const { error } = await supabaseHelpers.db.update('products', productId, updateData);

      if (error) throw error;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update product');
    }
  }

  // Delete product (soft delete)
  static async deleteProduct(productId: string): Promise<void> {
    try {
      const { error } = await supabaseHelpers.db.update('products', productId, {
        is_active: false
      });

      if (error) throw error;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to delete product');
    }
  }

  // Upload product image
  static async uploadProductImage(productId: string, file: File): Promise<string> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${productId}.${fileExt}`;
      const filePath = `products/${fileName}`;

      const { error: uploadError } = await supabaseHelpers.storage.upload('product-images', filePath, file);

      if (uploadError) throw uploadError;

      const { data: urlData } = supabaseHelpers.storage.getPublicUrl('product-images', filePath);

      // Update product with image URL
      await this.updateProduct(productId, { image: urlData.publicUrl });

      return urlData.publicUrl;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to upload product image');
    }
  }

  // Get categories for a business
  static async getCategories(businessId: string): Promise<Category[]> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('business_id', businessId)
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;

      return data.map(this.mapCategoryFromDB);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch categories');
    }
  }

  // Create new category
  static async createCategory(businessId: string, categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const { data, error } = await supabaseHelpers.db.insert('categories', {
        business_id: businessId,
        name: categoryData.name,
        color: categoryData.color,
        icon: categoryData.icon,
        sort_order: categoryData.sortOrder,
        is_active: true
      });

      if (error) throw error;

      return data[0].id;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to create category');
    }
  }

  // Update category
  static async updateCategory(categoryId: string, updates: Partial<Category>): Promise<void> {
    try {
      const updateData: any = {};

      if (updates.name) updateData.name = updates.name;
      if (updates.color) updateData.color = updates.color;
      if (updates.icon !== undefined) updateData.icon = updates.icon;
      if (updates.sortOrder !== undefined) updateData.sort_order = updates.sortOrder;

      const { error } = await supabaseHelpers.db.update('categories', categoryId, updateData);

      if (error) throw error;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update category');
    }
  }

  // Delete category (soft delete)
  static async deleteCategory(categoryId: string): Promise<void> {
    try {
      const { error } = await supabaseHelpers.db.update('categories', categoryId, {
        is_active: false
      });

      if (error) throw error;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to delete category');
    }
  }

  // Get low stock products
  static async getLowStockProducts(businessId: string): Promise<Product[]> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          category:categories(*)
        `)
        .eq('business_id', businessId)
        .eq('is_active', true)
        .filter('inventory->track_inventory', 'eq', true)
        .filter('inventory->current_stock', 'lte', 'inventory->low_stock_alert');

      if (error) throw error;

      return data.map(this.mapProductFromDB);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch low stock products');
    }
  }

  // Update inventory
  static async updateInventory(productId: string, quantity: number, type: 'sale' | 'adjustment' | 'restock', referenceId?: string): Promise<void> {
    try {
      // Get current product
      const product = await this.getProduct(productId);
      if (!product || !product.inventory.trackInventory) return;

      // Calculate new stock level
      let newStock = product.inventory.currentStock;
      if (type === 'sale') {
        newStock -= quantity;
      } else if (type === 'restock' || type === 'adjustment') {
        newStock = quantity; // For adjustments, quantity is the new total
      }

      // Update product inventory
      await this.updateProduct(productId, {
        inventory: {
          ...product.inventory,
          currentStock: Math.max(0, newStock)
        }
      });

      // Record inventory transaction
      await supabaseHelpers.db.insert('inventory_transactions', {
        product_id: productId,
        type,
        quantity: type === 'sale' ? -quantity : quantity,
        reference_id: referenceId,
        notes: `${type} transaction`
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update inventory');
    }
  }

  // Helper method to map product from database
  private static mapProductFromDB(data: any): Product {
    return {
      id: data.id,
      businessId: data.business_id,
      name: data.name,
      description: data.description,
      price: parseFloat(data.price),
      cost: parseFloat(data.cost || 0),
      sku: data.sku,
      barcode: data.barcode,
      image: data.image_url,
      category: this.mapCategoryFromDB(data.category),
      modifiers: data.modifiers || [],
      inventory: {
        trackInventory: data.inventory?.track_inventory || false,
        currentStock: data.inventory?.current_stock || 0,
        lowStockAlert: data.inventory?.low_stock_alert || 5,
        unit: data.inventory?.unit || 'pcs'
      },
      isActive: data.is_active,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  // Helper method to map category from database
  private static mapCategoryFromDB(data: any): Category {
    return {
      id: data.id,
      name: data.name,
      color: data.color,
      icon: data.icon,
      sortOrder: data.sort_order,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }
}
