import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc,
  query, 
  where, 
  orderBy,
  Timestamp
} from 'firebase/firestore';
import { 
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  updateProfile
} from 'firebase/auth';
import { auth, db } from '../config/firebase';
import { User, Permission } from '../types';

export class UserService {
  // Get all users for a business
  static async getBusinessUsers(businessId: string): Promise<User[]> {
    try {
      const q = query(
        collection(db, 'users'),
        where('businessId', '==', businessId),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        lastLogin: doc.data().lastLogin?.toDate()
      })) as User[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch users');
    }
  }

  // Get single user
  static async getUser(userId: string): Promise<User | null> {
    try {
      const docRef = doc(db, 'users', userId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data(),
          createdAt: docSnap.data().createdAt?.toDate(),
          lastLogin: docSnap.data().lastLogin?.toDate()
        } as User;
      }
      return null;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch user');
    }
  }

  // Create new user
  static async createUser(
    businessId: string,
    userData: {
      name: string;
      email: string;
      password: string;
      role: User['role'];
      permissions?: Permission[];
    }
  ): Promise<string> {
    try {
      // Create Firebase user
      const userCredential = await createUserWithEmailAndPassword(
        auth, 
        userData.email, 
        userData.password
      );
      
      // Update Firebase user profile
      await updateProfile(userCredential.user, { 
        displayName: userData.name 
      });

      // Create user document in Firestore
      const user: Omit<User, 'id'> = {
        businessId,
        email: userData.email,
        name: userData.name,
        role: userData.role,
        permissions: userData.permissions || this.getDefaultPermissions(userData.role),
        isActive: true,
        createdAt: new Date()
      };

      await doc(db, 'users', userCredential.user.uid);
      await updateDoc(doc(db, 'users', userCredential.user.uid), {
        ...user,
        createdAt: Timestamp.now()
      });

      return userCredential.user.uid;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to create user');
    }
  }

  // Update user
  static async updateUser(userId: string, updates: Partial<User>): Promise<void> {
    try {
      const docRef = doc(db, 'users', userId);
      await updateDoc(docRef, {
        ...updates,
        ...(updates.lastLogin && { lastLogin: Timestamp.fromDate(updates.lastLogin) })
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update user');
    }
  }

  // Deactivate user (soft delete)
  static async deactivateUser(userId: string): Promise<void> {
    try {
      const docRef = doc(db, 'users', userId);
      await updateDoc(docRef, {
        isActive: false
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to deactivate user');
    }
  }

  // Reactivate user
  static async reactivateUser(userId: string): Promise<void> {
    try {
      const docRef = doc(db, 'users', userId);
      await updateDoc(docRef, {
        isActive: true
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to reactivate user');
    }
  }

  // Send password reset email
  static async sendPasswordReset(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to send password reset email');
    }
  }

  // Update user permissions
  static async updateUserPermissions(userId: string, permissions: Permission[]): Promise<void> {
    try {
      const docRef = doc(db, 'users', userId);
      await updateDoc(docRef, { permissions });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update user permissions');
    }
  }

  // Check if user has permission
  static hasPermission(user: User, action: string, resource: string): boolean {
    // Owner has all permissions
    if (user.role === 'owner') return true;

    // Check specific permissions
    return user.permissions.some(permission => 
      (permission.action === action || permission.action === '*') &&
      (permission.resource === resource || permission.resource === '*') &&
      permission.allowed
    );
  }

  // Get default permissions for role
  static getDefaultPermissions(role: User['role']): Permission[] {
    switch (role) {
      case 'owner':
        return [
          { action: '*', resource: '*', allowed: true }
        ];
      
      case 'manager':
        return [
          { action: 'read', resource: '*', allowed: true },
          { action: 'create', resource: 'products', allowed: true },
          { action: 'update', resource: 'products', allowed: true },
          { action: 'create', resource: 'orders', allowed: true },
          { action: 'update', resource: 'orders', allowed: true },
          { action: 'void', resource: 'orders', allowed: true },
          { action: 'refund', resource: 'orders', allowed: true },
          { action: 'read', resource: 'analytics', allowed: true },
          { action: 'create', resource: 'customers', allowed: true },
          { action: 'update', resource: 'customers', allowed: true },
          { action: 'manage', resource: 'inventory', allowed: true }
        ];
      
      case 'cashier':
        return [
          { action: 'read', resource: 'products', allowed: true },
          { action: 'create', resource: 'orders', allowed: true },
          { action: 'read', resource: 'orders', allowed: true },
          { action: 'read', resource: 'customers', allowed: true },
          { action: 'create', resource: 'customers', allowed: true },
          { action: 'update', resource: 'customers', allowed: true }
        ];
      
      default:
        return [];
    }
  }

  // Get available permissions
  static getAvailablePermissions(): { action: string; resource: string; description: string }[] {
    return [
      { action: 'read', resource: 'products', description: 'View products' },
      { action: 'create', resource: 'products', description: 'Create products' },
      { action: 'update', resource: 'products', description: 'Edit products' },
      { action: 'delete', resource: 'products', description: 'Delete products' },
      
      { action: 'read', resource: 'orders', description: 'View orders' },
      { action: 'create', resource: 'orders', description: 'Create orders' },
      { action: 'update', resource: 'orders', description: 'Edit orders' },
      { action: 'void', resource: 'orders', description: 'Void orders' },
      { action: 'refund', resource: 'orders', description: 'Refund orders' },
      
      { action: 'read', resource: 'customers', description: 'View customers' },
      { action: 'create', resource: 'customers', description: 'Create customers' },
      { action: 'update', resource: 'customers', description: 'Edit customers' },
      { action: 'delete', resource: 'customers', description: 'Delete customers' },
      
      { action: 'read', resource: 'analytics', description: 'View analytics' },
      { action: 'export', resource: 'analytics', description: 'Export reports' },
      
      { action: 'manage', resource: 'inventory', description: 'Manage inventory' },
      { action: 'manage', resource: 'users', description: 'Manage users' },
      { action: 'manage', resource: 'settings', description: 'Manage settings' }
    ];
  }

  // Get users by role
  static async getUsersByRole(businessId: string, role: User['role']): Promise<User[]> {
    try {
      const q = query(
        collection(db, 'users'),
        where('businessId', '==', businessId),
        where('role', '==', role),
        where('isActive', '==', true)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        lastLogin: doc.data().lastLogin?.toDate()
      })) as User[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch users by role');
    }
  }

  // Get active users count
  static async getActiveUsersCount(businessId: string): Promise<number> {
    try {
      const q = query(
        collection(db, 'users'),
        where('businessId', '==', businessId),
        where('isActive', '==', true)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.size;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get active users count');
    }
  }

  // Get user activity log (placeholder - would track user actions)
  static async getUserActivityLog(userId: string, limit: number = 50): Promise<any[]> {
    try {
      // This would query an activity log collection
      // For now, return empty array
      return [];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch user activity log');
    }
  }
}
