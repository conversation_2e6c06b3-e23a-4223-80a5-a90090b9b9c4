import { 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  User as FirebaseUser,
  updateProfile
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import { User, Business } from '../types';

export class AuthService {
  // Sign in with email and password
  static async signIn(email: string, password: string): Promise<User> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      
      // Get user data from Firestore
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
      if (!userDoc.exists()) {
        throw new Error('User profile not found');
      }
      
      const userData = userDoc.data() as User;
      
      // Update last login
      await updateDoc(doc(db, 'users', firebaseUser.uid), {
        lastLogin: new Date()
      });
      
      return userData;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to sign in');
    }
  }

  // Create new user account
  static async signUp(
    email: string, 
    password: string, 
    name: string,
    businessData: Partial<Business>
  ): Promise<{ user: User; business: Business }> {
    try {
      // Create Firebase user
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      
      // Update Firebase user profile
      await updateProfile(firebaseUser, { displayName: name });
      
      // Create business document
      const businessId = firebaseUser.uid; // Use user ID as business ID for single-business setup
      const business: Business = {
        id: businessId,
        name: businessData.name || 'My Business',
        email: email,
        phone: businessData.phone || '',
        address: businessData.address || {
          street: '',
          city: '',
          state: '',
          zipCode: '',
          country: 'US'
        },
        taxRate: businessData.taxRate || 0.08,
        currency: 'USD',
        theme: {
          primaryColor: '#0ea5e9',
          secondaryColor: '#0284c7',
          darkMode: false
        },
        paymentMethods: [
          {
            id: 'cash',
            type: 'cash',
            name: 'Cash',
            isActive: true
          }
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // Create user document
      const user: User = {
        id: firebaseUser.uid,
        businessId: businessId,
        email: email,
        name: name,
        role: 'owner',
        permissions: [
          { action: '*', resource: '*', allowed: true } // Owner has all permissions
        ],
        isActive: true,
        createdAt: new Date()
      };
      
      // Save to Firestore
      await setDoc(doc(db, 'businesses', businessId), business);
      await setDoc(doc(db, 'users', firebaseUser.uid), user);
      
      return { user, business };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to create account');
    }
  }

  // Sign out
  static async signOut(): Promise<void> {
    try {
      await signOut(auth);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to sign out');
    }
  }

  // Get current user
  static getCurrentUser(): FirebaseUser | null {
    return auth.currentUser;
  }

  // Listen to auth state changes
  static onAuthStateChange(callback: (user: FirebaseUser | null) => void) {
    return onAuthStateChanged(auth, callback);
  }

  // Get user profile from Firestore
  static async getUserProfile(uid: string): Promise<User | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', uid));
      if (userDoc.exists()) {
        return userDoc.data() as User;
      }
      return null;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  }

  // Get business profile from Firestore
  static async getBusinessProfile(businessId: string): Promise<Business | null> {
    try {
      const businessDoc = await getDoc(doc(db, 'businesses', businessId));
      if (businessDoc.exists()) {
        return businessDoc.data() as Business;
      }
      return null;
    } catch (error) {
      console.error('Error fetching business profile:', error);
      return null;
    }
  }

  // Update user profile
  static async updateUserProfile(uid: string, updates: Partial<User>): Promise<void> {
    try {
      await updateDoc(doc(db, 'users', uid), {
        ...updates,
        updatedAt: new Date()
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update profile');
    }
  }

  // Update business profile
  static async updateBusinessProfile(businessId: string, updates: Partial<Business>): Promise<void> {
    try {
      await updateDoc(doc(db, 'businesses', businessId), {
        ...updates,
        updatedAt: new Date()
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update business profile');
    }
  }
}
