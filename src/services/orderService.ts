import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  getDocs, 
  getDoc,
  query, 
  where, 
  orderBy,
  limit,
  Timestamp,
  runTransaction
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Order, OrderItem, Payment, Customer, Shift } from '../types';
import { ProductService } from './productService';

export class OrderService {
  // Generate order number
  private static generateOrderNumber(): string {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `ORD-${timestamp}-${random}`;
  }

  // Create new order
  static async createOrder(orderData: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const orderNumber = this.generateOrderNumber();
      
      const docRef = await addDoc(collection(db, 'orders'), {
        ...orderData,
        orderNumber,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      
      return docRef.id;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to create order');
    }
  }

  // Get order by ID
  static async getOrder(orderId: string): Promise<Order | null> {
    try {
      const docRef = doc(db, 'orders', orderId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data(),
          createdAt: docSnap.data().createdAt?.toDate(),
          updatedAt: docSnap.data().updatedAt?.toDate()
        } as Order;
      }
      return null;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch order');
    }
  }

  // Get orders for a business
  static async getOrders(businessId: string, limitCount: number = 50): Promise<Order[]> {
    try {
      const q = query(
        collection(db, 'orders'),
        where('businessId', '==', businessId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      })) as Order[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch orders');
    }
  }

  // Get orders by status
  static async getOrdersByStatus(businessId: string, status: Order['status']): Promise<Order[]> {
    try {
      const q = query(
        collection(db, 'orders'),
        where('businessId', '==', businessId),
        where('status', '==', status),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      })) as Order[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch orders by status');
    }
  }

  // Update order
  static async updateOrder(orderId: string, updates: Partial<Order>): Promise<void> {
    try {
      const docRef = doc(db, 'orders', orderId);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update order');
    }
  }

  // Add item to order
  static async addItemToOrder(orderId: string, item: OrderItem): Promise<void> {
    try {
      const order = await this.getOrder(orderId);
      if (!order) {
        throw new Error('Order not found');
      }

      const updatedItems = [...order.items, item];
      const subtotal = updatedItems.reduce((sum, item) => sum + item.subtotal, 0);
      const tax = subtotal * 0.08; // TODO: Get tax rate from business settings
      const total = subtotal + tax + order.tip - order.discount;

      await this.updateOrder(orderId, {
        items: updatedItems,
        subtotal,
        tax,
        total
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to add item to order');
    }
  }

  // Remove item from order
  static async removeItemFromOrder(orderId: string, itemId: string): Promise<void> {
    try {
      const order = await this.getOrder(orderId);
      if (!order) {
        throw new Error('Order not found');
      }

      const updatedItems = order.items.filter(item => item.id !== itemId);
      const subtotal = updatedItems.reduce((sum, item) => sum + item.subtotal, 0);
      const tax = subtotal * 0.08; // TODO: Get tax rate from business settings
      const total = subtotal + tax + order.tip - order.discount;

      await this.updateOrder(orderId, {
        items: updatedItems,
        subtotal,
        tax,
        total
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to remove item from order');
    }
  }

  // Process payment and complete order
  static async processPayment(orderId: string, payment: Omit<Payment, 'id' | 'orderId' | 'processedAt'>): Promise<void> {
    try {
      await runTransaction(db, async (transaction) => {
        const orderRef = doc(db, 'orders', orderId);
        const orderDoc = await transaction.get(orderRef);
        
        if (!orderDoc.exists()) {
          throw new Error('Order not found');
        }
        
        const order = orderDoc.data() as Order;
        
        // Add payment to order
        const newPayment: Payment = {
          ...payment,
          id: doc(collection(db, 'payments')).id,
          orderId,
          processedAt: new Date()
        };
        
        const updatedPayments = [...order.payments, newPayment];
        const totalPaid = updatedPayments
          .filter(p => p.status === 'completed')
          .reduce((sum, p) => sum + p.amount, 0);
        
        // Update order status if fully paid
        const orderStatus = totalPaid >= order.total ? 'paid' : 'open';
        
        // Update order
        transaction.update(orderRef, {
          payments: updatedPayments,
          status: orderStatus,
          updatedAt: Timestamp.now()
        });
        
        // Update inventory for sold items
        if (orderStatus === 'paid') {
          for (const item of order.items) {
            await ProductService.updateInventory(
              item.productId,
              item.quantity,
              'sale',
              order.cashierId
            );
          }
        }
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to process payment');
    }
  }

  // Void order
  static async voidOrder(orderId: string, reason: string): Promise<void> {
    try {
      await this.updateOrder(orderId, {
        status: 'voided',
        notes: reason
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to void order');
    }
  }

  // Refund order
  static async refundOrder(orderId: string, amount: number, reason: string): Promise<void> {
    try {
      const order = await this.getOrder(orderId);
      if (!order) {
        throw new Error('Order not found');
      }

      // Create refund payment record
      const refundPayment: Payment = {
        id: doc(collection(db, 'payments')).id,
        orderId,
        method: { id: 'refund', type: 'other', name: 'Refund', isActive: true },
        amount: -amount,
        status: 'completed',
        reference: reason,
        processedAt: new Date()
      };

      const updatedPayments = [...order.payments, refundPayment];
      
      await this.updateOrder(orderId, {
        payments: updatedPayments,
        status: 'refunded'
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to refund order');
    }
  }

  // Get today's sales summary
  static async getTodaysSales(businessId: string): Promise<{
    totalSales: number;
    totalOrders: number;
    averageOrderValue: number;
  }> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const q = query(
        collection(db, 'orders'),
        where('businessId', '==', businessId),
        where('status', '==', 'paid'),
        where('createdAt', '>=', Timestamp.fromDate(today))
      );
      
      const querySnapshot = await getDocs(q);
      const orders = querySnapshot.docs.map(doc => doc.data() as Order);
      
      const totalSales = orders.reduce((sum, order) => sum + order.total, 0);
      const totalOrders = orders.length;
      const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;
      
      return {
        totalSales,
        totalOrders,
        averageOrderValue
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch today\'s sales');
    }
  }
}
