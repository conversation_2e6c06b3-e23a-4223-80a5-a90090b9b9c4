import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc,
  query, 
  where, 
  orderBy,
  limit,
  Timestamp,
  increment
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Customer, Order } from '../types';

export class CustomerService {
  // Get all customers for a business
  static async getCustomers(businessId: string): Promise<Customer[]> {
    try {
      const q = query(
        collection(db, 'customers'),
        where('businessId', '==', businessId),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        lastVisit: doc.data().lastVisit?.toDate()
      })) as Customer[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch customers');
    }
  }

  // Get single customer
  static async getCustomer(customerId: string): Promise<Customer | null> {
    try {
      const docRef = doc(db, 'customers', customerId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data(),
          createdAt: docSnap.data().createdAt?.toDate(),
          lastVisit: docSnap.data().lastVisit?.toDate()
        } as Customer;
      }
      return null;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch customer');
    }
  }

  // Search customers by name, email, or phone
  static async searchCustomers(businessId: string, searchTerm: string): Promise<Customer[]> {
    try {
      const customers = await this.getCustomers(businessId);
      const term = searchTerm.toLowerCase();
      
      return customers.filter(customer =>
        customer.name.toLowerCase().includes(term) ||
        customer.email?.toLowerCase().includes(term) ||
        customer.phone?.includes(term)
      );
    } catch (error: any) {
      throw new Error(error.message || 'Failed to search customers');
    }
  }

  // Create new customer
  static async createCustomer(customerData: Omit<Customer, 'id' | 'createdAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'customers'), {
        ...customerData,
        createdAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to create customer');
    }
  }

  // Update customer
  static async updateCustomer(customerId: string, updates: Partial<Customer>): Promise<void> {
    try {
      const docRef = doc(db, 'customers', customerId);
      await updateDoc(docRef, {
        ...updates,
        ...(updates.lastVisit && { lastVisit: Timestamp.fromDate(updates.lastVisit) })
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update customer');
    }
  }

  // Delete customer
  static async deleteCustomer(customerId: string): Promise<void> {
    try {
      const docRef = doc(db, 'customers', customerId);
      await deleteDoc(docRef);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to delete customer');
    }
  }

  // Update customer stats after order
  static async updateCustomerStats(customerId: string, orderAmount: number): Promise<void> {
    try {
      const docRef = doc(db, 'customers', customerId);
      await updateDoc(docRef, {
        totalSpent: increment(orderAmount),
        visitCount: increment(1),
        lastVisit: Timestamp.now()
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update customer stats');
    }
  }

  // Add loyalty points
  static async addLoyaltyPoints(customerId: string, points: number): Promise<void> {
    try {
      const docRef = doc(db, 'customers', customerId);
      await updateDoc(docRef, {
        loyaltyPoints: increment(points)
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to add loyalty points');
    }
  }

  // Redeem loyalty points
  static async redeemLoyaltyPoints(customerId: string, points: number): Promise<void> {
    try {
      const customer = await this.getCustomer(customerId);
      if (!customer) {
        throw new Error('Customer not found');
      }

      if (customer.loyaltyPoints < points) {
        throw new Error('Insufficient loyalty points');
      }

      const docRef = doc(db, 'customers', customerId);
      await updateDoc(docRef, {
        loyaltyPoints: increment(-points)
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to redeem loyalty points');
    }
  }

  // Get customer order history
  static async getCustomerOrders(customerId: string, limitCount: number = 20): Promise<Order[]> {
    try {
      const q = query(
        collection(db, 'orders'),
        where('customer.id', '==', customerId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      })) as Order[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch customer orders');
    }
  }

  // Get top customers by spending
  static async getTopCustomers(businessId: string, limitCount: number = 10): Promise<Customer[]> {
    try {
      const customers = await this.getCustomers(businessId);
      return customers
        .sort((a, b) => b.totalSpent - a.totalSpent)
        .slice(0, limitCount);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch top customers');
    }
  }

  // Get customers with birthdays this month
  static async getBirthdayCustomers(businessId: string): Promise<Customer[]> {
    try {
      const customers = await this.getCustomers(businessId);
      const currentMonth = new Date().getMonth();
      
      return customers.filter(customer => {
        if (!customer.notes) return false;
        
        // Look for birthday in notes (format: "Birthday: MM/DD" or "DOB: MM/DD/YYYY")
        const birthdayMatch = customer.notes.match(/(?:birthday|dob|born):\s*(\d{1,2})\/(\d{1,2})/i);
        if (birthdayMatch) {
          const birthMonth = parseInt(birthdayMatch[1]) - 1; // Convert to 0-based month
          return birthMonth === currentMonth;
        }
        
        return false;
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch birthday customers');
    }
  }

  // Send SMS receipt (placeholder - would integrate with SMS service)
  static async sendSMSReceipt(customer: Customer, orderNumber: string, total: number): Promise<void> {
    try {
      // This would integrate with Twilio, AWS SNS, or similar SMS service
      console.log(`SMS Receipt sent to ${customer.phone}: Order ${orderNumber} - $${total.toFixed(2)}`);
      
      // For now, just log the action
      // In production, implement actual SMS sending
    } catch (error: any) {
      throw new Error(error.message || 'Failed to send SMS receipt');
    }
  }

  // Send email receipt (placeholder - would integrate with email service)
  static async sendEmailReceipt(customer: Customer, orderNumber: string, total: number, items: any[]): Promise<void> {
    try {
      // This would integrate with SendGrid, AWS SES, or similar email service
      console.log(`Email Receipt sent to ${customer.email}: Order ${orderNumber} - $${total.toFixed(2)}`);
      
      // For now, just log the action
      // In production, implement actual email sending with HTML template
    } catch (error: any) {
      throw new Error(error.message || 'Failed to send email receipt');
    }
  }

  // Calculate loyalty points earned from purchase
  static calculateLoyaltyPoints(orderAmount: number, pointsPerDollar: number = 1): number {
    return Math.floor(orderAmount * pointsPerDollar);
  }

  // Calculate loyalty discount
  static calculateLoyaltyDiscount(points: number, pointValue: number = 0.01): number {
    return points * pointValue;
  }

  // Get customer analytics
  static async getCustomerAnalytics(businessId: string): Promise<{
    totalCustomers: number;
    newCustomersThisMonth: number;
    averageSpendPerCustomer: number;
    topSpender: Customer | null;
    loyaltyPointsIssued: number;
  }> {
    try {
      const customers = await this.getCustomers(businessId);
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      
      const newCustomersThisMonth = customers.filter(customer => {
        const createdDate = customer.createdAt;
        return createdDate.getMonth() === currentMonth && 
               createdDate.getFullYear() === currentYear;
      }).length;
      
      const totalSpent = customers.reduce((sum, customer) => sum + customer.totalSpent, 0);
      const averageSpendPerCustomer = customers.length > 0 ? totalSpent / customers.length : 0;
      
      const topSpender = customers.reduce((top, customer) => 
        !top || customer.totalSpent > top.totalSpent ? customer : top, 
        null as Customer | null
      );
      
      const loyaltyPointsIssued = customers.reduce((sum, customer) => sum + customer.loyaltyPoints, 0);
      
      return {
        totalCustomers: customers.length,
        newCustomersThisMonth,
        averageSpendPerCustomer,
        topSpender,
        loyaltyPointsIssued
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch customer analytics');
    }
  }
}
