import { supabase, supabaseHelpers } from '../config/supabase';
import { User, Business } from '../types';

export class SupabaseAuthService {
  // Sign up a new user
  static async signUp(
    email: string, 
    password: string, 
    name: string, 
    businessData?: Partial<Business>
  ): Promise<{ user: User; business: Business }> {
    try {
      // Create auth user
      const { data: authData, error: authError } = await supabaseHelpers.auth.signUp(email, password);
      
      if (authError) throw authError;
      if (!authData.user) throw new Error('Failed to create user');

      // Create business if provided
      let business: Business;
      if (businessData) {
        const { data: businessResult, error: businessError } = await supabaseHelpers.db.insert('businesses', {
          name: businessData.name,
          email: businessData.email || email,
          phone: businessData.phone,
          address: businessData.address || {},
          tax_rate: businessData.taxRate || 0.08,
          currency: 'USD',
          timezone: 'America/New_York',
          payment_methods: businessData.paymentMethods || [],
          settings: businessData.settings || {},
          is_active: true
        });

        if (businessError) throw businessError;
        business = businessResult[0];
      } else {
        throw new Error('Business data is required');
      }

      // Create user profile
      const { data: profileData, error: profileError } = await supabaseHelpers.db.insert('profiles', {
        id: authData.user.id,
        business_id: business.id,
        email,
        name,
        role: 'owner',
        permissions: [{ action: '*', resource: '*', allowed: true }],
        is_active: true
      });

      if (profileError) throw profileError;

      const user: User = {
        id: authData.user.id,
        businessId: business.id,
        email,
        name,
        role: 'owner',
        permissions: [{ action: '*', resource: '*', allowed: true }],
        isActive: true,
        createdAt: new Date()
      };

      return { user, business };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to sign up');
    }
  }

  // Sign in an existing user
  static async signIn(email: string, password: string): Promise<{ user: User; business: Business }> {
    try {
      const { data: authData, error: authError } = await supabaseHelpers.auth.signIn(email, password);
      
      if (authError) throw authError;
      if (!authData.user) throw new Error('Failed to sign in');

      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', authData.user.id)
        .single();

      if (profileError) throw profileError;

      // Get business data
      const { data: businessData, error: businessError } = await supabase
        .from('businesses')
        .select('*')
        .eq('id', profileData.business_id)
        .single();

      if (businessError) throw businessError;

      // Update last login
      await supabaseHelpers.db.update('profiles', authData.user.id, {
        last_login: new Date()
      });

      const user: User = {
        id: profileData.id,
        businessId: profileData.business_id,
        email: profileData.email,
        name: profileData.name,
        role: profileData.role,
        permissions: profileData.permissions || [],
        isActive: profileData.is_active,
        createdAt: new Date(profileData.created_at),
        lastLogin: new Date()
      };

      const business: Business = {
        id: businessData.id,
        name: businessData.name,
        email: businessData.email,
        phone: businessData.phone,
        address: businessData.address,
        taxRate: parseFloat(businessData.tax_rate),
        currency: businessData.currency,
        timezone: businessData.timezone,
        paymentMethods: businessData.payment_methods || [],
        settings: businessData.settings || {},
        isActive: businessData.is_active,
        createdAt: new Date(businessData.created_at)
      };

      return { user, business };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to sign in');
    }
  }

  // Sign out the current user
  static async signOut(): Promise<void> {
    try {
      const { error } = await supabaseHelpers.auth.signOut();
      if (error) throw error;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to sign out');
    }
  }

  // Get current user session
  static async getCurrentUser(): Promise<{ user: User; business: Business } | null> {
    try {
      const { data: authData } = await supabaseHelpers.auth.getCurrentUser();
      
      if (!authData.user) return null;

      // Get user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', authData.user.id)
        .single();

      if (profileError) return null;

      // Get business data
      const { data: businessData, error: businessError } = await supabase
        .from('businesses')
        .select('*')
        .eq('id', profileData.business_id)
        .single();

      if (businessError) return null;

      const user: User = {
        id: profileData.id,
        businessId: profileData.business_id,
        email: profileData.email,
        name: profileData.name,
        role: profileData.role,
        permissions: profileData.permissions || [],
        isActive: profileData.is_active,
        createdAt: new Date(profileData.created_at),
        lastLogin: profileData.last_login ? new Date(profileData.last_login) : undefined
      };

      const business: Business = {
        id: businessData.id,
        name: businessData.name,
        email: businessData.email,
        phone: businessData.phone,
        address: businessData.address,
        taxRate: parseFloat(businessData.tax_rate),
        currency: businessData.currency,
        timezone: businessData.timezone,
        paymentMethods: businessData.payment_methods || [],
        settings: businessData.settings || {},
        isActive: businessData.is_active,
        createdAt: new Date(businessData.created_at)
      };

      return { user, business };
    } catch (error: any) {
      return null;
    }
  }

  // Reset password
  static async resetPassword(email: string): Promise<void> {
    try {
      const { error } = await supabaseHelpers.auth.resetPassword(email);
      if (error) throw error;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to reset password');
    }
  }

  // Update user profile
  static async updateProfile(userId: string, updates: Partial<User>): Promise<void> {
    try {
      const { error } = await supabaseHelpers.db.update('profiles', userId, {
        name: updates.name,
        role: updates.role,
        permissions: updates.permissions,
        is_active: updates.isActive
      });

      if (error) throw error;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update profile');
    }
  }

  // Update business profile
  static async updateBusinessProfile(businessId: string, updates: Partial<Business>): Promise<void> {
    try {
      const { error } = await supabaseHelpers.db.update('businesses', businessId, {
        name: updates.name,
        email: updates.email,
        phone: updates.phone,
        address: updates.address,
        tax_rate: updates.taxRate,
        payment_methods: updates.paymentMethods,
        settings: updates.settings
      });

      if (error) throw error;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update business profile');
    }
  }

  // Set up auth state change listener
  static onAuthStateChange(callback: (user: User | null, business: Business | null) => void) {
    return supabaseHelpers.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        try {
          const result = await this.getCurrentUser();
          if (result) {
            callback(result.user, result.business);
          } else {
            callback(null, null);
          }
        } catch (error) {
          callback(null, null);
        }
      } else {
        callback(null, null);
      }
    });
  }
}
