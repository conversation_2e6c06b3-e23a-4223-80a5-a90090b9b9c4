import { 
  collection, 
  query, 
  where, 
  orderBy,
  getDocs,
  Timestamp
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Order, Product, Customer, SalesAnalytics, ProductSales, HourlySales, PaymentMethodSales } from '../types';

export class AnalyticsService {
  // Get sales analytics for a specific period
  static async getSalesAnalytics(
    businessId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<SalesAnalytics> {
    try {
      const q = query(
        collection(db, 'orders'),
        where('businessId', '==', businessId),
        where('status', '==', 'paid'),
        where('createdAt', '>=', Timestamp.fromDate(startDate)),
        where('createdAt', '<=', Timestamp.fromDate(endDate)),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const orders = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      })) as Order[];

      const totalSales = orders.reduce((sum, order) => sum + order.total, 0);
      const totalOrders = orders.length;
      const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

      // Calculate top products
      const productSales = new Map<string, ProductSales>();
      orders.forEach(order => {
        order.items.forEach(item => {
          const existing = productSales.get(item.productId) || {
            productId: item.productId,
            productName: item.productName,
            quantitySold: 0,
            revenue: 0,
            profit: 0
          };
          
          existing.quantitySold += item.quantity;
          existing.revenue += item.subtotal;
          // Profit calculation would need product cost data
          existing.profit += item.subtotal * 0.3; // Assuming 30% margin
          
          productSales.set(item.productId, existing);
        });
      });

      const topProducts = Array.from(productSales.values())
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 10);

      // Calculate hourly sales
      const hourlySales: HourlySales[] = [];
      for (let hour = 0; hour < 24; hour++) {
        const hourOrders = orders.filter(order => order.createdAt.getHours() === hour);
        hourlySales.push({
          hour,
          sales: hourOrders.reduce((sum, order) => sum + order.total, 0),
          orders: hourOrders.length
        });
      }

      // Calculate payment method breakdown
      const paymentMethodSales = new Map<string, number>();
      orders.forEach(order => {
        order.payments.forEach(payment => {
          const existing = paymentMethodSales.get(payment.method.name) || 0;
          paymentMethodSales.set(payment.method.name, existing + payment.amount);
        });
      });

      const paymentMethodBreakdown: PaymentMethodSales[] = Array.from(paymentMethodSales.entries())
        .map(([method, amount]) => ({
          method,
          amount,
          percentage: totalSales > 0 ? (amount / totalSales) * 100 : 0
        }))
        .sort((a, b) => b.amount - a.amount);

      return {
        period: this.getPeriodType(startDate, endDate),
        totalSales,
        totalOrders,
        averageOrderValue,
        topProducts,
        salesByHour: hourlySales,
        paymentMethodBreakdown
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch sales analytics');
    }
  }

  // Get today's sales summary
  static async getTodaysSummary(businessId: string): Promise<{
    sales: number;
    orders: number;
    customers: number;
    averageOrder: number;
    hourlyTrend: { hour: number; sales: number }[];
  }> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const analytics = await this.getSalesAnalytics(businessId, today, tomorrow);
      
      // Get unique customers today
      const q = query(
        collection(db, 'orders'),
        where('businessId', '==', businessId),
        where('createdAt', '>=', Timestamp.fromDate(today)),
        where('createdAt', '<', Timestamp.fromDate(tomorrow))
      );

      const querySnapshot = await getDocs(q);
      const uniqueCustomers = new Set();
      querySnapshot.docs.forEach(doc => {
        const order = doc.data() as Order;
        if (order.customer?.id) {
          uniqueCustomers.add(order.customer.id);
        }
      });

      return {
        sales: analytics.totalSales,
        orders: analytics.totalOrders,
        customers: uniqueCustomers.size,
        averageOrder: analytics.averageOrderValue,
        hourlyTrend: analytics.salesByHour.map(h => ({ hour: h.hour, sales: h.sales }))
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch today\'s summary');
    }
  }

  // Get weekly comparison
  static async getWeeklyComparison(businessId: string): Promise<{
    thisWeek: number;
    lastWeek: number;
    change: number;
    changePercent: number;
  }> {
    try {
      const now = new Date();
      const thisWeekStart = new Date(now);
      thisWeekStart.setDate(now.getDate() - now.getDay());
      thisWeekStart.setHours(0, 0, 0, 0);

      const lastWeekStart = new Date(thisWeekStart);
      lastWeekStart.setDate(lastWeekStart.getDate() - 7);

      const lastWeekEnd = new Date(thisWeekStart);

      const [thisWeekAnalytics, lastWeekAnalytics] = await Promise.all([
        this.getSalesAnalytics(businessId, thisWeekStart, now),
        this.getSalesAnalytics(businessId, lastWeekStart, lastWeekEnd)
      ]);

      const change = thisWeekAnalytics.totalSales - lastWeekAnalytics.totalSales;
      const changePercent = lastWeekAnalytics.totalSales > 0 
        ? (change / lastWeekAnalytics.totalSales) * 100 
        : 0;

      return {
        thisWeek: thisWeekAnalytics.totalSales,
        lastWeek: lastWeekAnalytics.totalSales,
        change,
        changePercent
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch weekly comparison');
    }
  }

  // Get inventory insights
  static async getInventoryInsights(businessId: string): Promise<{
    lowStockItems: number;
    outOfStockItems: number;
    totalProducts: number;
    topSellingProducts: ProductSales[];
  }> {
    try {
      const q = query(
        collection(db, 'products'),
        where('businessId', '==', businessId),
        where('isActive', '==', true)
      );

      const querySnapshot = await getDocs(q);
      const products = querySnapshot.docs.map(doc => doc.data()) as Product[];

      const lowStockItems = products.filter(product =>
        product.inventory.trackInventory &&
        product.inventory.currentStock <= product.inventory.lowStockAlert &&
        product.inventory.currentStock > 0
      ).length;

      const outOfStockItems = products.filter(product =>
        product.inventory.trackInventory &&
        product.inventory.currentStock <= 0
      ).length;

      return {
        lowStockItems,
        outOfStockItems,
        totalProducts: products.length,
        topSellingProducts: [] // Would be populated from sales data
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch inventory insights');
    }
  }

  // Get customer insights
  static async getCustomerInsights(businessId: string): Promise<{
    totalCustomers: number;
    newCustomersThisMonth: number;
    returningCustomers: number;
    averageLifetimeValue: number;
    topCustomers: { name: string; spent: number }[];
  }> {
    try {
      const q = query(
        collection(db, 'customers'),
        where('businessId', '==', businessId)
      );

      const querySnapshot = await getDocs(q);
      const customers = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate()
      })) as Customer[];

      const now = new Date();
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const newCustomersThisMonth = customers.filter(
        customer => customer.createdAt >= thisMonth
      ).length;

      const returningCustomers = customers.filter(
        customer => customer.visitCount > 1
      ).length;

      const totalSpent = customers.reduce((sum, customer) => sum + customer.totalSpent, 0);
      const averageLifetimeValue = customers.length > 0 ? totalSpent / customers.length : 0;

      const topCustomers = customers
        .sort((a, b) => b.totalSpent - a.totalSpent)
        .slice(0, 5)
        .map(customer => ({
          name: customer.name,
          spent: customer.totalSpent
        }));

      return {
        totalCustomers: customers.length,
        newCustomersThisMonth,
        returningCustomers,
        averageLifetimeValue,
        topCustomers
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch customer insights');
    }
  }

  // Get cash drawer reconciliation data
  static async getCashDrawerData(businessId: string, date: Date): Promise<{
    openingBalance: number;
    cashSales: number;
    cashRefunds: number;
    expectedBalance: number;
    actualBalance?: number;
    variance?: number;
  }> {
    try {
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      const q = query(
        collection(db, 'orders'),
        where('businessId', '==', businessId),
        where('createdAt', '>=', Timestamp.fromDate(startOfDay)),
        where('createdAt', '<=', Timestamp.fromDate(endOfDay))
      );

      const querySnapshot = await getDocs(q);
      const orders = querySnapshot.docs.map(doc => doc.data()) as Order[];

      let cashSales = 0;
      let cashRefunds = 0;

      orders.forEach(order => {
        order.payments.forEach(payment => {
          if (payment.method.type === 'cash') {
            if (payment.amount > 0) {
              cashSales += payment.amount;
            } else {
              cashRefunds += Math.abs(payment.amount);
            }
          }
        });
      });

      const openingBalance = 200; // This would come from shift data
      const expectedBalance = openingBalance + cashSales - cashRefunds;

      return {
        openingBalance,
        cashSales,
        cashRefunds,
        expectedBalance
      };
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch cash drawer data');
    }
  }

  // Helper method to determine period type
  private static getPeriodType(startDate: Date, endDate: Date): 'day' | 'week' | 'month' | 'year' {
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays <= 1) return 'day';
    if (diffDays <= 7) return 'week';
    if (diffDays <= 31) return 'month';
    return 'year';
  }

  // Export data to CSV
  static exportToCSV(data: any[], filename: string): void {
    const csvContent = this.convertToCSV(data);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  // Convert data to CSV format
  private static convertToCSV(data: any[]): string {
    if (data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvRows = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          return typeof value === 'string' ? `"${value}"` : value;
        }).join(',')
      )
    ];

    return csvRows.join('\n');
  }
}
