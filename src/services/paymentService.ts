// Note: QRCode import will be available when qrcode package is installed
// import QRCode from 'qrcode';
import { PaymentMethod, Payment } from '../types';

export class PaymentService {
  // Generate CashApp payment link
  static generateCashAppPayment(handle: string, amount: number, note?: string): string {
    const baseUrl = 'https://cash.app';
    const params = new URLSearchParams({
      amount: amount.toString(),
      ...(note && { note })
    });
    
    return `${baseUrl}/${handle}?${params.toString()}`;
  }

  // Generate Zelle payment instructions
  static generateZellePayment(email: string, amount: number): {
    email: string;
    amount: number;
    instructions: string[];
  } {
    return {
      email,
      amount,
      instructions: [
        'Open your banking app or Zelle app',
        `Send $${amount.toFixed(2)} to ${email}`,
        'Include your order number in the memo',
        'Show confirmation to complete your order'
      ]
    };
  }

  // Generate Venmo payment link
  static generateVenmoPayment(handle: string, amount: number, note?: string): string {
    const baseUrl = 'https://venmo.com';
    const params = new URLSearchParams({
      txn: 'pay',
      recipients: handle,
      amount: amount.toString(),
      ...(note && { note })
    });
    
    return `${baseUrl}/?${params.toString()}`;
  }

  // Generate QR code for payment
  static async generatePaymentQR(paymentUrl: string): Promise<string> {
    try {
      // TODO: Implement QR code generation when qrcode package is available
      // const qrCodeDataUrl = await QRCode.toDataURL(paymentUrl, {
      //   width: 256,
      //   margin: 2,
      //   color: {
      //     dark: '#000000',
      //     light: '#FFFFFF'
      //   }
      // });
      // return qrCodeDataUrl;

      // For now, return a placeholder
      return `data:image/svg+xml;base64,${btoa(`
        <svg width="256" height="256" xmlns="http://www.w3.org/2000/svg">
          <rect width="256" height="256" fill="white"/>
          <text x="128" y="128" text-anchor="middle" font-family="Arial" font-size="16" fill="black">QR Code</text>
          <text x="128" y="150" text-anchor="middle" font-family="Arial" font-size="12" fill="gray">Scan to Pay</text>
        </svg>
      `)}`;
    } catch (error) {
      console.error('Error generating QR code:', error);
      throw new Error('Failed to generate QR code');
    }
  }

  // Process manual payment confirmation
  static async processManualPayment(
    orderId: string,
    method: PaymentMethod,
    amount: number,
    reference?: string
  ): Promise<Payment> {
    const payment: Payment = {
      id: `pay_${Date.now()}`,
      orderId,
      method,
      amount,
      status: 'completed',
      transactionId: reference || `manual_${Date.now()}`,
      reference,
      processedAt: new Date(),
      metadata: {
        processedManually: true,
        timestamp: new Date().toISOString()
      }
    };

    return payment;
  }

  // Validate payment method configuration
  static validatePaymentMethod(method: PaymentMethod): boolean {
    switch (method.type) {
      case 'cashapp':
        return !!(method.config?.handle);
      case 'zelle':
        return !!(method.config?.email);
      case 'venmo':
        return !!(method.config?.handle);
      case 'cash':
        return true;
      case 'card':
        return !!(method.config?.apiKey);
      default:
        return false;
    }
  }

  // Get payment method display info
  static getPaymentMethodInfo(method: PaymentMethod): {
    name: string;
    icon: string;
    description: string;
    requiresManualConfirmation: boolean;
  } {
    switch (method.type) {
      case 'cash':
        return {
          name: 'Cash',
          icon: '💵',
          description: 'Cash payment',
          requiresManualConfirmation: false
        };
      case 'cashapp':
        return {
          name: 'CashApp',
          icon: '💚',
          description: `Send to ${method.config?.handle || 'CashApp'}`,
          requiresManualConfirmation: true
        };
      case 'zelle':
        return {
          name: 'Zelle',
          icon: '🏦',
          description: `Send to ${method.config?.email || 'Zelle'}`,
          requiresManualConfirmation: true
        };
      case 'venmo':
        return {
          name: 'Venmo',
          icon: '💙',
          description: `Send to ${method.config?.handle || 'Venmo'}`,
          requiresManualConfirmation: true
        };
      case 'qr':
        return {
          name: 'QR Payment',
          icon: '📱',
          description: 'Scan QR code to pay',
          requiresManualConfirmation: true
        };
      case 'card':
        return {
          name: 'Credit/Debit Card',
          icon: '💳',
          description: 'Card payment',
          requiresManualConfirmation: false
        };
      default:
        return {
          name: method.name,
          icon: '💰',
          description: 'Payment method',
          requiresManualConfirmation: true
        };
    }
  }

  // Generate payment instructions for customer
  static generatePaymentInstructions(
    method: PaymentMethod,
    amount: number,
    orderNumber: string
  ): {
    title: string;
    instructions: string[];
    paymentUrl?: string;
    qrCode?: Promise<string>;
  } {
    const info = this.getPaymentMethodInfo(method);
    
    switch (method.type) {
      case 'cashapp':
        const cashAppUrl = this.generateCashAppPayment(
          method.config?.handle || '',
          amount,
          `Order ${orderNumber}`
        );
        return {
          title: `Pay with ${info.name}`,
          instructions: [
            'Click the link below or scan the QR code',
            `Send $${amount.toFixed(2)} to ${method.config?.handle}`,
            `Include "Order ${orderNumber}" in the note`,
            'Show confirmation to staff when complete'
          ],
          paymentUrl: cashAppUrl,
          qrCode: this.generatePaymentQR(cashAppUrl)
        };

      case 'zelle':
        const zelleInfo = this.generateZellePayment(method.config?.email || '', amount);
        return {
          title: `Pay with ${info.name}`,
          instructions: [
            ...zelleInfo.instructions,
            `Reference: Order ${orderNumber}`
          ]
        };

      case 'venmo':
        const venmoUrl = this.generateVenmoPayment(
          method.config?.handle || '',
          amount,
          `Order ${orderNumber}`
        );
        return {
          title: `Pay with ${info.name}`,
          instructions: [
            'Click the link below or scan the QR code',
            `Send $${amount.toFixed(2)} to ${method.config?.handle}`,
            `Include "Order ${orderNumber}" in the note`,
            'Show confirmation to staff when complete'
          ],
          paymentUrl: venmoUrl,
          qrCode: this.generatePaymentQR(venmoUrl)
        };

      case 'cash':
        return {
          title: 'Cash Payment',
          instructions: [
            `Total amount: $${amount.toFixed(2)}`,
            'Please provide exact change or staff will provide change',
            'Receipt will be provided upon payment'
          ]
        };

      default:
        return {
          title: `Pay with ${info.name}`,
          instructions: [
            `Amount: $${amount.toFixed(2)}`,
            `Order: ${orderNumber}`,
            'Follow the payment method instructions',
            'Show confirmation to staff when complete'
          ]
        };
    }
  }

  // Split payment calculation
  static calculateSplitPayment(
    totalAmount: number,
    splitCount: number
  ): { amountPerPerson: number; remainder: number } {
    const amountPerPerson = Math.floor((totalAmount * 100) / splitCount) / 100;
    const remainder = Math.round((totalAmount - (amountPerPerson * splitCount)) * 100) / 100;
    
    return { amountPerPerson, remainder };
  }

  // Stripe integration (optional fallback)
  static async processStripePayment(
    amount: number,
    currency: string = 'usd',
    paymentMethodId: string
  ): Promise<Payment> {
    // This would integrate with Stripe API
    // For now, return a mock payment
    return {
      id: `stripe_${Date.now()}`,
      orderId: '',
      method: {
        id: 'stripe',
        type: 'card',
        name: 'Credit Card',
        isActive: true
      },
      amount,
      status: 'completed',
      transactionId: `pi_${Date.now()}`,
      processedAt: new Date(),
      metadata: {
        paymentMethodId,
        currency
      }
    };
  }

  // PayPal integration (optional fallback)
  static async processPayPalPayment(
    amount: number,
    currency: string = 'USD'
  ): Promise<Payment> {
    // This would integrate with PayPal API
    // For now, return a mock payment
    return {
      id: `paypal_${Date.now()}`,
      orderId: '',
      method: {
        id: 'paypal',
        type: 'other',
        name: 'PayPal',
        isActive: true
      },
      amount,
      status: 'completed',
      transactionId: `PAYID-${Date.now()}`,
      processedAt: new Date(),
      metadata: {
        currency
      }
    };
  }
}
