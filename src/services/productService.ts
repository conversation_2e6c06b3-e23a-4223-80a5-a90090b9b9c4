import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc,
  query, 
  where, 
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from '../config/firebase';
import { Product, Category, InventoryTransaction } from '../types';

export class ProductService {
  // Get all products for a business
  static async getProducts(businessId: string): Promise<Product[]> {
    try {
      const q = query(
        collection(db, 'products'),
        where('businessId', '==', businessId),
        where('isActive', '==', true),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      })) as Product[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch products');
    }
  }

  // Get products by category
  static async getProductsByCategory(businessId: string, categoryId: string): Promise<Product[]> {
    try {
      const q = query(
        collection(db, 'products'),
        where('businessId', '==', businessId),
        where('category.id', '==', categoryId),
        where('isActive', '==', true),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate()
      })) as Product[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch products by category');
    }
  }

  // Get single product
  static async getProduct(productId: string): Promise<Product | null> {
    try {
      const docRef = doc(db, 'products', productId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data(),
          createdAt: docSnap.data().createdAt?.toDate(),
          updatedAt: docSnap.data().updatedAt?.toDate()
        } as Product;
      }
      return null;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch product');
    }
  }

  // Create new product
  static async createProduct(productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'products'), {
        ...productData,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });
      return docRef.id;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to create product');
    }
  }

  // Update product
  static async updateProduct(productId: string, updates: Partial<Product>): Promise<void> {
    try {
      const docRef = doc(db, 'products', productId);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update product');
    }
  }

  // Delete product (soft delete)
  static async deleteProduct(productId: string): Promise<void> {
    try {
      const docRef = doc(db, 'products', productId);
      await updateDoc(docRef, {
        isActive: false,
        updatedAt: Timestamp.now()
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to delete product');
    }
  }

  // Upload product image
  static async uploadProductImage(productId: string, imageFile: File): Promise<string> {
    try {
      const imageRef = ref(storage, `products/${productId}/${imageFile.name}`);
      const snapshot = await uploadBytes(imageRef, imageFile);
      const downloadURL = await getDownloadURL(snapshot.ref);
      
      // Update product with image URL
      await this.updateProduct(productId, { image: downloadURL });
      
      return downloadURL;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to upload image');
    }
  }

  // Delete product image
  static async deleteProductImage(productId: string, imageUrl: string): Promise<void> {
    try {
      const imageRef = ref(storage, imageUrl);
      await deleteObject(imageRef);
      
      // Remove image URL from product
      await this.updateProduct(productId, { image: undefined });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to delete image');
    }
  }

  // Update inventory
  static async updateInventory(
    productId: string, 
    quantity: number, 
    type: InventoryTransaction['type'],
    userId: string,
    reason?: string
  ): Promise<void> {
    try {
      // Get current product
      const product = await this.getProduct(productId);
      if (!product) {
        throw new Error('Product not found');
      }

      // Calculate new stock level
      let newStock = product.inventory.currentStock;
      if (type === 'sale' || type === 'waste') {
        newStock -= quantity;
      } else if (type === 'restock' || type === 'adjustment') {
        newStock += quantity;
      }

      // Update product inventory
      await this.updateProduct(productId, {
        inventory: {
          ...product.inventory,
          currentStock: Math.max(0, newStock)
        }
      });

      // Create inventory transaction record
      await addDoc(collection(db, 'inventoryTransactions'), {
        productId,
        type,
        quantity,
        reason,
        userId,
        createdAt: Timestamp.now()
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update inventory');
    }
  }

  // Get low stock products
  static async getLowStockProducts(businessId: string): Promise<Product[]> {
    try {
      const products = await this.getProducts(businessId);
      return products.filter(product => 
        product.inventory.trackInventory && 
        product.inventory.currentStock <= product.inventory.lowStockAlert
      );
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch low stock products');
    }
  }

  // Get categories for a business
  static async getCategories(businessId: string): Promise<Category[]> {
    try {
      const q = query(
        collection(db, 'categories'),
        where('businessId', '==', businessId),
        orderBy('sortOrder')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Category[];
    } catch (error: any) {
      throw new Error(error.message || 'Failed to fetch categories');
    }
  }

  // Create category
  static async createCategory(businessId: string, categoryData: Omit<Category, 'id'>): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, 'categories'), {
        ...categoryData,
        businessId
      });
      return docRef.id;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to create category');
    }
  }

  // Update category
  static async updateCategory(categoryId: string, updates: Partial<Category>): Promise<void> {
    try {
      const docRef = doc(db, 'categories', categoryId);
      await updateDoc(docRef, updates);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update category');
    }
  }

  // Delete category
  static async deleteCategory(categoryId: string): Promise<void> {
    try {
      const docRef = doc(db, 'categories', categoryId);
      await deleteDoc(docRef);
    } catch (error: any) {
      throw new Error(error.message || 'Failed to delete category');
    }
  }
}
