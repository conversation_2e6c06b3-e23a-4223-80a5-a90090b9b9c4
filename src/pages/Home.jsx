import React from 'react';
import Hero from '../components/Hero';
import Button from '../components/Button';
import ServiceCard from '../components/ServiceCard';

export default function Home() {
  const services = [
    {
      id: 1,
      title: "Luxury Manicure",
      price: 35,
      description: "Premium nail care with Dominican-inspired designs and high-quality products.",
      image: "/images/manicure.jpg"
    },
    {
      id: 2,
      title: "Deluxe Pedicure",
      price: 45,
      description: "Rejuvenating foot treatment with exfoliation, massage, and perfect polish.",
      image: "/images/pedicure.jpg"
    },
    {
      id: 3,
      title: "Nail Art Design",
      price: 25,
      description: "Custom nail art featuring Dominican flag colors and cultural patterns.",
      image: "/images/nail-art.jpg"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <Hero />
      
      {/* About Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:flex lg:items-center lg:gap-12">
            <div className="lg:w-1/2">
              <h2 className="text-3xl font-bold text-dominican-blue mb-6">
                Dominican-Inspired Nail Excellence
              </h2>
              <p className="text-gray-600 mb-6">
                Our nail salon brings the vibrant spirit and artistry of Dominican culture to every service. 
                We combine traditional techniques with modern trends to create stunning nail designs that stand out.
              </p>
              <p className="text-gray-600 mb-8">
                Using only premium products and maintaining the highest standards of cleanliness, 
                we ensure your nail care experience is both luxurious and safe.
              </p>
              <Button className="mb-6 lg:mb-0">Learn More About Us</Button>
            </div>
            <div className="lg:w-1/2 mt-8 lg:mt-0">
              <div className="relative h-96 rounded-lg overflow-hidden shadow-skeu">
                <img 
                  src="/images/salon-interior.jpg" 
                  alt="Salon Interior" 
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-dominican-blue/60 to-transparent"></div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Services Section */}
      <section className="py-16 bg-gradient-to-br from-dominican-blue/5 to-dominican-red/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-dominican-blue mb-4">Our Premium Services</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Discover our range of nail care services designed to pamper and perfect your hands and feet.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map(service => (
              <ServiceCard key={service.id} {...service} />
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Button>View All Services</Button>
          </div>
        </div>
      </section>
      
      {/* Testimonials Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-dominican-blue mb-4">What Our Clients Say</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Read testimonials from our satisfied customers who love our Dominican-inspired nail designs.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Testimonial Card 1 */}
            <div className="bg-white p-6 rounded-lg shadow-skeu hover:shadow-skeu-hover transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 rounded-full bg-dominican-red flex items-center justify-center text-white font-bold text-xl">
                  M
                </div>
                <div className="ml-4">
                  <h4 className="font-bold">Maria Rodriguez</h4>
                  <div className="flex text-yellow-400">
                    <span>★</span><span>★</span><span>★</span><span>★</span><span>★</span>
                  </div>
                </div>
              </div>
              <p className="text-gray-600">
                "The Dominican-inspired designs are absolutely gorgeous! I always get compliments on my nails after visiting this salon."
              </p>
            </div>
            
            {/* Testimonial Card 2 */}
            <div className="bg-white p-6 rounded-lg shadow-skeu hover:shadow-skeu-hover transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 rounded-full bg-dominican-blue flex items-center justify-center text-white font-bold text-xl">
                  J
                </div>
                <div className="ml-4">
                  <h4 className="font-bold">Jessica Thompson</h4>
                  <div className="flex text-yellow-400">
                    <span>★</span><span>★</span><span>★</span><span>★</span><span>★</span>
                  </div>
                </div>
              </div>
              <p className="text-gray-600">
                "The attention to detail is amazing. My nails have never looked better, and the skeuomorphic designs are so unique!"
              </p>
            </div>
            
            {/* Testimonial Card 3 */}
            <div className="bg-white p-6 rounded-lg shadow-skeu hover:shadow-skeu-hover transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 rounded-full bg-dominican-red flex items-center justify-center text-white font-bold text-xl">
                  L
                </div>
                <div className="ml-4">
                  <h4 className="font-bold">Lisa Morales</h4>
                  <div className="flex text-yellow-400">
                    <span>★</span><span>★</span><span>★</span><span>★</span><span>★</span>
                  </div>
                </div>
              </div>
              <p className="text-gray-600">
                "Not only are the designs beautiful, but the staff is so friendly and professional. Best nail salon in town!"
              </p>
            </div>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-16 bg-dominican-blue">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">Ready for Beautiful Nails?</h2>
          <p className="text-white/80 max-w-2xl mx-auto mb-8">
            Book your appointment today and experience the finest Dominican-inspired nail care services.
          </p>
          <button className="bg-white text-dominican-blue font-bold py-3 px-8 rounded-lg shadow-skeu 
                           hover:bg-dominican-red hover:text-white transition-all duration-300 
                           hover:shadow-skeu-hover transform hover:-translate-y-1">
            Book Your Appointment
          </button>
        </div>
      </section>
    </div>
  );
}