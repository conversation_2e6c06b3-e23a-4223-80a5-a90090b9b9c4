<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crispy Crown - Product & Inventory Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fef7ed',
                            100: '#fdedd3',
                            200: '#fbd6a5',
                            300: '#f8b86d',
                            400: '#f59332',
                            500: '#f37316',
                            600: '#e4570b',
                            700: '#bd420b',
                            800: '#973510',
                            900: '#7c2d12',
                        },
                        neu: {
                            base: '#e0e5ec',
                            dark: '#a3b1c6',
                            light: '#ffffff',
                        }
                    },
                    boxShadow: {
                        'neu-outset': '2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff',
                        'neu-inset': 'inset 2px 2px 5px #a3b1c6, inset -2px -2px 5px #ffffff',
                        'neu-pressed': 'inset 1px 1px 2px #a3b1c6, inset -1px -1px 2px #ffffff',
                    }
                }
            }
        }
    </script>
    <style>
        .neu-card {
            background: #e0e5ec;
            box-shadow: 2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff;
            transition: all 0.2s ease;
        }
        .neu-card:hover {
            box-shadow: 2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff;
        }
        .neu-button {
            background: #e0e5ec;
            box-shadow: 2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .neu-button:active {
            box-shadow: inset 2px 2px 5px #a3b1c6, inset -2px -2px 5px #ffffff;
        }
        .hidden { display: none; }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between px-6 py-4">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-sm">🍗</span>
                </div>
                <div>
                    <h1 class="text-xl font-bold text-gray-900">Crispy Crown</h1>
                    <p class="text-xs text-gray-500">Louisiana Style Chicken</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>Shift Active</span>
                </div>
                <div class="relative">
                    <button class="neu-button px-4 py-2 rounded-lg text-gray-700 font-medium">
                        🛒 Cart (<span id="cartCount">0</span>)
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-sm h-screen">
            <nav class="p-4 space-y-2">
                <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                    📊 Dashboard
                </a>
                <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                    🛒 Order Entry
                </a>
                <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-primary-100 text-primary-700">
                    📦 Menu Items
                </a>
                <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                    👥 Customers
                </a>
                <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                    📈 Sales Reports
                </a>
                <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                    ⚙️ Settings
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6 overflow-y-auto">
            <!-- Product Management Header -->
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Menu Items & Inventory</h1>
                    <p class="text-gray-600">Manage your chicken restaurant menu and track inventory</p>
                </div>
                <div class="flex space-x-3">
                    <button class="neu-button px-4 py-2 rounded-lg text-gray-700 font-medium">
                        📋 Categories
                    </button>
                    <button class="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700">
                        + Add Menu Item
                    </button>
                </div>
            </div>

            <!-- Inventory Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="neu-card p-6 rounded-lg">
                    <div class="flex items-center">
                        <div class="p-3 rounded-lg bg-blue-100 mr-4">
                            <span class="text-blue-600 text-xl">📦</span>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Total Items</p>
                            <p class="text-2xl font-bold text-gray-900">12</p>
                        </div>
                    </div>
                </div>

                <div class="neu-card p-6 rounded-lg">
                    <div class="flex items-center">
                        <div class="p-3 rounded-lg bg-yellow-100 mr-4">
                            <span class="text-yellow-600 text-xl">⚠️</span>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Low Stock</p>
                            <p class="text-2xl font-bold text-gray-900">3</p>
                        </div>
                    </div>
                </div>

                <div class="neu-card p-6 rounded-lg">
                    <div class="flex items-center">
                        <div class="p-3 rounded-lg bg-red-100 mr-4">
                            <span class="text-red-600 text-xl">📉</span>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Out of Stock</p>
                            <p class="text-2xl font-bold text-gray-900">1</p>
                        </div>
                    </div>
                </div>

                <div class="neu-card p-6 rounded-lg">
                    <div class="flex items-center">
                        <div class="p-3 rounded-lg bg-green-100 mr-4">
                            <span class="text-green-600 text-xl">📈</span>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Inventory Value</p>
                            <p class="text-2xl font-bold text-gray-900">$1,247.50</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Low Stock Alert -->
            <div class="neu-card rounded-lg mb-6 border-l-4 border-l-yellow-500">
                <div class="p-6">
                    <h3 class="flex items-center text-lg font-semibold text-yellow-700 mb-4">
                        <span class="mr-2">⚠️</span>
                        Low Stock Alert (3 items)
                    </h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-4 rounded-lg border-2 bg-yellow-50 border-yellow-200">
                            <div class="flex items-center space-x-4">
                                <div class="h-12 w-12 bg-white rounded-lg flex items-center justify-center text-xl shadow-sm">
                                    🍗
                                </div>
                                <div>
                                    <h4 class="font-medium text-yellow-900">Chicken Wings (6pc)</h4>
                                    <div class="flex items-center space-x-4 text-sm">
                                        <span class="font-medium text-yellow-700">
                                            Current: 6 servings
                                        </span>
                                        <span class="text-gray-600">
                                            Alert at: 10 servings
                                        </span>
                                        <span class="text-gray-500 font-mono">
                                            SKU: CW6001
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                <div class="px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Low Stock
                                </div>
                                <button class="bg-primary-600 text-white px-3 py-1 rounded-lg text-sm font-medium hover:bg-primary-700">
                                    Restock
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center justify-between p-4 rounded-lg border-2 bg-red-50 border-red-200">
                            <div class="flex items-center space-x-4">
                                <div class="h-12 w-12 bg-white rounded-lg flex items-center justify-center text-xl shadow-sm">
                                    🧀
                                </div>
                                <div>
                                    <h4 class="font-medium text-red-900">Mac & Cheese</h4>
                                    <div class="flex items-center space-x-4 text-sm">
                                        <span class="font-medium text-red-700">
                                            Current: 0 servings
                                        </span>
                                        <span class="text-gray-600">
                                            Alert at: 8 servings
                                        </span>
                                        <span class="text-gray-500 font-mono">
                                            SKU: MC001
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                <div class="px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Out of Stock
                                </div>
                                <button class="bg-primary-600 text-white px-3 py-1 rounded-lg text-sm font-medium hover:bg-primary-700">
                                    Restock
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="neu-card rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                        <div class="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
                            <!-- Search -->
                            <div class="relative">
                                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</span>
                                <input
                                    type="text"
                                    placeholder="Search menu items..."
                                    class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 w-64"
                                />
                            </div>

                            <!-- Category Filter -->
                            <select class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option value="all">All Categories</option>
                                <option value="chicken-main">🍗 Chicken Entrees</option>
                                <option value="sides">🍟 Sides</option>
                                <option value="beverages">🥤 Beverages</option>
                                <option value="desserts">🍰 Desserts</option>
                                <option value="family-meals">👨‍👩‍👧‍👦 Family Meals</option>
                            </select>

                            <!-- Sort -->
                            <select class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option value="name-asc">Name A-Z</option>
                                <option value="name-desc">Name Z-A</option>
                                <option value="price-asc">Price Low-High</option>
                                <option value="price-desc">Price High-Low</option>
                                <option value="stock-asc">Stock Low-High</option>
                                <option value="stock-desc">Stock High-Low</option>
                            </select>
                        </div>

                        <!-- View Mode Toggle -->
                        <div class="flex items-center space-x-2">
                            <button class="bg-primary-600 text-white p-2 rounded-lg">
                                <span class="text-sm">Grid</span>
                            </button>
                            <button class="neu-button p-2 rounded-lg text-gray-700">
                                <span class="text-sm">List</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <!-- Product Card 1 -->
                <div class="neu-card rounded-lg overflow-hidden">
                    <div class="h-48 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center relative">
                        <div class="text-6xl">🍗</div>
                        <div class="absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            In Stock
                        </div>
                        <div class="absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-700">
                            🍗 Chicken Entrees
                        </div>
                    </div>

                    <div class="p-4">
                        <div class="mb-3">
                            <h3 class="font-semibold text-gray-900 mb-1">Spicy Chicken Sandwich</h3>
                            <p class="text-sm text-gray-600">Crispy fried chicken breast with spicy mayo, pickles, and lettuce on a brioche bun</p>
                        </div>

                        <div class="flex items-center justify-between mb-3">
                            <div>
                                <p class="text-lg font-bold text-primary-600">$8.99</p>
                                <p class="text-xs text-gray-500">Cost: $3.50</p>
                            </div>
                            <div class="text-right">
                                <p class="text-xs text-gray-500">SKU</p>
                                <p class="text-sm font-mono">SCS001</p>
                            </div>
                        </div>

                        <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">Inventory</span>
                                <button class="text-xs text-primary-600 hover:text-primary-700">
                                    Adjust
                                </button>
                            </div>
                            
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">Current Stock:</span>
                                <span class="font-medium text-gray-900">
                                    45 pieces
                                </span>
                            </div>
                            
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">Alert Level:</span>
                                <span class="text-gray-900">10</span>
                            </div>

                            <div class="mt-2">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="h-2 rounded-full bg-green-500" style="width: 90%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <p class="text-sm font-medium text-gray-700 mb-2">Modifiers:</p>
                            <div class="flex flex-wrap gap-1">
                                <span class="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full">
                                    Spice Level
                                </span>
                            </div>
                        </div>

                        <div class="flex space-x-2">
                            <button class="neu-button flex-1 px-3 py-2 rounded-lg text-sm">
                                ✏️ Edit
                            </button>
                            <button class="neu-button px-3 py-2 rounded-lg text-sm">
                                📦 Stock
                            </button>
                            <button class="text-red-600 hover:text-red-700 px-2 py-2 rounded-lg text-sm">
                                🗑️
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product Card 2 (Low Stock) -->
                <div class="neu-card rounded-lg overflow-hidden">
                    <div class="h-48 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center relative">
                        <div class="text-6xl">🍗</div>
                        <div class="absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            Low Stock
                        </div>
                        <div class="absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-700">
                            🍗 Chicken Entrees
                        </div>
                    </div>

                    <div class="p-4">
                        <div class="mb-3">
                            <h3 class="font-semibold text-gray-900 mb-1">Chicken Wings (6pc)</h3>
                            <p class="text-sm text-gray-600">Six crispy chicken wings tossed in your choice of sauce</p>
                        </div>

                        <div class="flex items-center justify-between mb-3">
                            <div>
                                <p class="text-lg font-bold text-primary-600">$9.99</p>
                                <p class="text-xs text-gray-500">Cost: $4.20</p>
                            </div>
                            <div class="text-right">
                                <p class="text-xs text-gray-500">SKU</p>
                                <p class="text-sm font-mono">CW6001</p>
                            </div>
                        </div>

                        <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">Inventory</span>
                                <button class="text-xs text-primary-600 hover:text-primary-700">
                                    Adjust
                                </button>
                            </div>
                            
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">Current Stock:</span>
                                <span class="font-medium text-yellow-600">
                                    6 servings
                                </span>
                            </div>
                            
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">Alert Level:</span>
                                <span class="text-gray-900">10</span>
                            </div>

                            <div class="mt-2">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="h-2 rounded-full bg-yellow-500" style="width: 30%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <p class="text-sm font-medium text-gray-700 mb-2">Modifiers:</p>
                            <div class="flex flex-wrap gap-1">
                                <span class="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full">
                                    Wing Sauce
                                </span>
                            </div>
                        </div>

                        <div class="flex space-x-2">
                            <button class="neu-button flex-1 px-3 py-2 rounded-lg text-sm">
                                ✏️ Edit
                            </button>
                            <button class="neu-button px-3 py-2 rounded-lg text-sm">
                                📦 Stock
                            </button>
                            <button class="text-red-600 hover:text-red-700 px-2 py-2 rounded-lg text-sm">
                                🗑️
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Add button press effects
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.neu-button').forEach(button => {
                button.addEventListener('mousedown', () => {
                    button.style.boxShadow = 'inset 2px 2px 5px #a3b1c6, inset -2px -2px 5px #ffffff';
                });
                button.addEventListener('mouseup', () => {
                    button.style.boxShadow = '2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff';
                });
                button.addEventListener('mouseleave', () => {
                    button.style.boxShadow = '2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff';
                });
            });
        });
    </script>
</body>
</html>
