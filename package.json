{"name": "posess", "version": "1.0.0", "description": "POSess - Cloud POS Platform for Small Business Owners - A modern alternative to Clover & Lightspeed", "private": true, "dependencies": {"@react-three/drei": "^9.88.13", "@react-three/fiber": "^8.15.11", "@tanstack/react-query": "^5.8.4", "@supabase/supabase-js": "^2.38.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "uuid": "^9.0.1", "qrcode": "^1.5.3", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/uuid": "^9.0.7", "@types/qrcode": "^1.5.5", "typescript": "^4.9.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}