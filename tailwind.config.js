/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html"
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Brand colors for Crispy Crown Chicken Restaurant
        primary: {
          50: '#fef7ed',
          100: '#fdedd3',
          200: '#fbd6a5',
          300: '#f8b86d',
          400: '#f59332',
          500: '#f37316',
          600: '#e4570b',
          700: '#bd420b',
          800: '#973510',
          900: '#7c2d12',
        },
        // Neumorphic colors
        neu: {
          base: '#e0e5ec',
          dark: '#a3b1c6',
          light: '#ffffff',
        },
        // Dark mode colors
        dark: {
          bg: '#1a1a2e',
          surface: '#16213e',
          card: '#0f3460',
          text: '#e94560',
        },
        // Legacy Dominican colors (keep for existing components)
        dominican: {
          blue: '#002D62',
          red: '#CE1126',
          white: '#FFFFFF',
        }
      },
      boxShadow: {
        // Skeuomorphic shadows
        'skeu': '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
        'skeu-hover': '0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)',
        // Neumorphic shadows
        'neu-inset': 'inset 2px 2px 5px #a3b1c6, inset -2px -2px 5px #ffffff',
        'neu-outset': '2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff',
        'neu-pressed': 'inset 1px 1px 2px #a3b1c6, inset -1px -1px 2px #ffffff',
        // Dark mode neumorphic
        'neu-dark-inset': 'inset 2px 2px 5px #0d1117, inset -2px -2px 5px #262c36',
        'neu-dark-outset': '2px 2px 5px #0d1117, -2px -2px 5px #262c36',
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-subtle': 'bounce 2s infinite',
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}