<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POSess Platform Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        neu: {
                            base: '#e0e5ec',
                            dark: '#a3b1c6',
                            light: '#ffffff',
                        }
                    },
                    boxShadow: {
                        'neu-outset': '2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff',
                        'neu-inset': 'inset 2px 2px 5px #a3b1c6, inset -2px -2px 5px #ffffff',
                        'neu-pressed': 'inset 1px 1px 2px #a3b1c6, inset -1px -1px 2px #ffffff',
                    }
                }
            }
        }
    </script>
    <style>
        .neu-card {
            background: #e0e5ec;
            box-shadow: 2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff;
            transition: all 0.2s ease;
        }
        .neu-card:hover {
            box-shadow: inset 1px 1px 2px #a3b1c6, inset -1px -1px 2px #ffffff;
        }
        .neu-button {
            background: #e0e5ec;
            box-shadow: 2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff;
            transition: all 0.2s ease;
        }
        .neu-button:active {
            box-shadow: inset 2px 2px 5px #a3b1c6, inset -2px -2px 5px #ffffff;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between px-6 py-4">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-sm">P</span>
                </div>
                <div>
                    <h1 class="text-xl font-bold text-gray-900">POSess</h1>
                    <p class="text-xs text-gray-500">Modern Cloud POS</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>Shift Active</span>
                </div>
                <button class="neu-button px-4 py-2 rounded-lg text-gray-700 font-medium">
                    🌙 Dark Mode
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="flex">
        <!-- Sidebar -->
        <aside class="w-64 bg-white shadow-sm h-screen">
            <nav class="p-4 space-y-2">
                <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-primary-100 text-primary-700">
                    📊 Dashboard
                </a>
                <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                    🛒 POS
                </a>
                <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                    📦 Products
                </a>
                <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                    👥 Customers
                </a>
                <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                    📈 Analytics
                </a>
                <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100">
                    ⚙️ Settings
                </a>
            </nav>
        </aside>

        <!-- Main Dashboard -->
        <main class="flex-1 p-6">
            <!-- Welcome Section -->
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Welcome back, John!</h2>
                <p class="text-gray-600">Here's what's happening at your business today.</p>
            </div>

            <!-- Stats Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- Today's Sales -->
                <div class="neu-card p-6 rounded-lg">
                    <div class="flex items-center">
                        <div class="p-3 rounded-lg bg-green-100 mr-4">
                            <span class="text-green-600 text-xl">💵</span>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Today's Sales</p>
                            <p class="text-2xl font-bold text-gray-900">$1,247.50</p>
                            <p class="text-xs text-green-600">↗️ +12.5% from yesterday</p>
                        </div>
                    </div>
                </div>

                <!-- Orders -->
                <div class="neu-card p-6 rounded-lg">
                    <div class="flex items-center">
                        <div class="p-3 rounded-lg bg-blue-100 mr-4">
                            <span class="text-blue-600 text-xl">🛒</span>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Orders</p>
                            <p class="text-2xl font-bold text-gray-900">47</p>
                            <p class="text-xs text-gray-500">Avg. $26.54 per order</p>
                        </div>
                    </div>
                </div>

                <!-- Customers -->
                <div class="neu-card p-6 rounded-lg">
                    <div class="flex items-center">
                        <div class="p-3 rounded-lg bg-purple-100 mr-4">
                            <span class="text-purple-600 text-xl">👥</span>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Customers</p>
                            <p class="text-2xl font-bold text-gray-900">234</p>
                            <p class="text-xs text-gray-500">8 new this month</p>
                        </div>
                    </div>
                </div>

                <!-- Inventory -->
                <div class="neu-card p-6 rounded-lg">
                    <div class="flex items-center">
                        <div class="p-3 rounded-lg bg-orange-100 mr-4">
                            <span class="text-orange-600 text-xl">📦</span>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Inventory</p>
                            <p class="text-2xl font-bold text-gray-900">156</p>
                            <p class="text-xs text-red-500">3 low stock items</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions & Recent Activity -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Quick Actions -->
                <div class="neu-card p-6 rounded-lg">
                    <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <button class="neu-button p-4 rounded-lg flex flex-col items-center space-y-2 bg-primary-600 text-white hover:bg-primary-700">
                            <span class="text-2xl">🛒</span>
                            <div class="text-center">
                                <p class="font-medium">New Sale</p>
                                <p class="text-xs opacity-90">Start transaction</p>
                            </div>
                        </button>
                        <button class="neu-button p-4 rounded-lg flex flex-col items-center space-y-2 bg-green-600 text-white hover:bg-green-700">
                            <span class="text-2xl">📦</span>
                            <div class="text-center">
                                <p class="font-medium">Add Product</p>
                                <p class="text-xs opacity-90">Manage inventory</p>
                            </div>
                        </button>
                        <button class="neu-button p-4 rounded-lg flex flex-col items-center space-y-2 bg-blue-600 text-white hover:bg-blue-700">
                            <span class="text-2xl">👥</span>
                            <div class="text-center">
                                <p class="font-medium">Customers</p>
                                <p class="text-xs opacity-90">Manage database</p>
                            </div>
                        </button>
                        <button class="neu-button p-4 rounded-lg flex flex-col items-center space-y-2 bg-purple-600 text-white hover:bg-purple-700">
                            <span class="text-2xl">📈</span>
                            <div class="text-center">
                                <p class="font-medium">Analytics</p>
                                <p class="text-xs opacity-90">View reports</p>
                            </div>
                        </button>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="neu-card p-6 rounded-lg">
                    <h3 class="text-lg font-semibold mb-4">Recent Orders</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="font-medium">Order #1247</p>
                                <p class="text-sm text-gray-600">Sarah Johnson • 2 items</p>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-green-600">$34.50</p>
                                <p class="text-xs text-gray-500">2 min ago</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="font-medium">Order #1246</p>
                                <p class="text-sm text-gray-600">Mike Chen • 1 item</p>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-green-600">$18.99</p>
                                <p class="text-xs text-gray-500">5 min ago</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="font-medium">Order #1245</p>
                                <p class="text-sm text-gray-600">Emma Davis • 3 items</p>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-green-600">$67.25</p>
                                <p class="text-xs text-gray-500">8 min ago</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Methods Status -->
            <div class="mt-6">
                <div class="neu-card p-6 rounded-lg">
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        💳 Payment Methods
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="p-4 rounded-lg border-2 border-green-200 bg-green-50">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">💵 Cash</span>
                                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span>
                            </div>
                        </div>
                        <div class="p-4 rounded-lg border-2 border-green-200 bg-green-50">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">💚 CashApp</span>
                                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span>
                            </div>
                        </div>
                        <div class="p-4 rounded-lg border-2 border-green-200 bg-green-50">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">🏦 Zelle</span>
                                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span>
                            </div>
                        </div>
                        <div class="p-4 rounded-lg border-2 border-gray-200 bg-gray-50">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">💙 Venmo</span>
                                <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Inactive</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Add some interactivity
        document.querySelectorAll('.neu-button').forEach(button => {
            button.addEventListener('mousedown', () => {
                button.style.boxShadow = 'inset 2px 2px 5px #a3b1c6, inset -2px -2px 5px #ffffff';
            });
            button.addEventListener('mouseup', () => {
                button.style.boxShadow = '2px 2px 5px #a3b1c6, -2px -2px 5px #ffffff';
            });
        });
    </script>
</body>
</html>
