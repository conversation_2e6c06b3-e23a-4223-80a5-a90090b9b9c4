class Client {
  final String id;
  final String name;
  final String? phoneNumber;
  final String? email;
  final Map<String, dynamic> preferences;
  final List<Map<String, dynamic>> interactions;
  final List<Map<String, dynamic>> orders;
  final List<Map<String, dynamic>> payments;
  
  Client({
    required this.id,
    required this.name,
    this.phoneNumber,
    this.email,
    this.preferences = const {},
    this.interactions = const [],
    this.orders = const [],
    this.payments = const [],
  });
  
  Client copyWith({
    String? id,
    String? name,
    String? phoneNumber,
    String? email,
    Map<String, dynamic>? preferences,
    List<Map<String, dynamic>>? interactions,
    List<Map<String, dynamic>>? orders,
    List<Map<String, dynamic>>? payments,
  }) {
    return Client(
      id: id ?? this.id,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      preferences: preferences ?? this.preferences,
      interactions: interactions ?? this.interactions,
      orders: orders ?? this.orders,
      payments: payments ?? this.payments,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
      'preferences': preferences,
      'interactions': interactions,
      'orders': orders,
      'payments': payments,
    };
  }
  
  factory Client.fromJson(Map<String, dynamic> json) {
    return Client(
      id: json['id'],
      name: json['name'],
      phoneNumber: json['phoneNumber'],
      email: json['email'],
      preferences: json['preferences'] ?? {},
      interactions: List<Map<String, dynamic>>.from(json['interactions'] ?? []),
      orders: List<Map<String, dynamic>>.from(json['orders'] ?? []),
      payments: List<Map<String, dynamic>>.from(json['payments'] ?? []),
    );
  }
  
  String getSummary() {
    final List<String> details = [];
    
    details.add('Name: $name');
    if (phoneNumber != null) details.add('Phone: $phoneNumber');
    if (email != null) details.add('Email: $email');
    
    // Add preferences
    if (preferences.isNotEmpty) {
      details.add('\nPreferences:');
      preferences.forEach((key, value) {
        details.add('- $key: $value');
      });
    }
    
    // Add recent orders if any
    if (orders.isNotEmpty) {
      details.add('\nRecent Orders:');
      for (int i = 0; i < (orders.length > 3 ? 3 : orders.length); i++) {
        details.add('- ${orders[i]['date']}: ${orders[i]['description']}');
      }
    }
    
    return details.join('\n');
  }
}