class Drink {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final double price;
  final List<String> ingredients;
  final List<String> tags;
  
  Drink({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.price,
    this.ingredients = const [],
    this.tags = const [],
  });
  
  factory Drink.fromJson(Map<String, dynamic> json) {
    return Drink(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      imageUrl: json['imageUrl'],
      price: json['price'].toDouble(),
      ingredients: List<String>.from(json['ingredients'] ?? []),
      tags: List<String>.from(json['tags'] ?? []),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'price': price,
      'ingredients': ingredients,
      'tags': tags,
    };
  }
}