import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:orby/screens/home_screen.dart';
import 'package:orby/services/ai_service.dart';
import 'package:orby/services/audio_service.dart';
import 'package:orby/services/storage_service.dart';
import 'package:orby/services/instagram_service.dart';
import 'package:orby/services/payment_service.dart';
import 'package:provider/provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load();
  await Firebase.initializeApp();
  
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AIService()),
        Provider(create: (_) => AudioService()),
        Provider(create: (_) => StorageService()),
        Provider(create: (_) => InstagramService()),
        Provider(create: (_) => PaymentService()),
      ],
      child: OrbyApp(),
    ),
  );
}

class OrbyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Orby - Bar Assistant',
      debugShowCheckedModeBanner: false,
      theme: ThemeData.dark().copyWith(
        primaryColor: Color(0xFF6200EA),
        scaffoldBackgroundColor: Colors.black,
        colorScheme: ColorScheme.dark(
          primary: Color(0xFF6200EA),
          secondary: Color(0xFF00E5FF),
          surface: Color(0xFF121212),
          background: Colors.black,
        ),
        textTheme: TextTheme(
          headlineMedium: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
          bodyLarge: TextStyle(color: Colors.white70),
          bodyMedium: TextStyle(color: Colors.white60),
        ),
      ),
      home: HomeScreen(),
    );
  }
}