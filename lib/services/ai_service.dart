import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:orby/models/client.dart';
import 'package:orby/models/drink.dart';
import 'package:orby/services/storage_service.dart';
import 'package:uuid/uuid.dart';

class AIService extends ChangeNotifier {
  final StorageService _storage = StorageService();
  bool _isProcessing = false;
  String _lastResponse = '';
  Client? _currentClient;
  List<Map<String, String>> _conversationHistory = [];
  
  bool get isProcessing => _isProcessing;
  String get lastResponse => _lastResponse;
  Client? get currentClient => _currentClient;
  List<Map<String, String>> get conversationHistory => _conversationHistory;
  
  // Process voice input
  Future<String> processVoiceInput(String transcription) async {
    _isProcessing = true;
    notifyListeners();
    
    try {
      // Add user message to history
      _conversationHistory.add({
        'role': 'user',
        'content': transcription,
      });
      
      // Process with GPT-4
      final String aiResponse = await _processWithGPT(transcription);
      
      // Add assistant response to history
      _conversationHistory.add({
        'role': 'assistant',
        'content': aiResponse,
      });
      
      // Keep conversation history manageable
      if (_conversationHistory.length > 10) {
        _conversationHistory = _conversationHistory.sublist(
          _conversationHistory.length - 10
        );
      }
      
      _lastResponse = aiResponse;
      _isProcessing = false;
      notifyListeners();
      
      // Extract and save client information if possible
      _extractClientInfo(transcription, aiResponse);
      
      return aiResponse;
    } catch (e) {
      _isProcessing = false;
      notifyListeners();
      return "I'm sorry, I encountered an error processing your request. Please try again.";
    }
  }
  
  Future<String> _processWithGPT(String userInput) async {
    final apiKey = dotenv.env['OPENAI_API_KEY'];
    final url = Uri.parse('https://api.openai.com/v1/chat/completions');
    
    // Get client context if available
    String clientContext = '';
    if (_currentClient != null) {
      clientContext = "Client information: ${_currentClient!.getSummary()}";
    }
    
    // Prepare system message with context
    final systemMessage = '''
You are Orby, an AI assistant for a mobile bartending service. Your goal is to help clients book services, 
explore drink options, and process payments. Be friendly, professional, and helpful.

${clientContext}

Available drink menu:
- Classic Margarita ($12): Tequila, lime juice, and triple sec with a salt rim
- Spicy Jalapeño Margarita ($14): Classic margarita with muddled jalapeños for heat
- Old Fashioned ($15): Bourbon, sugar, bitters, and an orange twist
- Mojito ($13): White rum, mint, lime, sugar, and soda water
- Cosmopolitan ($14): Vodka, triple sec, cranberry juice, and lime

Payment options:
- Cash App: \$mobilebartender
- Zelle: <EMAIL>

Booking information:
- Minimum 2 hours booking
- Base rate: $150/hour
- Custom drink packages available
- 50% deposit required to secure booking
''';

    // Prepare conversation history
    List<Map<String, String>> messages = [
      {'role': 'system', 'content': systemMessage},
    ];
    
    // Add recent conversation history
    if (_conversationHistory.isNotEmpty) {
      messages.addAll(_conversationHistory.sublist(
        _conversationHistory.length > 5 ? _conversationHistory.length - 5 : 0
      ));
    }
    
    // Add current user input
    messages.add({'role': 'user', 'content': userInput});
    
    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
      body: jsonEncode({
        'model': 'gpt-4',
        'messages': messages,
        'temperature': 0.7,
        'max_tokens': 500,
      }),
    );
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['choices'][0]['message']['content'];
    } else {
      throw Exception('Failed to get response from OpenAI: ${response.body}');
    }
  }
  
  void _extractClientInfo(String userInput, String aiResponse) async {
    // If no client is set, try to identify the client or create a new one
    if (_currentClient == null) {
      // Check if user introduced themselves
      RegExp nameRegex = RegExp(r"(?:my name is|I'm|I am) ([A-Z][a-z]+(?: [A-Z][a-z]+)?)");
      var nameMatch = nameRegex.firstMatch(userInput);
      
      if (nameMatch != null) {
        String name = nameMatch.group(1)!;
        // Check if client exists
        Client? existingClient = await _storage.findClientByName(name);
        
        if (existingClient != null) {
          _currentClient = existingClient;
        } else {
          // Create new client
          String clientId = Uuid().v4();
          _currentClient = Client(
            id: clientId,
            name: name,
          );
          await _storage.saveClient(_currentClient!);
        }
        notifyListeners();
      }
    } else {
      // Update client preferences based on conversation
      Map<String, dynamic> newPreferences = {};
      
      // Extract drink preferences
      if (userInput.toLowerCase().contains("favorite drink") || 
          userInput.toLowerCase().contains("prefer") || 
          userInput.toLowerCase().contains("like")) {
        
        // Check for drink mentions
        List<String> drinks = [
          "margarita", "old fashioned", "mojito", "cosmopolitan", 
          "martini", "whiskey", "tequila", "vodka", "rum"
        ];
        
        for (String drink in drinks) {
          if (userInput.toLowerCase().contains(drink)) {
            newPreferences["favorite_drink"] = drink;
            break;
          }
        }
      }
      
      // Extract event details
      RegExp dateRegex = RegExp(r"(?:on|for) ([A-Z][a-z]+ \d{1,2}(?:st|nd|rd|th)?)");
      var dateMatch = dateRegex.firstMatch(userInput);
      if (dateMatch != null) {
        newPreferences["event_date"] = dateMatch.group(1);
      }
      
      RegExp guestRegex = RegExp(r"(\d+) (?:people|guests|attendees)");
      var guestMatch = guestRegex.firstMatch(userInput);
      if (guestMatch != null) {
        newPreferences["guest_count"] = guestMatch.group(1);
      }
      
      // Update client if we found new preferences
      if (newPreferences.isNotEmpty) {
        await updateClientMemory(newPreferences);
      }
    }
  }
  
  Future<void> setCurrentClient(String clientId) async {
    _currentClient = await _storage.getClient(clientId);
    notifyListeners();
  }
  
  Future<void> updateClientMemory(Map<String, dynamic> newInfo) async {
    if (_currentClient != null) {
      _currentClient = _currentClient!.copyWith(
        preferences: {..._currentClient!.preferences, ...newInfo}
      );
      await _storage.updateClient(_currentClient!);
      notifyListeners();
    }
  }
  
  void resetConversation() {
    _conversationHistory = [];
    _lastResponse = '';
    notifyListeners();
  }
}