import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:orby/models/drink.dart';

class InstagramService {
  final String _accessToken = dotenv.env['INSTAGRAM_ACCESS_TOKEN'] ?? '';
  final String _businessId = dotenv.env['INSTAGRAM_BUSINESS_ID'] ?? '';
  
  Future<List<Drink>> getDrinkCatalog() async {
    // For MVP, we'll use a simplified approach with a predefined catalog
    // Later, this can be integrated with actual Instagram API
    
    // Mock data for initial development
    return [
      Drink(
        id: '1',
        name: 'Classic Margarita',
        description: 'Tequila, lime juice, and triple sec with a salt rim',
        imageUrl: 'https://images.unsplash.com/photo-1556855810-ac404aa91e85?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        price: 12.0,
        ingredients: ['Tequila', 'Lime Juice', 'Triple Sec', 'Salt'],
        tags: ['Classic', 'Tequila', 'Citrus'],
      ),
      Drink(
        id: '2',
        name: 'Spicy