import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:just_audio/just_audio.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;

class AudioService {
  final FlutterSoundRecorder _recorder = FlutterSoundRecorder();
  final AudioPlayer _player = AudioPlayer();
  final FlutterTts _tts = FlutterTts();
  final stt.SpeechToText _speech = stt.SpeechToText();
  
  String? _recordingPath;
  bool _isInitialized = false;
  
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    // Request microphone permission
    final status = await Permission.microphone.request();
    if (status != PermissionStatus.granted) {
      throw Exception('Microphone permission not granted');
    }
    
    // Initialize recorder
    await _recorder.openRecorder();
    
    // Initialize TTS
    await _tts.setLanguage('en-US');
    await _tts.setSpeechRate(0.5);
    await _tts.setVolume(1.0);
    await _tts.setPitch(1.0);
    
    // Initialize STT
    await _speech.initialize();
    
    _isInitialized = true;
  }
  
  Future<void> startRecording() async {
    if (!_isInitialized) await initialize();
    
    // Create temp file for recording
    final Directory tempDir = await getTemporaryDirectory();
    _recordingPath = '${tempDir.path}/recording.wav';
    
    // Start recording
    await _recorder.startRecorder(
      toFile: _recordingPath,
      codec: Codec.pcm16WAV,
    );
  }
  
  Future<String> stopRecording() async {
    if (!_recorder.isRecording) return '';
    
    // Stop recording
    await _recorder.stopRecorder();
    
    // Convert audio to text using Whisper API
    final transcription = await _speechToText();
    
    return transcription;
  }
  
  Future<String> _speechToText() async {
    if (_recordingPath == null) return '';
    
    // For development, use on-device STT
    if (await _speech.initialize()) {
      String recognizedText = '';
      await _speech.listen(
        onResult: (result) {
          recognizedText = result.recognizedWords;
        },
        listenFor: Duration(seconds: 30),
      );
      await Future.delayed(Duration(seconds: 5)); // Give time for processing
      return recognizedText;
    }
    
    // For production, use Whisper API
    try {
      final apiKey = dotenv.env['OPENAI_API_KEY'];
      final url = Uri.parse('https://api.openai.com/v1/audio/transcriptions');
      
      final request = http.MultipartRequest('POST', url)
        ..headers.addAll({
          'Authorization': 'Bearer $apiKey',
        })
        ..fields['model'] = 'whisper-1'
        ..files.add(await http.MultipartFile.fromPath(
          'file', 
          _recordingPath!,
        ));
      
      final response = await request.send();
      final responseBody = await response.stream.bytesToString();
      
      if (response.statusCode == 200) {
        final data = jsonDecode(responseBody);
        return data['text'];
      } else {
        throw Exception('Failed to transcribe audio: $responseBody');
      }
    } catch (e) {
      print('Error in speech to text: $e');
      return '';
    }
  }
  
  Future<void> playAudio(String text) async {
    // For development, use on-device TTS
    await _tts.speak(text);
    
    // For production, use ElevenLabs
    // await _playWithElevenLabs(text);
  }
  
  Future<void> _playWithElevenLabs(String text) async {
    try {
      final apiKey = dotenv.env['ELEVENLABS_API_KEY'];
      final voiceId = 'EXAVITQu4vr4xnSDxMaL'; // Example voice ID
      
      final url = Uri.parse('https://api.elevenlabs.io/v1/text-to-speech/$voiceId');
      
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'xi-api-key': apiKey!,
        },
        body: jsonEncode({
          'text': text,
          'model_id': 'eleven_monolingual_v1',
          'voice_settings': {
            'stability': 0.5,
            'similarity_boost': 0.5,
          }
        }),
      );
      
      if (response.statusCode == 200) {
        // Save audio to temp file
        final Directory tempDir = await getTemporaryDirectory();
        final String filePath = '${tempDir.path}/response.mp3';
        
        final File file = File(filePath);
        await file.writeAsBytes(response.bodyBytes);
        
        // Play audio
        await _player.setFilePath(filePath);
        await _player.play();
      } else {
        throw Exception('Failed to generate speech: ${response.body}');
      }
    } catch (e) {
      print('Error in text to speech: $e');
      // Fallback to device TTS
      await _tts.speak(text);
    }
  }
  
  Future<void> dispose() async {
    await _recorder.closeRecorder();
    await _player.dispose();
    await _tts.stop();
  }
}