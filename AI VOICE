// Replace the script section with this updated version
<script>
    let mediaRecorder;
    let audioChunks = [];
    const orb = document.querySelector('.orb');
    const statusText = document.querySelector('.status-text');
    let isRecording = false;

    // Initialize text-to-speech
    const synth = window.speechSynthesis;

    // API endpoint (replace with your actual API endpoint)
    const API_ENDPOINT = 'https://your-ai-service-endpoint.com/process-audio';

    // Request microphone access when page loads
    window.addEventListener('load', async () => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            setupMediaRecorder(stream);
            statusText.textContent = 'Click to speak';
        } catch (err) {
            console.error('Error accessing microphone:', err);
            statusText.textContent = 'Microphone access denied';
            orb.style.opacity = '0.5';
            orb.style.cursor = 'not-allowed';
        }
    });

    function setupMediaRecorder(stream) {
        mediaRecorder = new MediaRecorder(stream);

        mediaRecorder.ondataavailable = (event) => {
            audioChunks.push(event.data);
        };

        mediaRecorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
            await processAudioAndGetResponse(audioBlob);
            audioChunks = [];
        };
    }

    async function processAudioAndGetResponse(audioBlob) {
        statusText.textContent = 'Processing...';
        orb.classList.add('responding');

        try {
            const formData = new FormData();
            formData.append('audio', audioBlob);

            const response = await fetch(API_ENDPOINT, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error('API request failed');
            }

            const data = await response.json();
            const aiResponse = data.response || "Hello! I'm your AI assistant. How can I help you today?";
            speakResponse(aiResponse);

        } catch (error) {
            console.error('Error processing audio:', error);
            speakResponse("I'm sorry, I couldn't process that. Please try again.");
        }
    }

    function speakResponse(text) {
        statusText.textContent = 'AI is speaking...';
        
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.onend = () => {
            orb.classList.remove('responding');
            statusText.textContent = 'Click to speak';
        };
        
        synth.speak(utterance);
    }

    orb.addEventListener('click', () => {
        // If microphone access was denied, don't proceed
        if (!mediaRecorder) {
            statusText.textContent = 'Microphone access required';
            return;
        }

        // Don't allow new recording while AI is responding
        if (orb.classList.contains('responding')) {
            return;
        }

        if (!isRecording) {
            // Start recording
            mediaRecorder.start();
            isRecording = true;
            orb.classList.add('recording');
            statusText.textContent = 'Listening...';
            
            // Automatically stop recording after 5 seconds
            setTimeout(() => {
                if (isRecording) {
                    mediaRecorder.stop();
                    isRecording = false;
                    orb.classList.remove('recording');
                }
            }, 5000);
        } else {
            // Stop recording
            mediaRecorder.stop();
            isRecording = false;
            orb.classList.remove('recording');
        }
    });

    // Handle keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.code === 'Space') {
            e.preventDefault();
            orb.click();
        }
    });
</script>